{"name": "cdti-partners", "version": "1.0.0", "private": true, "engines": {"node": "22.14.0", "npm": "10.9.2"}, "scripts": {"checkVersion": "check-node-version --node 22.14.0 --npm 10.9.2", "start": "npm run checkVersion && rspack serve --config rspack/rspack.dev.ts --env isProdApp=false", "buildDev": "rspack build --config rspack/rspack.prod.ts --env isProdApp=false", "buildProd": "rspack build --config rspack/rspack.prod.ts --env isProdApp=true", "lint": "eslint --ext .ts,.tsx,.js,.jsx src --fix", "lint-test": "npx eslint src --fix --no-cache --max-warnings=0", "prettier": "prettier --write ./src", "prepare": "husky"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{jsx,js,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/react-fontawesome": "^3.0.1", "antd": "^4.24.16", "axios": "^1.9.0", "classnames": "^2.2.6", "effector": "^22.5.0", "effector-react": "^22.4.0", "i18next": "^22.4.14", "i18next-browser-languagedetector": "^7.0.1", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^12.2.0", "react-input-mask": "^2.0.4", "react-pdf": "^10.1.0", "react-refresh": "^0.14.0", "react-to-print": "^3.1.1"}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.5%", "last 2 versions", "not dead", "not IE 11", "not op_mini all"], "devDependencies": {"@rspack/cli": "^1.5.2", "@rspack/core": "^1.5.2", "@rspack/dev-server": "^1.1.4", "@rspack/plugin-react-refresh": "^1.5.0", "@svgr/webpack": "^8.1.0", "@trivago/prettier-plugin-sort-imports": "^4.1.1", "@types/js-cookie": "^3.0.2", "@types/lodash": "^4.17.13", "@types/ramda": "^0.28.15", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@types/react-input-mask": "^3.0.6", "@types/react-router-dom": "^5.3.3", "@types/webpack-bundle-analyzer": "^4.7.0", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "autoprefixer": "^9.7.2", "check-node-version": "^4.2.1", "core-js": "^3.6.5", "css-loader": "^6.7.1", "css-mqpacker": "^7.0.0", "cssnano": "^4.1.10", "eslint": "^7.32.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.29.3", "eslint-plugin-react-hooks": "^4.3.0", "husky": "^9.1.7", "less": "^4.1.3", "less-loader": "^11.0.0", "lint-staged": "^15.2.10", "mini-css-extract-plugin": "^2.6.1", "postcss": "^8.4.19", "postcss-loader": "^7.0.1", "postcss-preset-env": "^7.8.0", "prettier": "^2.7.1", "query-string": "^7.1.1", "ramda": "^0.28.0", "react-router-dom": "^5.3.3", "sass": "1.77.6", "sass-loader": "^13.0.2", "style-loader": "^3.3.1", "ts-loader": "^9.3.1", "ts-node": "^10.9.2", "typescript": "^4.7.4", "webpack-bundle-analyzer": "^4.10.2"}}