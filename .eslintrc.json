{"env": {"browser": true, "es2020": true, "jest": true}, "extends": ["plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["react", "@typescript-eslint", "prettier"], "rules": {"react/jsx-no-undef": "error", "react/no-unescaped-entities": 0, "no-trailing-spaces": "error", "react/prop-types": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off", "react/display-name": "off", "react/jsx-key": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/ban-types": ["warn", {"extendDefaults": true, "types": {"{}": false}}], "prettier/prettier": ["error", {"endOfLine": "auto"}]}}