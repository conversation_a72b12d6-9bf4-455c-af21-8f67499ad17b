{"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>h", "create": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "O'chirish", "download": "<PERSON><PERSON><PERSON>", "upload": "Yuklash", "save": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON>", "cancel": "Bekor qilish", "accept": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON> etish", "connect": "<PERSON><PERSON><PERSON>", "register": "<PERSON>o'yxa<PERSON><PERSON> o'tish", "send": "Yuborish", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON> et<PERSON><PERSON>", "confirm": "Tasdiqlash", "activate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ready": "Tayyor", "updateStatus": "<PERSON><PERSON>", "uploadFile": "<PERSON><PERSON><PERSON>", "sendSms": "SMS yuborish", "sendSmsConfirm": "SMS-tas<PERSON><PERSON><PERSON> yub<PERSON>h", "registerApay": "<PERSON><PERSON><PERSON><PERSON> ro'yxa<PERSON><PERSON> o'tish", "addBank": "Bank qo'shish", "deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkPaymentStatus": "To'lov <PERSON><PERSON> tekshirish", "downloadApplication": "<PERSON><PERSON><PERSON> o<PERSON>", "uploadApplication": "<PERSON><PERSON><PERSON> yuk<PERSON>", "payByApay": "A<PERSON>y or<PERSON><PERSON> to'lash", "uploadDoc": "<PERSON><PERSON><PERSON><PERSON><PERSON> yuk<PERSON>", "selectFile": "<PERSON><PERSON><PERSON>", "editBank": "<PERSON><PERSON>", "updateXfileStatus": "<PERSON>-<PERSON><PERSON>l <PERSON><PERSON> ya<PERSON>", "paymentRevert": "<PERSON><PERSON>lo<PERSON><PERSON> q<PERSON>ish", "revert": "<PERSON><PERSON><PERSON><PERSON>", "checkPayment": "<PERSON><PERSON><PERSON><PERSON><PERSON> teks<PERSON>", "changeStatus": "<PERSON><PERSON>", "changeKkmModel": "KKM model<PERSON>", "newOrder": "<PERSON><PERSON>", "newOneTimeOrder": "<PERSON><PERSON> yagona buyurtma", "newLeasingOrder": "Yangi kredit buy<PERSON>", "order": "<PERSON><PERSON><PERSON> berish", "createSpecification": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "acceptAct": "<PERSON><PERSON><PERSON> qabul qilish", "changeStatusForPendingAttachDoc": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ul<PERSON> k<PERSON>' <PERSON><PERSON>", "addCounterContract": "<PERSON><PERSON><PERSON> s<PERSON><PERSON>ma qo'shish", "updateCounterContract": "<PERSON><PERSON><PERSON> ya<PERSON>", "newLetter": "<PERSON><PERSON> xat", "editGuaranteeLetter": "<PERSON><PERSON><PERSON> ta<PERSON>", "newGuaranteeLetter": "<PERSON><PERSON>", "newReturnAct": "<PERSON><PERSON> qaytish akti", "addAct": "<PERSON><PERSON><PERSON> qo'shish", "addReturnAct": "<PERSON><PERSON><PERSON><PERSON> aktini qo'shish", "changeIndividualDocStatus": "Jismoniy huj<PERSON><PERSON>", "addFolder": "<PERSON><PERSON><PERSON> qo'shish", "editFolder": "<PERSON><PERSON><PERSON><PERSON>", "editDocName": "<PERSON><PERSON><PERSON><PERSON> nomini ta<PERSON>", "ofdSync": "NITS bilan sinxronizatsiya", "temporaryDeviceReplacement": "Vaqtinchalik KKMni almashtirish", "addCase": "<PERSON><PERSON><PERSON><PERSON> qo'shish", "editCase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addFile": "<PERSON><PERSON> qo'shish", "createContract": "<PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "quickCustomerCreation": "Mijozni tez yaratish", "changeSaleType": "Sotuv turini o'<PERSON>", "changePaymentStatus": "To'lov <PERSON><PERSON> o'<PERSON><PERSON><PERSON>sh", "sign": "<PERSON><PERSON><PERSON><PERSON>", "sendAggAgain": "<PERSON><PERSON><PERSON>", "registerSmartpos": "Smartposni ro'yxa<PERSON><PERSON> o't<PERSON>sh", "gotIt": "<PERSON><PERSON><PERSON><PERSON>", "go": "Batafsil", "showMore": "<PERSON><PERSON>proq ko'rsatish", "markAllAsRead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> deb belgilash", "changePassword": "<PERSON><PERSON><PERSON>", "resend": "<PERSON><PERSON><PERSON> qayta j<PERSON>'natish", "back": "Orqaga", "addKkm": "KKM qo'shish", "addFm": "FM qo'shish", "more": "Batafsil"}