{"branch": "Filial", "department": "<PERSON>'lim", "customers": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "tt2": "<PERSON><PERSON><PERSON>", "tin": "STIR", "pinfl": "JSHSHIR", "phone": "Telefon", "phoneNum": "Telefon raqami", "additionalPhoneNum": "Q<PERSON>'s<PERSON><PERSON> telefon raqami", "activityType": "Faoliyat turi", "employee": "<PERSON><PERSON><PERSON>", "employees": "Xodimlar", "status": "<PERSON><PERSON><PERSON>", "operatorStatus": "Operator holati", "registrationApay": "A-Pay <PERSON>'y<PERSON><PERSON>", "companyBrand": "<PERSON><PERSON><PERSON>", "companyInfo": "Kompaniya ma'lumoti", "balance": "Balans", "businessType": "Kompaniya turi", "companyName": "Komp<PERSON><PERSON> nomi", "title": "<PERSON><PERSON>", "vat": "QQS", "address": "Man<PERSON><PERSON>", "city": "<PERSON><PERSON>", "region": "Viloyat", "district": "<PERSON><PERSON>", "neighborhood": "Mahalla", "street": "<PERSON><PERSON>cha", "houseNum": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "house": "U<PERSON>", "apartmentNum": "<PERSON><PERSON><PERSON><PERSON> raqami", "apartment": "<PERSON><PERSON><PERSON><PERSON>", "ownerName": "Egasining ismi", "ownerLastName": "Egasining famili<PERSON>i", "ownerPatronymic": "Egasining otasining ismi", "directorName": "Direktorning ismi", "directorLastName": "Direktorning familiyasi", "directorPatronymic": "Direktorning otasining ismi", "accountantName": "<PERSON>uxgalterning ismi", "accountantLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON> famili<PERSON>i", "accountantPatronymic": "Buxgalterning otasining ismi", "appType": "<PERSON><PERSON> turi", "profile": "Foydalanuv<PERSON>ing profili", "customer": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "smartposData": "<PERSON><PERSON>s ma'lumoti", "payment": "To'lov", "allServices": "<PERSON><PERSON>", "connectedServices": "<PERSON><PERSON><PERSON>", "latitude": "<PERSON><PERSON><PERSON>", "longitude": "Uzunlik", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "patronymic": "<PERSON><PERSON><PERSON> ismi", "fullName": "To'liq ism", "reason": "Sababi", "activityType2": "Faoliyat turi", "cadastreNum": "<PERSON><PERSON><PERSON>", "creationDate": "Yaratilgan sana", "currentPhoneNumber": "<PERSON><PERSON><PERSON> telefon raqami", "customerId": "Mijoz ID", "deviceId": "NKM ID", "description": "<PERSON><PERSON><PERSON><PERSON>", "fileUrl": "<PERSON><PERSON>", "fiscalModuleSerial": "FMning seriya raqami", "lossFiscalModule": "<PERSON>skal Modulning Yo<PERSON>", "newKkm": "Yangi NKM", "newFm": "Yangi FM", "newSerialNumber": "<PERSON><PERSON> seriya raqami", "deviceSerialNumber": "NKM seriya raqami", "fmSerialNumber": "FM seriya raqami", "newFiscalModuleSerial": "Yangi <PERSON>ning seriya raqami", "accountNumber": "<PERSON><PERSON> raq<PERSON>", "oked": "OKED", "mfo": "MFO", "bankName": "Bank nomi", "tariffName": "<PERSON><PERSON><PERSON>", "newTariffName": "<PERSON><PERSON> tarif rejasi", "newActivityType": "Yangi Faoliyat turi", "tariff": "<PERSON><PERSON><PERSON>", "templateType": "Shablon turi", "chat": "Cha<PERSON>", "yes": "Ha", "no": "Yo'q", "changedData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma'lumotlari", "price": "Narxi", "bonus": "Bonus", "total": "<PERSON><PERSON>", "verificationCode": "<PERSON><PERSON><PERSON><PERSON><PERSON> kodi", "loginPhone": "<PERSON><PERSON><PERSON> (telefon)", "pan": "<PERSON><PERSON> raqami", "expiryDate": "<PERSON><PERSON> q<PERSON>h muddati", "transactionAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mi<PERSON>i", "smsCode": "SMS kodi", "prepayment": "<PERSON><PERSON><PERSON> to'lov", "credit": "Kredit", "debit": "Debet", "number": "<PERSON><PERSON><PERSON>", "transfer": "o't<PERSON><PERSON>sh", "date": "<PERSON><PERSON>", "dates": "<PERSON><PERSON><PERSON>", "comment": "<PERSON><PERSON><PERSON><PERSON>", "serviceType": "Xizmat turi", "ofdStatus": "NITS holati", "2dScanner": "2D skaner", "ownProperty": "<PERSON><PERSON><PERSON><PERSON><PERSON> mulk", "rent": "<PERSON><PERSON><PERSON>", "contractNum": "<PERSON><PERSON><PERSON><PERSON> raqami", "rentPeriod": "<PERSON><PERSON><PERSON>", "showOnMap": "<PERSON><PERSON><PERSON><PERSON> ko'rsating", "tts": "<PERSON><PERSON><PERSON>", "tt": "<PERSON><PERSON><PERSON>", "bank": "Bank", "rs": "<PERSON><PERSON> raq<PERSON>", "notSetted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manager": "<PERSON><PERSON><PERSON>", "acquiringBank": "Ekvayring banki", "paymentType": "To'lov turi", "receipt": "<PERSON><PERSON>", "fiscalizationDate": "Fiskalizatsiya sanasi", "ofdDoc": "NITS hujjati", "applicationAr": "Ariza", "template": "S<PERSON><PERSON>", "registrationType": "<PERSON>o'yxa<PERSON><PERSON> o'tish turi", "bill": "<PERSON><PERSON>", "terminalModel": "Terminal modeli", "serialNumber": "<PERSON><PERSON> raqami", "contractOffer": "Shart<PERSON>ma ", "leasingContract": "Bo'lib to'lash s<PERSON><PERSON><PERSON>i", "tradeInContract": "Trade-in shartnomasi", "primeContract": "Prime s<PERSON><PERSON><PERSON><PERSON>", "rentActs": "<PERSON><PERSON><PERSON>", "accessory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "accessories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generateTemplate": "<PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "docType": "<PERSON><PERSON><PERSON><PERSON> turi", "doc": "<PERSON><PERSON><PERSON><PERSON>", "fiscalization": "Fiskalizatsiya ", "areYouSure": "Ishonchingiz komilmi?", "yourBalance": "Sizning balansingiz", "model": "Model", "only": "<PERSON>aq<PERSON>", "position": "Lavozim", "type": "<PERSON><PERSON>", "size": "  <PERSON><PERSON><PERSON><PERSON><PERSON>", "createdBy": "<PERSON> ya<PERSON>", "company": "Kompaniya", "hideDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> ko'rish", "servicePrice": "<PERSON><PERSON><PERSON> narxi", "value": "<PERSON><PERSON><PERSON>", "calcPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmSms": "SMS kodini tasdiqlang", "service": "Xizmat", "serviceType2": "Xizmat turi", "amount": "Narxi", "activationDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sanasi", "startDate": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>i", "endDate": "<PERSON><PERSON><PERSON> sanasi", "dateStart": "<PERSON><PERSON>", "dateEnd": "<PERSON><PERSON> oxiri", "nextPayment": "Keyingi to'lov", "invoicePayment": "To'lov hisobi", "invoicePayments": "To'lov hisoblar", "recurring": "Takrorlanadigan", "singleService": "Bir martalik", "order": "<PERSON><PERSON><PERSON>", "details": "Ta<PERSON><PERSON>tl<PERSON>", "publicOffer": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "invoices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invoice": "Hisob-fak<PERSON>", "original": "<PERSON><PERSON> nusxasi", "uploaded": "Yuklangan", "amountPrice": "<PERSON><PERSON><PERSON><PERSON>", "invoiceDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sanasi", "xFileStatus": "x-file Holati", "UZS": "UZS", "branchName": "Filial nomi", "allowForCtsBranches": "TXKM filiallari uchun kirish", "additionalPhone": "<PERSON><PERSON>'s<PERSON>cha telefon", "mainBak": "Asosiy bank", "customerTin": "Mijozning STIR", "contractType": "Shartnoma turi", "deviceType": "NKM turi", "service2": "Xizmat", "detailing": "Ta<PERSON><PERSON>tl<PERSON>", "oneTime": "Bir martalik", "poa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "docNum": "<PERSON><PERSON><PERSON><PERSON>", "specificationNumber": "To'lov hisobning raqami", "file": "<PERSON><PERSON>", "resultTotal": "<PERSON><PERSON>", "creator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastModifierFullName": "<PERSON>", "passportSeries": "Pasport seriya", "passportNumber": "Pasport raqami", "statement": "<PERSON><PERSON><PERSON>", "payments": "<PERSON>'lovlar", "purchaseType": "<PERSON><PERSON><PERSON> olish turi", "tariffType": "<PERSON><PERSON><PERSON> turi", "trialPeriod": "<PERSON><PERSON>", "serviceAmount": "Xizmat narxi", "connectService": "<PERSON><PERSON><PERSON><PERSON>", "map": "<PERSON><PERSON><PERSON>", "payByQr": "QR-kod orqali to'lov", "payByCard": "<PERSON>rta orqali to'lov", "or": "yoki", "active": "Faol", "inActive": "Faol emas", "st": "ko'cha", "debt": "Qarz", "quantity": "<PERSON><PERSON><PERSON><PERSON>", "example": "Misol", "companyTin": "Kompaniya STIRi", "caseConfirmStatus": "<PERSON> qabul qilish holati", "fmArrivalStatus": "FM kelish holati", "fmArrivalDate": "FM kelish sanasi", "operator": "Operator", "caseType": "Keys turi", "caseId": "Keys ID", "fmCaseCreatedDate": "Keys kabul qlingan sanasi", "registrationDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o'tilgan sanasi", "kkmStatus": "NKM holati", "noData": "Ma'lumotlar yo'q", "apayFmPayment": "FM uchun A-pay to'lov", "director": "Direktor", "directorPhone": "Direktorning telefon raqami", "modelKkm": "NKM model", "paymentStatus": "To'lov holati", "additionalInfo": "<PERSON><PERSON><PERSON>s<PERSON>cha ma'lumot", "newBuyOrder": "<PERSON><PERSON>", "showBalance": "Balansni ko'rsatish", "agreement": "Shart<PERSON>ma", "uploadedDocument": "Yuklangan hujjat", "attachedDocument": "Biriktiri<PERSON><PERSON> hujjat", "contract": "Shart<PERSON>ma", "tariffForLeasing": "Bo'lib to'lash tarifi", "before": "oldin", "from": "dan", "paymentDate": "To'lov sanasi", "payment2": "To'lov", "balance2": "Qoldiq", "guarantee": "<PERSON><PERSON><PERSON>", "generatePoa": "<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "issueBy": "<PERSON> be<PERSON>", "issueDate": "Pasport berilgan sana", "poaNum": "<PERSON><PERSON><PERSON><PERSON><PERSON> raqami", "noInvoice": "<PERSON><PERSON>-faktura yo'q", "guaranteeLetter": "<PERSON><PERSON><PERSON>", "act": "Akt", "actFile": "Akt fayli", "invoiceStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>i", "poaFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> fayli", "devicePhotos": "Qurilmaning fotosuratlari", "letterOfWithdrawal": "<PERSON><PERSON>'<PERSON><PERSON><PERSON><PERSON> o'tish xati", "manufacturer": "<PERSON><PERSON><PERSON>", "counterContract": "<PERSON><PERSON><PERSON>", "primeContract2": "Prime shartnoma", "photo": "Rasm", "photos": "<PERSON><PERSON><PERSON>", "letter": "Xat", "manufacturerName": "<PERSON><PERSON><PERSON> nomi", "guaranteeLetters": "<PERSON><PERSON><PERSON>", "rentAgreement": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>i", "offer": "<PERSON><PERSON><PERSON>", "accessoryAgreement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cdtiRent": "CDTI Ijara ", "closedDate": "Yo<PERSON>lish sanasi", "annotation": "Annotatsiya", "sendDate": "Yuborilgan sana", "roles": "<PERSON><PERSON>", "phones": "Telefon(lar)", "emails": "<PERSON>chta(lar)", "searchById": "<PERSON> orqali qidirish", "messagesLoading": "<PERSON><PERSON><PERSON><PERSON> yuk<PERSON>", "lossFm": "FM yo'qolishi", "fmIsLoss": "FM yo'qolgan", "fmNotLoss": "FM yo'qolmagan", "changeOn": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeType": "O'zgarish turi", "attachedFile": "Biriktirilgan fayl", "check": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Harakatlar", "currentDirectorFullName": "Direktorning hozirgi ismi", "activityTypeCategory": "Faoliyat turi to<PERSON>asi", "ttName": "<PERSON><PERSON><PERSON> nomi", "businessType2": "<PERSON><PERSON><PERSON><PERSON> shakli", "templates": "<PERSON><PERSON><PERSON><PERSON>", "connectStatus": "<PERSON><PERSON><PERSON>", "bt": "MS", "bankType": "Bank turi", "docStatus": "<PERSON><PERSON><PERSON><PERSON>", "information": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastChanges": "<PERSON><PERSON><PERSON><PERSON>", "caseAcceptDate": "<PERSON> qabul qilish sanasi", "period": "Davr", "firstPayment": "  <PERSON><PERSON><PERSON><PERSON> to'lov", "registry": "Reestr", "male": "Erkak", "female": "<PERSON><PERSON><PERSON>", "birthDate": "<PERSON><PERSON>'ilgan sana", "relationship": "<PERSON> bo'ladi", "gender": "<PERSON><PERSON>", "age": "<PERSON><PERSON>", "services": "Xizmatlar", "tariffs": "Tariflar", "notSpecified": "Ko'rsatilmagan", "reports": "Hisobotlar", "family": "Oila", "timeslot": "<PERSON>h tartibi", "otherReason": "<PERSON><PERSON><PERSON> sabab", "pageNotFound": "<PERSON><PERSON><PERSON>", "codeExpiration": "<PERSON><PERSON><PERSON> amal qilish muddati", "authorization": "Avtorizatsiya", "password": "<PERSON><PERSON>", "uzbekistan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "additional": "qo`shimcha", "login": "<PERSON><PERSON>", "activationCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kodi", "returnActs": "<PERSON><PERSON><PERSON><PERSON>", "tradeInAgreement": "Trade-In Shartnoma", "acts": "Aktlar", "fm": "FM", "kkm": "NKM", "fmAndKkm": "NKM | FM", "buyOrders": "<PERSON><PERSON><PERSON>", "serviceOrders": "<PERSON>z<PERSON>", "oneTimeOrders": "Bir martalik xizmatlar", "contracts": "Shartnomalar", "leasingContracts": "Bo'lib to'lash <PERSON><PERSON><PERSON><PERSON>", "primeContracts": "Prime Shart<PERSON>ma", "tradeInContracts": "Trade-in Shartnoma", "lettersOfGuarantee": "<PERSON><PERSON><PERSON>", "rentContracts": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>i", "accessoriesContracts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smartposContracts": "CDTI Ijara", "documents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applications": "<PERSON><PERSON><PERSON>", "case": "Keys", "cases": "<PERSON><PERSON>", "humoConnect": "HUMO Arizalar", "fmReports": "FM Hisobotlar", "servicesSTS": "STS xizmatlari", "debetOrCredit": "Debit/Kredit", "invalidPhoneNumber": "Noto'g'ri telefon raqam", "changePaymentQuoteType": "Sotuv turini o'<PERSON>", "changePaymentQuoteStatus": "To'lov ho<PERSON><PERSON> o'<PERSON>sh", "secretWord": "Kod so'zi", "primeActs": "Prime huj<PERSON><PERSON><PERSON>", "detailsActs": "Huj<PERSON>tl<PERSON>ning tafsilotlari", "detailsPayments": "A<PERSON><PERSON> to'lovi", "repairTickets": "NKM ta'mirlash | Chiptalar", "act_status": "Akt holati", "doc_flow_status": "<PERSON><PERSON><PERSON><PERSON>", "accept_date": "<PERSON><PERSON><PERSON> qilis<PERSON> sanasi", "contract_status": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>i", "document_status": "<PERSON><PERSON><PERSON><PERSON>", "company_type": "Kompaniya turi", "accept_date_from": "<PERSON><PERSON><PERSON> qilish sanasi dan", "accept_date_to": "<PERSON><PERSON><PERSON> qilish sanasi gacha", "notificationType": "Bildirishnoma turi", "unreadFirst": "<PERSON><PERSON><PERSON><PERSON> o'qilmagan xabarlar", "saleFormat": "Sotish formati", "today": "<PERSON><PERSON><PERSON>", "yesterday": "<PERSON><PERSON>", "dayAgo": "oldin", "invoiceNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> raqami", "userName": "<PERSON><PERSON>", "advancedFilter": "Kengaytirilgan filtr", "monitoring": "Monitoring", "changePassword": "<PERSON><PERSON><PERSON>", "requiredField": "<PERSON><PERSON><PERSON><PERSON>", "turnover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "device": "<PERSON><PERSON><PERSON>", "deviceTypeTerminal": "Terminal", "deviceTypeFiscal": "Fiskal", "caseDataChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma'lumotlari", "caseTemplates": "<PERSON><PERSON><PERSON><PERSON>", "caseDocuments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caseHistory": "<PERSON><PERSON>", "caseActivities": "Faoliyatlar", "field": "<PERSON><PERSON>", "oldValue": "<PERSON><PERSON> qiymat", "newValue": "<PERSON><PERSON> qi<PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON>", "docType2": "<PERSON><PERSON><PERSON><PERSON> turi", "minutes": "daq", "newCompanyName": "Yangi nom", "newDeviceSerialNumber": "Yangi KKM", "newPhoneNumber": "Yangi telefon raqami", "activityTypeName": "Yangi faoliyat turi", "oldManagerName": "<PERSON><PERSON>", "newManagerName": "<PERSON><PERSON>", "rentNumber": "<PERSON><PERSON><PERSON> r<PERSON>ami", "rentStartDate": "<PERSON><PERSON><PERSON>", "rentEndDate": "<PERSON><PERSON><PERSON>", "changeCtsType": "CTS o'z<PERSON>ishi", "agreementNumber": "<PERSON><PERSON><PERSON> raqami", "reasonName": "<PERSON><PERSON>", "typeName": "Murojaat turi", "fromTime": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>i", "toTime": "<PERSON><PERSON><PERSON> sanasi", "scannerName": "<PERSON><PERSON><PERSON>", "deviceModelName": "KKM modeli", "purchaseDate": "KKM sotib olish sanasi", "ofdStatus2": "KKM ning GNI da ro'yxatdan o'tish holati", "fiscalModuleSerialNumber": "FM seriya raqami", "merchantId": "Merchant ID", "terminalId": "Terminal ID", "activityTypeId": "Faoliyat turi", "branches": "Filial", "currentDate": "Yaratilgan sana", "districtId": "<PERSON><PERSON>", "regionId": "Viloyat", "vatId": "QQS", "customerBranchDevicesTariff": "<PERSON><PERSON><PERSON>", "paymentType2": "To'lov tizimi turi", "customerTin2": "Mijozning STIR/PINFL", "days": {"monday": "Dushanba", "tuesday": "Seshanba", "wednesday": "<PERSON><PERSON><PERSON><PERSON> ", "thursday": "Payshanba", "friday": "<PERSON><PERSON>", "saturday": "Shanba", "sunday": "<PERSON><PERSON><PERSON><PERSON>", "md": "<PERSON>", "tue": "Se", "wed": "Chor", "thu": "Pay", "fd": "<PERSON>", "sat": "<PERSON><PERSON>", "sun": "Yak"}, "declination": {"day": {"one": "kun", "two": "kun", "five": "kun"}, "week": {"one": "hafta", "two": "hafta", "five": "hafta"}, "month": {"one": "oy", "two": "oy", "five": "oy"}, "year": {"one": "yil", "two": "yil", "five": "yil"}}, "statuses": {"readyForConnect": "<PERSON><PERSON>shga tayyor", "inProgress": "Jarayonda", "pending": "<PERSON><PERSON><PERSON>", "deregistered": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON>", "connected": "<PERSON><PERSON><PERSON>", "notConnected": "Ulanmagan", "mistakeAdded": "Noto'g'ri qo'shilgan"}, "placeholders": {"searchByCaseId": "<PERSON> bo'yicha q<PERSON>sh", "select": "Tanlang", "selectAllTime": "<PERSON><PERSON> vaqt uchun", "selectToday": "<PERSON><PERSON><PERSON> uchun", "selectYesterday": "<PERSON><PERSON>", "selectForMonth": "<PERSON><PERSON> uchun", "selectActivityType": "<PERSON><PERSON><PERSON><PERSON><PERSON> tan<PERSON>", "selectActivityTypeParent": "<PERSON><PERSON><PERSON><PERSON><PERSON> sohasi to<PERSON>ni tanlang", "selectStatus": "<PERSON><PERSON><PERSON>", "selectContractStatus": "S<PERSON><PERSON><PERSON> holatini tan<PERSON>", "selectTariff": "<PERSON><PERSON><PERSON><PERSON>", "selectApayTariff": "A<PERSON>y tarifini tanlang", "selectEmployee": "<PERSON><PERSON><PERSON><PERSON>", "selectBank": "<PERSON><PERSON>", "selectBranch": "<PERSON><PERSON><PERSON> tan<PERSON>", "selectBusinessType": "Kompaniya turini tanlang", "selectType": "<PERSON><PERSON> tan<PERSON>", "selectServiceType": "<PERSON>z<PERSON> turini tanlang", "selectBankType": "Bank turini tanlang", "selectCompanyType": "Kompaniya turini tanlang", "selectAppType": "<PERSON><PERSON> turini tan<PERSON>", "selectRegistrationType": "<PERSON>o<PERSON>yxatga olish turini tanlang", "selectCompany": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectFiscalModule": "Soliq modulini tanlang", "selectCustomer": "<PERSON><PERSON><PERSON><PERSON>", "selectReason": "Sababni tanlang", "selectRegion": "Viloyatni tanlang", "selectDistrict": "<PERSON><PERSON><PERSON> tan<PERSON>", "selectIndDocStatus": "Jismoniy huj<PERSON>t holat<PERSON> tanlang", "selectSerialNumber": "<PERSON><PERSON> raqamini tanlang", "selectDocStatus": "<PERSON><PERSON><PERSON><PERSON>", "selectContract": "<PERSON><PERSON><PERSON><PERSON> tan<PERSON>", "selectGuaranteeLetter": "<PERSON><PERSON><PERSON> xatini tanlang", "selectModel": "<PERSON><PERSON>", "selectService": "<PERSON>z<PERSON><PERSON>", "selectService2": "<PERSON><PERSON><PERSON>", "selectPosition": "Lavozimni tanlang", "selectProducer": "<PERSON><PERSON><PERSON><PERSON> tanlang", "selectRentAct": "<PERSON><PERSON><PERSON> akt<PERSON> tan<PERSON>", "selectCustomerBranch": "<PERSON><PERSON><PERSON><PERSON>", "selectVat": "QQSni tanlang", "selectNeighborhood": "<PERSON><PERSON><PERSON> tanlang", "selectManager": "<PERSON><PERSON><PERSON><PERSON> tan<PERSON>", "selectDocType": "<PERSON><PERSON><PERSON><PERSON> turini tan<PERSON>", "selectKkm": "KKMni tanlang", "selectFm": "FMni tanlang", "selectPayment": "To‘lov usulini tanlang", "selectFolder": "<PERSON><PERSON><PERSON><PERSON> tan<PERSON>", "selectOfdStatus": "NITS holatini tanlang", "selectTariffName": "<PERSON><PERSON><PERSON>", "selectOperatorStatus": "Operator ho<PERSON><PERSON>", "selectEmployees": "<PERSON><PERSON><PERSON><PERSON><PERSON> tan<PERSON>", "selectFmReason": "FM sababini tanlang", "selectRelationship": "<PERSON> bo‘lishini tanlang", "selectGender": "<PERSON><PERSON><PERSON>", "selectTimeslot": "<PERSON><PERSON> vaq<PERSON>i tanlang", "selectDate": "<PERSON><PERSON>", "inputTitle": "<PERSON><PERSON> kiri<PERSON>", "inputCompanyBrand": "<PERSON><PERSON><PERSON> kiri<PERSON>", "inputStreet": "<PERSON><PERSON>chani kiriting", "inputHouseNum": "<PERSON><PERSON> r<PERSON><PERSON>ni kiri<PERSON>", "inputApartmentNum": "<PERSON><PERSON><PERSON><PERSON> raqamini kiri<PERSON>", "inputOwnerName": "Egasining ismini kiriting", "inputOwnerLastName": "Egasining famili<PERSON>ini kiriting", "inputOwnerPatronymic": "Egasining otasining ismini kiriting", "inputDirectorName": "Direktor nomini kiriting", "inputDirectorLastName": "Direktorning famili<PERSON><PERSON> kiriting", "inputDirectorPatronymic": "Direktorning otasining ismini kiriting", "inputAccountantName": "<PERSON><PERSON><PERSON><PERSON><PERSON> is<PERSON> kiriting", "inputAccountantLastName": "Buxgalterning famili<PERSON><PERSON> kiriting", "inputAccountantPatronymic": "Buxgalterning otasining ismini kiriting", "inputPhoneNum": "Telefon raqamini kiriting", "inputEmailOrNum": "Pochta yoki telefon raqamni kiriting", "inputVerificationCode": "<PERSON><PERSON><PERSON><PERSON><PERSON> kodini kiriting", "inputCode": "<PERSON>d kiriting", "inputAmount": "<PERSON><PERSON><PERSON> kiri<PERSON>", "inputCadastreNum": "<PERSON><PERSON><PERSON> r<PERSON><PERSON> kiri<PERSON>", "inputLatitude": "Kenglikni kiriting", "inputLongitude": "Uzunlikni kiriting", "inputBill": "<PERSON><PERSON> r<PERSON><PERSON>ni kiri<PERSON>", "inputReason": "Sababni kiriting", "inputQty": "<PERSON><PERSON><PERSON> k<PERSON>", "inputDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "inputAccessorySerialNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON> seriya raqamini kiriting", "inputTin": "INN kiriting", "inputKkmQty": "KKM miqdorini kiriting", "inputFullName": "F.I.O ni kiriting", "inputPassportSeries": "Pasport seriyasini kiriting", "inputPassportNum": "Pasport raqamini kiriting", "inputIssueBy": "<PERSON> be<PERSON><PERSON><PERSON> kiriting", "inputPosition": "Lavozimni kiriting", "inputPoaNum": "Ishonchnoma raqamini kiriting", "inputSerialNum": "<PERSON><PERSON> raqamini kiriting", "inputDeviceSerialNum": "KKM seriya raqamini kiriting", "inputAnnotation": "<PERSON><PERSON><PERSON><PERSON><PERSON> kiri<PERSON>", "inputMessage": "Xabar yozing", "inputLastName": "F<PERSON><PERSON>ya kiri<PERSON>", "inputName": "<PERSON><PERSON> kiri<PERSON>", "inputPatronymic": "O<PERSON><PERSON> ismini kiriting", "inputCompanyName": "Kompaniya nomini kiriting", "inputPassword": "<PERSON><PERSON> k<PERSON>", "inputConfirmPassword": "<PERSON><PERSON><PERSON>", "inputOnlyCountNum": "{{count}} ta r<PERSON><PERSON><PERSON> o<PERSON>", "inputOnlyNums": "<PERSON><PERSON><PERSON> raqam kiri<PERSON>", "inputData": "<PERSON><PERSON>l<PERSON><PERSON> kiriting", "selectBranches": "<PERSON><PERSON><PERSON><PERSON> tan<PERSON>", "selectTypes": "<PERSON><PERSON><PERSON><PERSON>", "selectSerialNumbers": "<PERSON><PERSON> raq<PERSON><PERSON><PERSON> tanlang", "kkmSerialNumber": "ККМ seriya raqami", "newDeviceType": "Uskunaning yangi tipi", "selectClientBank": "Mijoz bankini tanlang", "selectPaymentSystem": "To'lov tizimini tanlang", "selectPaymentType": "Sotuv turini tanlang", "selectPaymentQuoteStatuses": "To'lov holatini tanlang", "selectLegalStatus": "<PERSON><PERSON><PERSON> ho<PERSON> tan<PERSON>", "selectCustomerStatus": "<PERSON><PERSON><PERSON>", "selectChangeCtsType": "CTS o'zgarish turi", "selectLetterType": "Xatning turini tanlang", "selectLetterReason": "Xatning sababini tanlang", "leaveDeviceAsTerminal": "Qurilmani terminal sifatida qoldiring"}, "validations": {"wrongFormat": "Yaroqsiz format", "wrongTin": "Noto‘g‘ri STIR", "wrongPinfl": "Noto‘g‘ri PINFL", "wrongAccountNumber": "<PERSON><PERSON> raqami noto‘g‘ri!", "tin": "Qiymat 9 dan 14 gacha belgidan iborat bo‘lishi kerak.", "qty": "Qiymat 0 dan 3 gacha belgidan iborat bo‘lishi kerak.", "qtyCalc": "\"Miqdor\" maydoni qiymati xizmat narxiga ko‘paytiriladi", "onlyLat": "Faqat lotin harflariga ruxsat etiladi", "sms": "Qiymat 6 belgidan iborat bo‘lishi kerak.", "secretKey": "Qiymat 10 ta belgidan oshmasligi kerak", "notFilled": "<PERSON><PERSON> to‘ldiril<PERSON><PERSON>", "error": "Validatsiya xatosi", "noAttachedDoc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hujjat yo‘q", "noPhotos": "Surat<PERSON> yo‘q", "noCounterContract": "<PERSON>ntrak<PERSON> s<PERSON>i yo‘q", "noAct": "Akt yo‘q", "noActs": "A<PERSON><PERSON> yo‘q", "noCounterAct": "Kontrakt akti yo‘q", "numMissing": "<PERSON><PERSON><PERSON> mavjud emas", "notesMissing": "<PERSON><PERSON><PERSON><PERSON> yo‘q", "fmReportCheck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ba'<PERSON>i mod<PERSON><PERSON> r<PERSON> \"FM hisobotida\" yo‘q edi, yo‘q bo‘lganlari excel formatida qaytarildi", "onlySpecFileTypes": "Faqat {{type}} turidagi fayllarni yuklash mumkin", "bigSize": "<PERSON><PERSON> hajmi {{size}} Mb dan osh<PERSON>ligi kerak", "noFile": "<PERSON>l yo‘q"}}