{"sync": "Sinxronizatsiya muvaffaqiyatli o‘tdi", "customerAdded": "<PERSON><PERSON><PERSON> qo‘shildi", "customerVerified": "<PERSON><PERSON><PERSON> mu<PERSON>aq<PERSON><PERSON>i faoliyatga o'tdi", "activationSent": "Tas<PERSON><PERSON><PERSON> kod yuborildi", "codeVerified": "<PERSON><PERSON>", "successPayment": "To‘lov muvaffaqiyatli o‘tdi", "ttAdded": "<PERSON><PERSON><PERSON>u<PERSON> qo‘shildi", "ttUpdated": "<PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "ttConfirmed": "<PERSON><PERSON><PERSON>u<PERSON><PERSON> ta<PERSON>", "ttActivated": "<PERSON><PERSON><PERSON> nu<PERSON><PERSON> faoli<PERSON><PERSON>ga o'tdi", "ttRegisteredInApay": "<PERSON><PERSON><PERSON> nu<PERSON><PERSON>i Apayda muvaffaqiyatli ro‘yxatdan o‘tdi", "fileDeleted": "<PERSON><PERSON> <PERSON><PERSON>iri<PERSON>i", "success": "Muvaffaqiyatli", "actConfirmed": "Akt tasdiqlandi", "actRemoved": "<PERSON><PERSON> olib ta<PERSON>i", "paymentConfirmed": "To<PERSON>lov hisob-fak<PERSON><PERSON>i tasdi<PERSON>i", "paymentRemoved": "To<PERSON>lov hisob-fak<PERSON><PERSON>i olib tashladi", "fileUploaded": "<PERSON><PERSON>", "infoNotFilled": "<PERSON><PERSON><PERSON><PERSON> haqidagi ma'lumotlar yetarli emas", "cantGetPublicOfferData": "O<PERSON><PERSON><PERSON> taklif ma'lum<PERSON><PERSON><PERSON> olish imkoniyati yo‘q", "successSmsVerification": "SMS tasdiqlash muvaffaqiyatli o‘tdi", "statusUpdated": "Status o‘zgartirildi", "docUploaded": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> yuk<PERSON>i", "tariffUpdated": "<PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "tariffConnected": "<PERSON><PERSON><PERSON><PERSON>", "customerDataUpdated": "<PERSON><PERSON><PERSON> ma'lum<PERSON><PERSON>i yang<PERSON>", "bankUpdated": "Bank yangilandi", "bankAdded": "Bank qo‘shildi", "invoiceRejected": "Hisob-faktura rad etildi", "rowDeleted": "<PERSON><PERSON><PERSON> muva<PERSON><PERSON><PERSON><PERSON> o‘chirildi", "paymentReverted": "To<PERSON>lov qay<PERSON>di", "smartposSync": "SMARTPOS bilan sinxronizatsiya muvaffaqiyatli o‘tdi", "launcherSync": "LAUNCHER bilan sinxronizatsiya muvaffaqiyatli o‘tdi", "paymentPending": "To<PERSON>lov kutish holatida", "paymentCancelled": "To‘lov bekor qilindi", "paymentPaid": "To‘lov muvaffaqiyatli o‘tdi", "orderAdded": "<PERSON><PERSON><PERSON> qo‘shildi", "modelCreated": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "modelUpdated": "Model ya<PERSON><PERSON><PERSON>", "agreementConfirmed": "S<PERSON><PERSON><PERSON> ta<PERSON>i", "agreementAccepted": "Shartnoma qabul qilindi", "agreementRejected": "Shartnoma rad etildi", "agreementClosed": "<PERSON><PERSON><PERSON><PERSON> yopi<PERSON>i", "agreementAdded": "<PERSON><PERSON><PERSON><PERSON> qo‘shildi", "invoicePaymentCreated": "To<PERSON>lov hisob-fak<PERSON><PERSON> yarat<PERSON>di", "invoicePaymentConfirmed": "To<PERSON>lov hisob-fak<PERSON><PERSON> tasdi<PERSON>i", "invoicePaymentRejected": "To<PERSON>lov hisob-fakturasi rad etildi", "counterContractAdded": "<PERSON><PERSON><PERSON> s<PERSON> qo‘shildi", "counterContractUpdated": "<PERSON><PERSON><PERSON> s<PERSON> ya<PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON>", "guaranteeLetterCreated": "<PERSON><PERSON><PERSON>", "guaranteeLetterUpdated": "<PERSON><PERSON><PERSON> ya<PERSON>", "returnActAdded": "<PERSON><PERSON><PERSON><PERSON> akti qo‘shildi", "returnActAccepted": "<PERSON>ay<PERSON>ish akti qabul qilindi", "folderAdded": "<PERSON><PERSON><PERSON> qo‘shildi", "folderUpdated": "Pap<PERSON> yang<PERSON>", "addFolderIn": "( {{name}} ) ichida papka qo‘shish", "documentNameUpdated": "<PERSON><PERSON><PERSON><PERSON> nomi ya<PERSON>", "docDeleted": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "caseAdded": "<PERSON><PERSON><PERSON><PERSON> qo‘shildi", "caseUpdated": "Ke<PERSON><PERSON>h yang<PERSON>", "phoneIsUsed": "<PERSON><PERSON><PERSON> raqam allaq<PERSON> is<PERSON>", "phoneIsFree": "<PERSON><PERSON><PERSON> bo‘sh", "fileAdded": "<PERSON><PERSON> qo‘shildi", "timeslotApplied": "<PERSON><PERSON> jad<PERSON>i qo‘llanildi", "dependentAdded": "<PERSON>a a'zosi qo‘shildi", "dependentUpdated": "Oila a'zosi ya<PERSON>", "smsWithCodeSent": "Kodli SMS yuborildi", "smsSent": "SMS yuborildi", "smsConfirmSent": "SMS-ta<PERSON><PERSON><PERSON><PERSON> y<PERSON>i", "cantEdit": " <PERSON>z <PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "paymentQuoteTypeChanged": "Sotuv turi o'zgart<PERSON>ldi", "paymentQuoteStatusChanged": "To'lov holati o'<PERSON>i", "empty": "<PERSON><PERSON><PERSON><PERSON> yoq", "notifications": "Bildirishnomalar", "dataChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>i", "kkmAdded": "KKM qo's<PERSON><PERSON>"}