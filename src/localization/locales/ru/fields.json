{"branch": "Филиал", "department": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customers": "Клиенты", "logout": "Выход", "tt2": "ТТ", "tin": "ИНН", "pinfl": "ПИНФЛ", "phone": "Телефон", "phoneNum": "Номер телефона", "additionalPhoneNum": "Дополнительный номер телефона", "activityType": "Сфера деятельности", "employee": "Сотрудник", "employees": "Сотрудники", "status": "Статус", "operatorStatus": "Статус оператора", "registrationApay": "Регистрация A-Pay", "companyBrand": "Торговая марка", "companyInfo": "Информация о компании", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "businessType": "Вид компании", "companyName": "Название компании", "title": "Название", "vat": "НДС", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Город", "region": "Регион", "district": "Рай<PERSON>н", "neighborhood": "Маха<PERSON>ля", "street": "Улица", "houseNum": "Номер дома", "house": "Дом", "apartmentNum": "Номер квартиры", "apartment": "Квартира", "ownerName": "Имя владельца", "ownerLastName": "Фамилия владельца", "ownerPatronymic": "Отчество владельца", "directorName": "Имя директора", "directorLastName": "Фамилия директора", "directorPatronymic": "Отчество директора", "accountantName": "Имя бухгалтера", "accountantLastName": "Фамилия бухгалтера", "accountantPatronymic": "Отчество бухгалтера", "appType": "Тип приложения", "profile": "Профиль", "customer": "Кли<PERSON><PERSON>т", "search": "Поиск", "smartposData": "Данные Smartpos", "payment": "Оплата", "allServices": "Все услуги", "connectedServices": "Подключенные услуги", "latitude": "Шир<PERSON><PERSON>а", "longitude": "Долгота", "firstName": "Имя", "lastName": "Фамилия", "patronymic": "Отчество", "fullName": "ФИО", "reason": "Причина", "activityType2": "Вид деятельности", "cadastreNum": "Номер кадастра", "creationDate": "Дата создания", "currentPhoneNumber": "Текущий номер телефона", "customerId": "Клиент Ид", "deviceId": "ККМ Ид", "description": "Описание", "fileUrl": "Адрес файла", "fiscalModuleSerial": "Серийный номер Фискального Модуля", "lossFiscalModule": "Утеря Фискального Модуля", "newKkm": "Новый ККМ", "newFm": "Новый ФМ", "newSerialNumber": "Новый серийный номер", "deviceSerialNumber": "Серийный номер ККМ", "fmSerialNumber": "Серийный номер ФМ", "newFiscalModuleSerial": "Новый Серийный номер ФМ", "accountNumber": "Расчетный счет", "oked": "ОКЭД", "mfo": "МФО", "bankName": "Название банка", "tariffName": "Тарифный план", "newTariffName": "Новый тарифный план", "newActivityType": "Новый вид деятельности", "tariff": "Тар<PERSON><PERSON>", "templateType": "Тип шаблона", "chat": "Чат", "yes": "да", "no": "нет", "changedData": "Данные изменений", "price": "Цена", "bonus": "<PERSON>о<PERSON><PERSON><PERSON>", "total": "Всего", "verificationCode": "Код подтверждения", "loginPhone": "<PERSON><PERSON><PERSON><PERSON><PERSON> (телефон)", "pan": "Но<PERSON>ер карты", "expiryDate": "Срок действия", "transactionAmount": "Сумма перевода", "smsCode": "Код из CMC", "prepayment": "Предоплата", "credit": "Кредит", "debit": "<PERSON>е<PERSON><PERSON><PERSON>", "number": "Номер", "transfer": "Перевод", "date": "Дата", "dates": "Даты", "comment": "Комментарии", "serviceType": "Тип сервиса", "ofdStatus": "Статус НИЦ", "2dScanner": "2D сканер", "ownProperty": "Личное имущество", "rent": "Аренда", "contractNum": "Номер договора", "rentPeriod": "Период аренды", "showOnMap": "Указать на карте", "tts": "Торговые точки", "tt": "Торговая точка", "bank": "<PERSON><PERSON><PERSON><PERSON>", "rs": "Р. / С.", "notSetted": "Не установлен", "manager": "Мен<PERSON>д<PERSON><PERSON><PERSON>", "acquiringBank": "Банк эквайринга", "paymentType": "Тип оплаты", "receipt": "<PERSON><PERSON><PERSON>", "fiscalizationDate": "Дата фискализации", "ofdDoc": "Документ НИЦ", "applicationAr": "Заявление (Ариза)", "template": "Шабл<PERSON>н", "registrationType": "Тип регистрации", "bill": "Счет", "terminalModel": "Модель терминала", "serialNumber": "Серийный номер", "contractOffer": "Контракт/Оферта", "leasingContract": "Контракт на рассрочку", "tradeInContract": "Контракт Trade in", "primeContract": "Кон<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "rentActs": "Акты на аренду", "accessory": "Аксес<PERSON><PERSON><PERSON><PERSON>", "accessories": "Аксессуары", "generateTemplate": "Генерация шаблона", "docType": "Тип документа", "doc": "Документ", "fiscalization": "Фискализация", "areYouSure": "Вы уверены ?", "yourBalance": "<PERSON>а<PERSON> баланс", "model": "Модель", "only": "только", "position": "Должность", "type": "Тип", "size": "Размер", "createdBy": "Кем создан", "company": "Компания", "hideDescription": "Скрыть описание", "openDescription": "Посмотреть описание", "servicePrice": "Стоимость подключения", "value": "Значение", "calcPrice": "Рассчитать стоимость", "confirmSms": "Подтвердите смс-код", "service": "Услуга", "serviceType2": "Тип услуги", "amount": "Стоимость", "activationDate": "Дата активации", "startDate": "Начальная дата", "endDate": "Конечная дата", "dateStart": "Начало даты", "dateEnd": "Конец даты", "nextPayment": "Следующий платеж", "invoicePayment": "Счет на оплату", "invoicePayments": "Счета на оплату", "recurring": "Повторяющийся", "singleService": "Разовый", "order": "Зак<PERSON>з", "details": "Детали", "publicOffer": "Публичная оферта", "invoices": "Счет фактуры", "invoice": "Счет фактура", "original": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uploaded": "Загруженный", "amountPrice": "Сумма", "invoiceDate": "Дата счет-фактуры", "xFileStatus": "Статус x-file", "UZS": "сум", "branchName": "Название филиала", "allowForCtsBranches": "Доступ для филиалов ЦТО", "additionalPhone": "Дополнительный телефон", "mainBak": "Основной банк", "customerTin": "ИНН клиента", "contractType": "Тип договора", "deviceType": "Тип девайса", "service2": "Сервис", "detailing": "Детализация", "oneTime": "Единоразовые", "poa": "Доверенность", "docNum": "Номер документа", "specificationNumber": "Номер счета на оплату", "file": "<PERSON>а<PERSON><PERSON>", "resultTotal": "Итого", "creator": "Создал", "lastModifierFullName": "Кем изменено", "passportSeries": "Серия паспорта", "passportNumber": "Номер паспорта", "statement": "Состояние", "payments": "Оплаты", "purchaseType": "Тип покупки", "tariffType": "Тип тарифа", "trialPeriod": "Пробный период", "serviceAmount": "Стоимость услуги", "connectService": "Подключить услугу", "map": "Карта", "payByQr": "Оплата через QR-code", "payByCard": "Оплата через карту", "or": "или", "active": "Активный", "inActive": "Неактивный", "st": "ул.", "debt": "Долг", "quantity": "Количество", "example": "Пример", "companyTin": "ИНН компании", "caseConfirmStatus": "Статус принятие кейса", "fmArrivalStatus": "Статус прихода ФМ", "fmArrivalDate": "Дата прихода ФМ", "operator": "Оператор", "caseType": "<PERSON>и<PERSON> ке<PERSON>а", "caseId": "Кейс ID", "fmCaseCreatedDate": "Дата принятие кейса", "registrationDate": "Дата регистрации", "kkmStatus": "Статус ККМ", "noData": "Нет данных", "apayFmPayment": "A-pay оплата за фискальный модуль", "director": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "directorPhone": "Номер телефона директора", "modelKkm": "Модель ККМ", "paymentStatus": "Статус оплаты", "additionalInfo": "Дополнительная информация", "newBuyOrder": "Новый заказ на покупку", "showBalance": "Показать баланс", "agreement": "Договор", "uploadedDocument": "Загруженный документ", "attachedDocument": "Прикрепленный документ", "contract": "Контракт", "tariffForLeasing": "Тариф на рассрочку", "before": "до", "from": "от", "paymentDate": "Дата платежа", "payment2": "Пла<PERSON><PERSON><PERSON>", "balance2": "Остаток", "guarantee": "Гарантия", "generatePoa": "Генерация доверенности", "issueBy": "Кем выдан", "issueDate": "Дата выдачи паспорта", "poaNum": "Номер доверенности", "noInvoice": "Нет счет-фактуры", "guaranteeLetter": "Гарантийное письмо", "act": "<PERSON>кт", "actFile": "<PERSON>айл акта", "invoiceStatus": "Статус счет-фактуры", "poaFile": "Файл доверенности", "devicePhotos": "Фотографии аппарата", "letterOfWithdrawal": "Письмо о снятии", "manufacturer": "Производитель", "counterContract": "Контр договор", "primeContract2": "Дог<PERSON><PERSON><PERSON> Prime", "photo": "Фотография", "photos": "Фотографии", "letter": "Письмо", "manufacturerName": "Наименование производителя", "guaranteeLetters": "Гарантийные письма", "rentAgreement": "Договор на аренду", "offer": "Оферта", "accessoryAgreement": "Договор Аксессуар", "cdtiRent": "Аренда CDTI", "closedDate": "Дата закрытия", "annotation": "Аннотация", "sendDate": "Дата отправки", "roles": "Роли", "phones": "Телефон(ы)", "emails": "Почта(ы)", "searchById": "Поиск по ID", "messagesLoading": "Загрузка сообщений", "lossFm": "Утеря ФМ", "fmIsLoss": "ФМ утерян", "fmNotLoss": "ФМ не утерян", "changeOn": "Изменить на", "changeType": "Тип изменения", "attachedFile": "Прикрепленный файл", "check": "Проверить", "actions": "Действии", "currentDirectorFullName": "Текущее ФИО Директора", "activityTypeCategory": "Категория сферы деятельности", "ttName": "Название ТТ", "businessType2": "Форма собственности", "templates": "Шаблоны", "connectStatus": "Статус подключения", "bt": "ФС", "bankType": "Ти<PERSON> банка", "docStatus": "Статус документа", "information": "Информация", "lastChanges": "Последние изменения", "caseAcceptDate": "Дата принятия кейса", "period": "Период", "firstPayment": "Первоначальный взнос", "registry": "Реестр", "male": "Мужской", "female": "Женский", "birthDate": "Дата рождения", "relationship": "Кем приходится", "gender": "Пол", "age": "Возраст", "services": "Услуги", "tariffs": "Тарифы", "notSpecified": "Не указано", "reports": "Отчеты", "family": "Семья", "timeslot": "Режим работы", "otherReason": "Другая причина", "pageNotFound": "Страница не найдена", "codeExpiration": "Срок действия кода", "authorization": "Авторизация", "password": "Пароль", "uzbekistan": "Узбекистан", "additional": "Дополнительно", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activationCode": "<PERSON>од активации", "returnActs": "Акты возврата", "tradeInAgreement": "Договор Trade In", "acts": "А<PERSON><PERSON>ы", "fm": "ФМ", "kkm": "ККМ", "fmAndKkm": "ККМ | ФМ", "buyOrders": "Заказы на покупку", "serviceOrders": "Заказы на услуги", "oneTimeOrders": "Единоразовые услуги", "contracts": "Контракты", "leasingContracts": "Контракты на рассрочку", "primeContracts": "Контракты Prime", "tradeInContracts": "Контракты Trade in", "lettersOfGuarantee": "Гарантийные письма", "rentContracts": "Контракты на аренду", "accessoriesContracts": "Контракты аксессуаров", "smartposContracts": "Аренда CDTI", "documents": "Документы", "case": "<PERSON><PERSON><PERSON><PERSON>", "cases": "Кейсы", "humoConnect": "HUMO Заявки", "fmReports": "ФМ отчеты", "servicesSTS": "Услуги STS", "debetOrCredit": "Дебет/Кредит", "invalidPhoneNumber": "Некорректный номер телефона", "changePaymentQuoteType": "Сменить тип продажи", "changePaymentQuoteStatus": "Изменить статус оплаты", "secretWord": "Кодовое слово", "primeActs": "Акты Prime", "detailsActs": "Детализация актов", "detailsPayments": "Акты Рассрочка", "repairTickets": "Ремонты ККМ | Тикеты", "act_status": "Статус акта", "doc_flow_status": "Статус физ. документа", "accept_date": "Дата принятия", "contract_status": "Статус контракта", "document_status": "Статус документа", "company_type": "Тип компании", "accept_date_from": "Дата принятия от", "accept_date_to": "Дата принятия до", "notificationType": "Тип уведомления", "unreadFirst": "Не прочитанные в верху", "saleFormat": "Формат продажи", "today": "Сегодня", "yesterday": "Вчера", "dayAgo": "назад", "invoiceNumber": "Номер счет-фактуры", "userName": "Имя пользователя", "advancedFilter": "Расширенный фильтр", "monitoring": "Мони<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "applications": "Заявки", "changePassword": "Сменить пароль", "requiredField": "Обязательное поле", "turnover": "Оборот", "device": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceTypeTerminal": "Терминальный", "deviceTypeFiscal": "Фискалкальный", "caseDataChanges": "Данные изменений", "caseTemplates": "Шаблоны", "caseDocuments": "Документы", "caseHistory": "История", "caseActivities": "Активности", "field": "Поле", "oldValue": "Старое значение", "newValue": "Новое значение", "duration": "Продолжительность", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "docType2": "Тип документа", "minutes": "мин", "newCompanyName": "Новое название", "newDeviceSerialNumber": "Новый ККМ", "newPhoneNumber": "Новый номер телефона", "activityTypeName": "Новый вид деятельности", "oldManagerName": "Старый менеджер", "newManagerName": "Новый менеджер", "rentNumber": "Номер аренды", "rentStartDate": "Начало аренды", "rentEndDate": "Конец аренды", "changeCtsType": "Смена ЦТО", "agreementNumber": "Номер заказа", "reasonName": "Основание", "typeName": "Тип обращения", "fromTime": "Дата начала", "toTime": "Дата окончания", "scannerName": "Сканер", "deviceModelName": "Модель ККМ", "purchaseDate": "Дата покупки ККМ", "ofdStatus2": "Статус регистрации ККМ в ГНИ", "fiscalModuleSerialNumber": "Серийный номер ФМ", "merchantId": "Мерчант ИД", "terminalId": "Терминал ИД", "activityTypeId": "Вид деятельности", "branches": "Филиал", "currentDate": "Дата создания", "districtId": "Рай<PERSON>н", "regionId": "Регион", "vatId": "НДС", "customerBranchDevicesTariff": "Тар<PERSON><PERSON>", "paymentType2": "Тип платежной системы", "customerTin2": "ИНН/ПИНФЛ клиента", "days": {"monday": "Понедельник", "tuesday": "Вторник", "wednesday": "Среда", "thursday": "Четверг", "friday": "Пятница", "saturday": "Суббота", "sunday": "Воскресенье", "md": "ПН", "tue": "ВТ", "wed": "СР", "thu": "ЧТ", "fd": "ПТ", "sat": "СБ", "sun": "ВС"}, "declination": {"day": {"one": "день", "two": "дня", "five": "<PERSON><PERSON><PERSON><PERSON>"}, "week": {"one": "неделю", "two": "недели", "five": "недель"}, "month": {"one": "мес<PERSON><PERSON>", "two": "месяца", "five": "меся<PERSON>ев"}, "year": {"one": "год", "two": "года", "five": "лет"}}, "statuses": {"readyForConnect": "Готов к подключению", "inProgress": "В процессе", "pending": "В ожидании", "deregistered": "Снят с регистрации", "connected": "Подключен", "notConnected": "Не подключен", "mistakeAdded": "Ошибочно добавлен"}, "placeholders": {"searchByCaseId": "Поиск по ID кейса", "select": "Выберите", "selectAllTime": "За все время", "selectToday": "За сегодня", "selectYesterday": "За вчера", "selectForMonth": "За месяц", "selectActivityType": "Выберите сферу деятельности", "selectActivityTypeParent": "Выберите категорию сферы деятельности", "selectStatus": "Выберите статус", "selectContractStatus": "Выберите статус контракта", "selectTariff": "Выберите тариф", "selectApayTariff": "Выберите тариф <PERSON>y", "selectEmployee": "Выберите сотрудника", "selectBank": "Выберите банк", "selectBranch": "Выберите филиал", "selectBusinessType": "Выберите вид компании", "selectType": "Выберите тип", "selectServiceType": "Выберите тип услуги", "selectBankType": "Выберите тип банка", "selectCompanyType": "Выберите тип компании", "selectAppType": "Выберите тип приложения", "selectRegistrationType": "Выберите тип регистрации", "selectCompany": "Выберите компанию", "selectFiscalModule": "Выберите фискальный модуль", "selectCustomer": "Выберите клиента", "selectReason": "Выберите причину", "selectRegion": "Выберите регион", "selectDistrict": "Выберите район", "selectIndDocStatus": "Выберите статус физ. документа", "selectSerialNumber": "Выберите серийный номер", "selectDocStatus": "Выберите статус документа", "selectContract": "Выберите контракт", "selectGuaranteeLetter": "Выберите гарантийное письмо", "selectModel": "Выберите модель", "selectService": "Выберите услугу", "selectService2": "Выберите сервис", "selectPosition": "Выберите должность", "selectProducer": "Выберите производителя", "selectRentAct": "Выберите акт на аренду", "selectCustomerBranch": "Выберите торговую точку", "selectVat": "Выберите НДС", "selectNeighborhood": "Выберите махаллю", "selectManager": "Выберите менеджера", "selectDocType": "Выберите тип документа", "selectKkm": "Выберите ККМ", "selectFm": "Выберите ФМ", "selectPayment": "Выберите способ оплаты", "selectFolder": "Выберите папку", "selectOfdStatus": "Выберите статус НИЦ", "selectTariffName": "Выберите тарифный план", "selectOperatorStatus": "Выберите статус оператора", "selectEmployees": "Выберите сотрудников", "selectFmReason": "Выберите ФМ причину", "selectRelationship": "Выберите кем приходится", "selectGender": "Выберите пол", "selectTimeslot": "Выберите режим работы", "selectDate": "Выберите дату", "inputTitle": "Введите название", "inputCompanyBrand": "Введите торговую марку", "inputStreet": "Введите улицу", "inputHouseNum": "Введите номер дома", "inputApartmentNum": "Введите номер квартиры", "inputOwnerName": "Введите имя владельца", "inputOwnerLastName": "Введите фамилию владельца", "inputOwnerPatronymic": "Введите отчество владельца", "inputDirectorName": "Введите имя директора", "inputDirectorLastName": "Введите фамилию директора", "inputDirectorPatronymic": "Введите отчество директора", "inputAccountantName": "Введите имя бухгалтера", "inputAccountantLastName": "Введите фамилию бухгалтера", "inputAccountantPatronymic": "Введите отчество бухгалтера", "inputPhoneNum": "Введите номер телефона", "inputEmailOrNum": "Введите почту или номер телефона", "inputVerificationCode": "Введите код подтверждения", "inputCode": "Введите код", "inputAmount": "Введите сумму", "inputCadastreNum": "Введите номер кадастра", "inputLatitude": "Введите широту", "inputLongitude": "Введите долготу", "inputBill": "Введите счет", "inputReason": "Введите причину", "inputQty": "Введите количество", "inputDescription": "Введите описание", "inputAccessorySerialNumber": "Введите серийный номер аксессуара", "inputTin": "Введите ИНН", "inputKkmQty": "Введите количество ККМ", "inputFullName": "Введите ФИО", "inputPassportSeries": "Введите серию паспорта", "inputPassportNum": "Введите номер паспорта", "inputIssueBy": "issueBy", "inputPosition": "Введите должность", "inputPoaNum": "Введите номер доверенности", "inputSerialNum": "Введите серийный номер", "inputDeviceSerialNum": "Введите серийный номер ККМ", "inputAnnotation": "Введите аннотацию", "inputMessage": "Напишите сообщение", "inputLastName": "Введите фамилию", "inputName": "Введите имя", "inputPatronymic": "Введите отчество", "inputCompanyName": "Введите название компании", "inputPassword": "Введите пароль", "inputConfirmPassword": "Повторите пароль", "inputOnlyCountNum": "Введите не более {{count}} цифр", "inputOnlyNums": "Введите только цифры", "inputData": "Введите данные", "selectBranches": "Выберите филиалы", "selectTypes": "Выберите типы", "selectSerialNumbers": "Выберите серийные номера", "kkmSerialNumber": "ККМ серийный номер", "newDeviceType": "Новый тип устройства", "selectClientBank": "Выберите банк клиента", "selectPaymentSystem": "Выберите платежную систему", "selectPaymentType": "Выберите тип продажи", "selectPaymentQuoteStatuses": "Выберите статус оплаты", "selectLegalStatus": "Выберите юридический статус", "selectCustomerStatus": "Выберите статус клиента", "selectChangeCtsType": "Тип смены ЦТО", "selectLetterType": "Выберите тип письма", "selectLetterReason": "Выберите причину письма", "leaveDeviceAsTerminal": "Оставить устройство как терминал"}, "validations": {"wrongFormat": "Неверный формат", "wrongTin": "Некорректный ИНН", "wrongPinfl": "Некорректный ПИНФЛ", "wrongAccountNumber": "Неверный расчетный счет!", "tin": "Значение должно быть от 9 до 14 символов.", "qty": "Значение должно быть от 0 до 3 символов.", "qtyCalc": "Значение поля \"Количество\" умножится на цену сервиса", "onlyLat": "Допустимы только латинские буквы", "sms": "Значение должно быть 6 символов.", "secretKey": "Значение должно быть не более 10 символов", "notFilled": "Поле не заполнено", "error": "Ошибка валидации", "noAttachedDoc": "Нет прикрепленного документа", "noPhotos": "Нет фотографий", "noCounterContract": "Контр договора нет", "noAct": "Нет акта", "noActs": "Актов нет", "noCounterAct": "Нет контр акта", "numMissing": "Номер отсутствует", "notesMissing": "Записи отсутствуют", "fmReportCheck": "При проверки выяснилось что некоторых номеров модулей не было в \"ФМ отчете\" те которых нету обратно отправился в формате excel", "onlySpecFileTypes": "Можно загрузить только {{type}} файл", "bigSize": "Размер файла не может превышать {{size}} Мб", "noFile": "Файла нет"}}