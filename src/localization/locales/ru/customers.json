{"addCustomer": "Добавление клиента", "branchesAccess": "Доступ для филиалов ЦТО", "selectBranchesAccess": "Выберите доступ для филиалов ЦТО", "tinOrPinfl": "ИНН | ПИНФЛ", "tinOrPinflValidation": "Значение должно быть от 9 до 14 символов.", "onlyNum": "Значение должно содержать только число.", "smsValidation": "Значение должно быть от 5 и до 6 символов.", "inputTinOrPinfl": "Введите ИНН | ПИНФЛ", "privacyPolicyTerm": "Я ознакомлен с <1>политикой конфиденциальности</1>", "publicOfferTermRu": "Я ознакомлен с <1>договором публичной оферты (RU)</1>", "publicOfferTermUz": "Я ознакомлен с <1>договором публичной оферты (UZ)</1>", "oldTin": "Старый ИНН", "activation": "Активация клиента", "needPay": "Нужно оплатить", "allTinTransactions": "Все транзакции с ИНН и ПИНФЛ", "uploadScanner": "Загрузить Сканер", "scannerType": "Тип <PERSON>нера", "addTT": "Добавить торговую точку", "status": "Статус клиента", "companyStatus": "Юридический статус", "activate": "Активировать клиента", "tariffRemoved": "Тари<PERSON> снят", "kkmRemoved": "ККМ снят", "successMerge": "Слияние прошло успешно", "kkmSuspend": "KKM приостановлен", "kkmActivate": "KKM активирован", "recalcKkm": "Перерасчет прошел успешно", "fiscalStatusUpdated": "Статус фискализации обновлен", "cacheCleared": "Кэш фискализации очищен", "confirmed": "Клиент подтвержден", "paymentMenu": "Меню оплаты", "confirm": "Подтвердить клиента", "registerKkm": "Зарегистрировать девайс", "prepaidBalance": "Баланс предоплаты", "registeredKkms": "Зарегистрировано ККМ", "payedKkms": "Оплачено ККМ", "allowKkms": "Доступ для регистрации ККМ", "notEnough": "Не хватает средств для добавления аксессуаров или ККМ", "goPay": "Перейти к оплате", "addConfirmKkm": "Вы уверены что хотите добавить ККМ ?", "kkmCountInBranch": "Количество ККМ в торговой точке", "fmRemoved": "ФМ снят", "fmMarkAsPaid": "ФМ отмечен как оплаченный", "fmRegistered": "Фискальный модуль успешно зарегистрирован", "fmAdded": "Фискальный модуль успешно добавлен", "fmRegister": "Зарегистрировать фискальный модуль", "fmAdd": "Добавить фискальный модуль", "balanceInfo": "Информация о балансе", "terminalCost": "Стоимость 1-го ФМ", "canBuy": "Можете купить", "notEnoughForRegistration": "Не хватает средств для регистрации ФМ", "addConfirmFm": "Вы уверены что хотите добавить ФМ ?", "fmCountInBranch": "Количество ФМ в торговой точке", "successFiscalization": "Фискализация прошла успешно", "tariffsForYou": "Пакеты тарифов для вас", "useTrial": "Использовать пробный период", "publicOffer": "Принимаю условия Публичной оферты", "tariffNotConnected": "Тариф не подключен", "emptyTariffName": "Нет названия тарифа", "changeTariff": "Изменить тариф", "changeConnectedTariff": "Изменить подключенный тариф", "connectTariff": "Подключить тариф", "branchInstanceAmount": "за {{branchInstance}} филиалов", "kkmRegistered": "ККМ зарегистрирована", "intervalCount": "на {{intervalCount}} {{intervalName}}", "selectKkmModel": "Выбор модели ККМ", "customerBalance": "Ба<PERSON><PERSON><PERSON><PERSON> клиента", "serviceCustomerBalance": "Баланс клиента на обслуживание", "payCustomerBalance": "Баланс клиента на покупку", "invoiceMainDoc": "к товарно-отгрузочным документам Основной договор", "offerNotActivated": "Оферта не подписана", "serviceConfirmed": "Услуга подтверждена", "successRegister": "Регистрация прошла успешно", "publicOfferLoading": "Проверка оферты в процессе. Пожалуйста, подождите.", "customerSource": "Источник регистрации", "customerNotFound": "Клиент не найден", "customerFillFieldsBuyOrder": "Пожалуйста, заполните данные ниже, чтобы создать клиента и заказ одновременно."}