{"sync": "Синхронизация прошла успешно", "customerAdded": "Клиент добавлен", "customerVerified": "Клиент успешно активирован", "activationSent": "Подтверждение отправлено", "codeVerified": "Код подтвержден", "successPayment": "Оплата прошла успешно", "ttAdded": "Торговая точка добавлена", "ttUpdated": "Торговая точка обновлена", "ttConfirmed": "Тороговая точка подтверждена", "ttActivated": "Тороговая точка активирована", "ttRegisteredInApay": "Тороговая точка успешно зарегистрирована в Apay", "fileDeleted": "Файл удален", "success": "Успешно", "actConfirmed": "Акт подтвержден", "actRemoved": "Акт снят", "paymentConfirmed": "Счет на оплату подтвержден", "paymentRemoved": "Счет на оплату снят", "fileUploaded": "Файл загружен", "infoNotFilled": "Недостаточно данных об организации", "cantGetPublicOfferData": "Не получилось получить данные о публичной оферте", "successSmsVerification": "СМС верификация успешно прошла", "statusUpdated": "Статус изменен", "docUploaded": "Документ успешно загружен", "tariffUpdated": "Тариф успешно обновлен", "tariffConnected": "Тариф успешно подключен", "customerDataUpdated": "Данные клиента обновлены", "bankUpdated": "Банк обновлен", "bankAdded": "Банк добавлен", "invoiceRejected": "Счет-фактура отклонена", "rowDeleted": "Запись успешно удалена", "paymentReverted": "Оплата возвращена", "smartposSync": "Синхронизация с SMARTPOS прошла успешно", "launcherSync": "Синхронизация с LAUNCHER прошла успешно", "paymentPending": "Оплата в ожидании", "paymentCancelled": "Оплата отменена", "paymentPaid": "Оплата успешна прошла", "orderAdded": "Заказ добавлен", "modelCreated": "Модель создана", "modelUpdated": "Модель обновлена", "agreementConfirmed": "Договор подтвержден", "agreementAccepted": "Договор принят", "agreementRejected": "Договор отклонен", "agreementClosed": "Договор закрыт", "agreementAdded": "Договор добавлен", "invoicePaymentCreated": "Счет на оплату создан", "invoicePaymentConfirmed": "Счет на оплату подтвержден", "invoicePaymentRejected": "Счет на оплату отклонен", "counterContractAdded": "Контр договор добавлен", "counterContractUpdated": "Контр договор обновлен", "created": "Создано", "guaranteeLetterCreated": "Гарантийное письмо создано", "guaranteeLetterUpdated": "Гарантийное письмо обновлено", "returnActAdded": "Акт возврата добавлен", "returnActAccepted": "Акт возврата принят", "folderAdded": "Папка добавлена", "folderUpdated": "Папка обновлена", "addFolderIn": "Добавить папку в ({{name}})", "documentNameUpdated": "Название документа обновлено", "docDeleted": "Документ удален", "caseAdded": "Кейс добавлен", "caseUpdated": "Кейс обновлен", "phoneIsUsed": "Этот номер уже используется", "phoneIsFree": "Номер свободен", "fileAdded": "Файл добавлен", "timeslotApplied": "График работы применён", "dependentAdded": "Член семьи добавлен", "dependentUpdated": "Член семьи обновлен", "smsWithCodeSent": "SMS с кодом отправлено", "smsSent": "СМС было отправлено", "smsConfirmSent": "СМС-подтверждение отправлено", "cantEdit": "Вы не можете редактировать ТТ", "paymentQuoteTypeChanged": "Тип продажи изменен", "paymentQuoteStatusChanged": "Статус оплаты изменен", "empty": "Уведомнений нет", "notifications": "Уведомления", "dataChanged": "Данные изменены", "kkmAdded": "ККМ добавлен"}