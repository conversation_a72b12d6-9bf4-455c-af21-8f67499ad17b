import React from "react";
import { ContentCustom } from "#ui/contentCustom";
import { ErrorResponse } from "#types/api";
import { useTranslation } from "react-i18next";

export const NotFoundPage = () => {

  const { t } = useTranslation();

  const error: ErrorResponse = {
    title: t("pageNotFound"),
    status: 404,
  };

  return (
    <ContentCustom error={error}>
      {""}
    </ContentCustom>
  )
};