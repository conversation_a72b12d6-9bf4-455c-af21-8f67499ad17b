import React, { <PERSON> } from "react";

import { useStore } from "effector-react";
import Cookies from "js-cookie";
import { useHistory } from "react-router-dom";

import { $currentUser } from "#stores/account";
import { ArrowDownIcon } from "#assets/icons";
import { useTranslation } from "react-i18next";
import { PopoverCustom } from "#ui/popoverCustom";
import { ButtonCustom } from "#ui/buttonCustom";
import { useLang } from "#hooks/useLang";

export const CurrentUserDropdown: FC = () => {
  const history = useHistory();

  const { t } = useTranslation();
  const { onLanguageChange, currentLanguage } = useLang();

  const currentUserState = useStore($currentUser.store);
  const currentUserData = currentUserState.data!;

  const onSignOutClick = () => {
    Cookies.remove("access-token");
    Cookies.remove("refresh-token");

    history.push("/sign-in");
  };

  return (
    <div className="header-user">
      <PopoverCustom
        overlayClassName="header-user__dropdown"
        placement="bottomRight"
        content={(
          <>
            <PopoverCustom.Item>
              <ButtonCustom>
                {t("changePassword")}
              </ButtonCustom>
            </PopoverCustom.Item>
            <PopoverCustom.Item>
              <ButtonCustom onClick={onLanguageChange}>
                {currentLanguage === "ru" ? "Оʻzbekcha" : "Русский"}
              </ButtonCustom>
            </PopoverCustom.Item>
            <PopoverCustom.Item>
              <ButtonCustom onClick={onSignOutClick}>
                {t("logout")}
              </ButtonCustom>
            </PopoverCustom.Item>
          </>
        )}
      >
        <div className="header-user__name">
          <span>{currentUserData.lastName} {currentUserData.firstName}</span>
          <ArrowDownIcon />
        </div>
      </PopoverCustom>
    </div>
  );
};
