@import "src/styles/variables.scss";

.site-header {
  display: flex;
  justify-content: space-between;
  color: #fff;
  padding: 0 20px;
  margin-bottom: 28px;

  &__left, &__right {
    display: flex;
    align-items: center;
  }
}

.header-user {
  & > .custom-btn {
    background: none;
    box-shadow: none;
    padding: 0;
    height: auto;
    color: #fff !important;
    border: none;

    &:hover {
      background: none;
      border: none;
    }

    &:focus {
      background: none;
      border: none;
    }
  }

  &__name {
    display: flex;
    align-items: center;
    border-radius: 6px;
    border-top: 1px solid rgba(#fff, 0.1);
    background-color: rgba(#fff, 0.08);
    backdrop-filter: blur(4px);
    font-size: 12px;
    line-height: 15px;
    font-weight: 600;
    padding: 5px 7px 6px;
    cursor: pointer;
    transition: background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    user-select: none;

    &:hover {
      background-color: rgba(#fff, 0.2);
    }

    svg {
      margin-left: 4px;
      transition: transform 0.3s ease;
    }
  }

  .ant-popover-open {
    .header-user__name {
      background-color: rgba(#fff, 0.2);

      svg {
        margin-top: 1px;
        transform: rotate(-180deg);
      }
    }
  }

  &__dropdown {
    .ant-popover-inner-content {
      min-width: 220px;
    }
  }
}