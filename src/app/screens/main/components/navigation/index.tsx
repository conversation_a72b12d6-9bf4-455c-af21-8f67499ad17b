import React, { useEffect, useMemo, useState } from "react";

import { Menu } from "antd";
import type { ItemType } from "antd/lib/menu/hooks/useItems";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { Link } from "react-router-dom";

import "./styles.scss";

const getActiveKeys = (pathname: string) => {
  if (pathname === "/") {
    return [];
  }

  const pathnameArray = pathname.split("/");
  const res: Array<string> = [];

  pathnameArray.reduce((acc, item) => {
    if (item === "") {
      return "";
    }

    const path = acc + "/" + item;

    res.push(path);

    return path;
  }, "");

  return res;
};

export const SiteNavigation = () => {
  const location = useLocation();

  const { t } = useTranslation();

  const [selectedKeys, setSelectedKeys] = useState<Array<string>>([]);

  useEffect(() => {
    setSelectedKeys(getActiveKeys(location.pathname));
  }, [location.pathname]);

  const menuItems: Array<ItemType> = useMemo(() => {
    const items: Array<{ label: string; key: string }> = [
      {
        label: t("monitoring"),
        key: "/monitoring",
      },
      {
        label: t("customers"),
        key: "/customers",
      },
      // {
      //   label: t("applications"),
      //   key: "/applications"
      // },
    ];

    return items.map((item) => ({
      label: <Link to={item.key}>{item.label}</Link>,
      key: item.key,
    }));
  }, []);

  return <Menu mode="horizontal" className="top-menu" items={menuItems} selectedKeys={selectedKeys} />;
};
