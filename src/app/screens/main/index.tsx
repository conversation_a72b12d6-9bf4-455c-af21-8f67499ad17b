import React, { useEffect } from "react";

import { ErrorBoundary } from "#components/errorBoundary";
import { $currentUser } from "#stores/account";
import { SpinCustom } from "#ui/spinCustom";
import { useStore } from "effector-react";
import Cookies from "js-cookie";
import { useHistory } from "react-router";
import { Redirect, Route, Switch } from "react-router-dom";

import { Applications } from "../../sections/applications";
import { Customers } from "../../sections/customers";
import { MerchantAcquiring } from "../../sections/merchantAcquiring";

import { SiteHeader } from "./components/header";
import { SiteNavigation } from "./components/navigation";
import { NotFoundPage } from "./components/notFoundPage";
import "./styles.scss";

export const Main = () => {
  const history = useHistory();

  const currentUserState = useStore($currentUser.store);

  useEffect(() => {
    const token = Cookies.get("access-token");

    if (token) {
      $currentUser.effect();
    } else {
      history.push("/sign-in");
    }
  }, []);

  if (currentUserState.error) {
    return null;
  }

  if (!currentUserState.data) {
    return (
      <div className="main-loader">
        <SpinCustom size="large" />
      </div>
    );
  }

  return (
    <div className="main-layout">
      <SiteHeader />
      <SiteNavigation />
      <div className="site-content">
        <div className="site-content__in">
          <ErrorBoundary>
            <Switch>
              <Redirect exact from="/" to="/monitoring" />
              <Route path="/monitoring" render={(props) => <MerchantAcquiring {...props} />} />
              <Route path="/customers" render={(props) => <Customers {...props} />} />
              <Route path="/applications" render={(props) => <Applications {...props} />} />
              <Route path="*" component={NotFoundPage} />
            </Switch>
          </ErrorBoundary>
        </div>
      </div>
    </div>
  );
};
