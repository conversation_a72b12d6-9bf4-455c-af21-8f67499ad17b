import React from "react";
import { AuthLayout } from "../layout";
import { AuthLoginInputIcon, AuthPasswordInputIcon } from "#assets/icons";
import { Form } from "antd";
import { AuthPasswordInput } from "../components/passwordField";
import { ButtonCustom } from "#ui/buttonCustom";
import { namespaces } from "#localization/i18n.constants";
import { FormCustom } from "#ui/formCustom";
import { useTranslation } from "react-i18next";
import { LocalizationWithTag } from "#localization/components";

export const ResetPassword = () => {

  const { t } = useTranslation();

  const [form] = Form.useForm();

  const onFieldsChange = () => {
    // if (logInState.error) {
    //   $logIn.reset();
    // }
  };

  const onFinish = () => {

  };

  return (
    <AuthLayout>
      <FormCustom
        form={form}
        onFinish={onFinish}
        phantomSubmit
        onFieldsChange={onFieldsChange}
      >
        <div className="auth__form__head">
          <div className="auth__form__head__title">{t("resetPassword.restorePassword", { ns: namespaces.auth })}</div>
          <div className="auth__form__head__text">
            <LocalizationWithTag
              text={t("resetPassword.restorePasswordText", { ns: namespaces.auth })}
              tags={{
                1: (text) => (
                  <div>{text}</div>
                ),
              }}
            />
          </div>
        </div>
        <div className="auth__form__field">
          <div className="auth__form__field__prefix">
            <AuthLoginInputIcon />
          </div>
          <Form.Item
            className="auth__form__field-last"
            name="login"
            rules={[
              {
                validateTrigger: "onSubmit",
                required: true,
              },
            ]}
          >
            <input className="auth__input" placeholder={t("userName")} />
          </Form.Item>
        </div>
        <div className="auth__form__btn-wrap">
          <ButtonCustom htmlType="submit" loading={false}>
            {t("continue", { ns: namespaces.buttons })}
          </ButtonCustom>
        </div>
      </FormCustom>
    </AuthLayout>
  )
}