import React, { FC, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { ClosedEyeIcon, EyeIcon } from "#assets/icons";

export const AuthPasswordInput: FC<any> = (props) => {
  const { value, ...restProps } = props;

  const { t } = useTranslation();

  const inputRef = useRef<HTMLInputElement>(null);

  const [showPassword, setShowPassword] = useState(false);

  const onShowPassword = () => {
    if (inputRef.current) {
      if (value !== undefined) {
        setTimeout(() => {
          inputRef.current!.focus();
          inputRef.current!.setSelectionRange(value.length, value.length);
        }, 0);
      }
    }
    setShowPassword(!showPassword);
  };

  return (
    <>
      <input
        ref={inputRef}
        className="auth__input"
        placeholder={t("password")}
        type={showPassword ? "text" : "password"}
        {...restProps}
      />
      <div
        className="auth__input__show-password"
        onClick={onShowPassword}
      >
        {showPassword ? (
          <ClosedEyeIcon />
        ) : (
          <EyeIcon />
        )}
      </div>
    </>
  )
};