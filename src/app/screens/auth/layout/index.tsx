import React, { FC, ReactNode } from "react";

import logo from "#assets/images/logo.svg";
import { useLang } from "#hooks/useLang";
import { LocalizationWithTag } from "#localization/components";
import { namespaces } from "#localization/i18n.constants";
import { ButtonCustom } from "#ui/buttonCustom";
import { useTranslation } from "react-i18next";

import "./styles.scss";

type PropsTypes = {
  children: ReactNode;
};

export const AuthLayout: FC<PropsTypes> = (props) => {
  const { t } = useTranslation();
  const { onLanguageChange, currentLanguage } = useLang();

  return (
    <div className="auth">
      <div className="auth__content">
        <div className="auth__head">
          <div className="auth__logo">
            <img src={logo} alt="logo" />
          </div>
          <div className="auth__lang">
            <ButtonCustom type="primary" size="small" onClick={onLanguageChange}>
              {currentLanguage === "ru" ? "Оʻzbekcha" : "Русский"}
            </ButtonCustom>
          </div>
        </div>
        <div className="auth__form-wrap">{props.children}</div>
        <div className="auth__footer">
          <div className="auth-slogan">
            <LocalizationWithTag
              text={t("common.slogan", { ns: namespaces.auth })}
              tags={{
                1: (text) => <div>{text}</div>,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
