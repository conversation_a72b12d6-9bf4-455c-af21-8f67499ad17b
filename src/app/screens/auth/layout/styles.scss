@import "src/styles/variables.scss";

.auth {
  display: flex;
  height: 100%;
  color: #fff;

  &__content {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    flex-flow: column;
    z-index: 1;
  }

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 28px 30px;

    .auth__logo {
      font-size: 0;
    }

    .auth__lang {
      .custom-btn {
        height: 24px;
        padding: 4.6px 10px;
        background-color: rgba(#fff, 0.1);

        &:hover {
          background-color: rgba(#fff, 0.2);
        }
      }
    }
  }

  &__form-wrap {
    position: relative;
    width: 348px;
    display: flex;
    justify-content: center;
    flex-flow: column;
    margin: 40px 15px 0;

    .ant-form {
      color: #fff;

      .ant-form-item {
        margin-bottom: 2px;

        &.ant-form-item-has-error {
          .auth__input {
            border-color: $danger;
          }
        }

        .ant-form-item-explain-error {
          display: none;
        }

        .ant-input {
          color: rgba(#fff, 0.3);
          background-color: rgba(#fff, 0.1);
          border: none;
        }

        .auth__input {
          color: #fff;
          background-color: rgba(#fff, 0.1);
          outline: none;
          width: 100%;
          border: none;
          padding: 13px 20px 13px 48px;
          border-top: 1px solid rgba(#fff, 0.2);
          font-size: 14px;

          &::placeholder {
            color: rgba(#fff, 0.3);
            user-select: none;
          }

          &__show-password {
            position: absolute;
            top: 16px;
            right: 16px;
            color: #fff;
            font-size: 0;
            cursor: pointer;
            user-select: none;
          }
        }

        input:-webkit-autofill,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:hover {
          -webkit-text-fill-color: #fff !important;
          transition: background-color 9999s ease-in-out 0s;
          caret-color: #fff;
          font-size: 14px;
        }
      }
    }
  }

  &__form__btn-wrap {
    padding-top: 8px;
    text-align: center;

    .custom-btn {
      background-color: #202124 !important;
      color: #fff !important;
      height: 36px;
      padding: 4px 14px 4px;
      border: none !important;

      &:before {
        background: none;
      }

      &:hover {
        background-color: #000 !important;
      }
    }
  }

  .auth__form__head {
    border-radius: 10px 10px 2px 2px;
    text-align: center;
    background-color: rgba(#fff, 0.1);
    border-top: 1px solid rgba(#fff, 0.2);
    padding: 18px;
    margin-bottom: 2px;

    &__title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 6px;
    }

    &__text {
      font-size: 14px;
      line-height: 20px;
    }
  }

  .auth__form__field {
    position: relative;

    &__prefix {
      position: absolute;
      top: 15px;
      left: 16px;
      color: #fff;
      font-size: 0;
    }

    &-first {
      .auth__input {
        border-radius: 12px 12px 2px 2px;
      }
    }

    &-last {
      .auth__input {
        border-radius: 2px 2px 12px 12px;
      }
    }
  }

  &__warning {
    position: absolute;
    top: -40px;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.34);
    border-radius: 10px;
    padding: 16px;
    transform: translateY(-100%);
    animation: warningFade 0.15s linear;

    &__icon {
      width: 18px;
      min-width: 18px;
      margin-right: 14px;
    }

    &__body {
      flex-grow: 1;

      a {
        display: inline-flex;
        align-items: center;
        color: rgba(#fff, 0.4);

        svg {
          margin: 2px 0 0 4px;
        }

        &:hover {
          color: #fff;
        }
      }
    }
  }

  &__footer {
    width: 100%;
    padding: 30px;

    .auth-slogan {
      font-size: 18px;
      line-height: 24px;
      max-width: 532px;
      min-height: 96px;
    }
  }
}

@-webkit-keyframes warningFade {
  0% {
    opacity: 0;
    top: -10px;
  }
  50% {
    opacity: 0.5;
    top: -20px;
  }
  100% {
    opacity: 1;
    top: -40px;
  }
}