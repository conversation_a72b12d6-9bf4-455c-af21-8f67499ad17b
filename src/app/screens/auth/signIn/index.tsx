import React, { useEffect } from "react";
import { AuthLayout } from "../layout";
import { FormCustom } from "#ui/formCustom";
import { Form } from "antd";
import Cookies from "js-cookie";
import { ButtonCustom } from "#ui/buttonCustom";
import { ArrowRightIcon, AuthLoginInputIcon, AuthPasswordInputIcon, WarningIcon } from "#assets/icons";
import { $logIn } from "#stores/auth";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import { namespaces } from "#localization/i18n.constants";
import { Link } from "react-router-dom";
import { AuthPasswordInput } from "../components/passwordField";
import { useHistory } from "react-router";

export const SignIn = () => {

  const history = useHistory();

  const { t } = useTranslation();

  const logInState = useStore($logIn.store);

  const [form] = Form.useForm();

  useEffect(() => {
    return () => {
      $logIn.reset();
    }
  }, []);

  useEffect(() => {
    if (logInState.data) {
      Cookies.set("access-token", logInState.data.access_token);
      history.push("/");
    }
  }, [logInState.data]);

  const onFieldsChange = () => {
    if (logInState.error) {
      $logIn.reset();
    }
  };

  const onFinish = (formFields: any) => {
    Cookies.remove("access-token");

    const formData = {
      username: formFields.username,
      password: formFields.password,
      rememberMe: false,
    };

    $logIn.effect(formData);
  };

  return (
    <AuthLayout>
      {logInState.error && (
        <div className="auth__warning">
          <div className="auth__warning__icon">
            <WarningIcon />
          </div>
          <div className="auth__warning__body">
            {logInState.error.status === 400 ? (
              <>
                <div className="auth__warning__body__title">
                  {t("auth.incorrectLoginPasswordEntered", { ns: namespaces.auth })}
                </div>
                <Link to="/reset-password">
                  Восстановить пароль <ArrowRightIcon />
                </Link>
              </>
            ) : (
              <div className="auth__warning__body__title">
                {logInState.error.title}
              </div>
            )}
          </div>
        </div>
      )}
      <FormCustom
        form={form}
        onFinish={onFinish}
        phantomSubmit
        onFieldsChange={onFieldsChange}
      >
        <div className="auth__form__field">
          <div className="auth__form__field__prefix">
            <AuthLoginInputIcon />
          </div>
          <Form.Item
            className="auth__form__field-first"
            name="username"
            rules={[
              {
                validateTrigger: "onSubmit",
                required: true,
              },
            ]}
          >
            <input className="auth__input" placeholder={t("userName")} />
          </Form.Item>
        </div>
        <div className="auth__form__field">
          <div className="auth__form__field__prefix">
            <AuthPasswordInputIcon />
          </div>
          <Form.Item
            className="auth__form__field-last"
            name="password"
            rules={[
              {
                validateTrigger: "onSubmit",
                required: true,
              },
            ]}
          >
            <AuthPasswordInput />
          </Form.Item>
        </div>
        <div className="auth__form__btn-wrap">
          <ButtonCustom htmlType="submit" loading={logInState.loading}>
            {t("auth.logIn", { ns: namespaces.auth })}
          </ButtonCustom>
        </div>
      </FormCustom>
    </AuthLayout>
  )
}