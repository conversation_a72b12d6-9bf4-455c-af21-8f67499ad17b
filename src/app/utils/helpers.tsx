import { ChangeEvent } from "react";

import { IReferenceTypeItemModel } from "#businessLogic/models/public/referencesTypes";
import { DATE_FORMAT, RANGE_DATE_TIME_FORMAT } from "#constants/index";
import { i18n } from "#localization/i18n";
import { namespaces } from "#localization/i18n.constants";
import { notification } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { NotificationInstance } from "antd/lib/notification";
import { debounce } from "lodash";
import moment from "moment";

export const openNotification = (type: keyof NotificationInstance, message: string, description?: string) => {
  notification[type]({
    message: message,
    description: description,
  });
};

export const withDebounce = debounce((action: any) => action(), 400);

export const validateTin = (tin: string) => tin.length === 9;
export const validatePinFl = (pinFl: string) => pinFl.length === 14;

export const formatPhoneNumber = (str: string) => {
  const cleaned = ("" + str).replace(/\D/g, "");
  const match = cleaned.match(/^(\d{3})?(\d{2})(\d{3})(\d{2})(\d{2})$/);

  return match ? `${match[1] ? `+${match[1]}` : "+998"} ${match[2]} ${match[3]} ${match[4]} ${match[5]}` : cleaned;
};

export const formatDate = (date: string, format?: string) => {
  if (!date) {
    return "-";
  }

  return moment(date).format(format ? format : DATE_FORMAT);
};

export const formatNumber = (price: number | string | undefined) => {
  if (!price) return 0;

  const n = String(price),
    p = n.indexOf(".");

  return n.replace(/\d(?=(?:\d{3})+(?:\.|$))/g, (m, i) => (p < 0 || i < p ? `${m} ` : m));
};

export const getDigitsNums = (value: string | undefined | null) => {
  if (!value) return "";

  return value.replace(/\D/g, "");
};

// (прописные латинские буквы, "'", "`", ".", "-", "&", и пробелы)
export const regexForUppercaseLatinAndSpecialChars = /^(?=.*[A-Z])[\dA-Z&`'\-\. №]+$/;

// (прописные латинские буквы, "'", "`" и пробелы)
export const uppercaseLettersWithSymbolsRegex = /^[A-Z '`]+$/;

/**
 * @description - Проверяет входящие данные на соответствие регулярному выражению (прописные латинские буквы, "'", "`", ".", "-", "&", и пробелы)
 */
export const validateUppercaseLatinWithSpecialChars = (regex: RegExp) => {
  const validate = (input: string) => {
    return regex.test(input.trim());
  };

  const toUppercaseLatin = (input: string) => {
    return input.replace(/[a-z]/g, (char) => char.toUpperCase());
  };

  const changeToUpperCase = (form: any, key: any) => {
    return (e: ChangeEvent<HTMLInputElement>) => {
      const trimmedValue = e.target.value?.trim();

      if (!trimmedValue) {
        form.setFieldValue(key, "");
        void form.validateFields([key]);
        return;
      }

      const uppercaseLatin = toUppercaseLatin(e.target.value);
      if (validate(uppercaseLatin)) {
        form.setFieldValue(key, uppercaseLatin);
        void form.validateFields([key]);
      }
    };
  };

  return {
    regex,
    validate,
    toUppercaseLatin,
    changeToUpperCase,
  };
};

export const getPhoneTypeIds = (
  phoneTypes: Array<IReferenceTypeItemModel>,
): { workTypeId: number; otherTypeId: number } => {
  return phoneTypes.reduce(
    (acc: any, item) => {
      return {
        workTypeId: item.code === "WORK" ? item.id : acc.workTypeId,
        otherTypeId: item.code === "OTHER" ? item.id : acc.workTypeId,
      };
    },
    { workTypeId: undefined, otherTypeId: undefined },
  );
};

export const validateFormNumberInput = (maxLength: number) => (_: any, value: string) => {
  if (!value) {
    return Promise.resolve();
  }

  const regex = new RegExp(`^\\d{1,${maxLength}}$`);
  if (regex.test(value)) {
    return Promise.resolve();
  }

  if (/^\d+$/.test(value)) {
    return Promise.reject(
      new Error(i18n.t("placeholders.inputOnlyCountNum", { ns: namespaces.fields, count: maxLength })),
    );
  }

  return Promise.reject(new Error(i18n.t("placeholders.inputOnlyNums")));
};

export function getLastMonthRange() {
  const to = moment().endOf("day").format(RANGE_DATE_TIME_FORMAT);
  const from = moment().subtract(1, "month").startOf("day").format(RANGE_DATE_TIME_FORMAT);

  return { from, to };
}

export const getMultipleAdditionalNames = <T extends object>(
  option: DefaultOptionType | DefaultOptionType[],
  key: keyof T,
): string[] | undefined => {
  if (!option) {
    return undefined;
  }
  if (!Array.isArray(option)) {
    return [option["data-option"]?.[key]];
  }
  return option.map((o) => o["data-option"]?.[key]).filter(Boolean) as string[];
};

export const getSingleAdditionalName = <T extends object>(
  option: DefaultOptionType | DefaultOptionType[],
  key: keyof T,
): string | undefined => {
  if (!option) {
    return undefined;
  }
  if (!Array.isArray(option)) {
    return option["data-option"]?.[key];
  }
  return undefined;
};

export const isPdf = (filename: string) => {
  const extension = filename.substr(filename.lastIndexOf(".") + 1);
  return extension.toLowerCase() === "pdf";
};

export const isPdfOrImage = (filename: string) => isPdf(filename) || isImage(filename);

export const isImage = (filename: string) => {
  const validImageExtensions = ["jpg", "jpeg", "bmp", "gif", "png", "webp"];
  const extension = filename.substr(filename.lastIndexOf(".") + 1);
  return validImageExtensions.indexOf(extension.toLowerCase()) !== -1;
};

export function formatFileSize(sizeInBytes: number) {
  const kb = 1024;
  const mb = 1024 * 1024;

  if (sizeInBytes < kb) {
    // Меньше 1 KB
    return sizeInBytes + " B";
  } else if (sizeInBytes < mb) {
    // Меньше 1 MB
    return (sizeInBytes / kb).toFixed(1) + " KB";
  } else {
    // Всё, что больше либо равно 1 MB
    return (sizeInBytes / mb).toFixed(1) + " MB";
  }
}

export const printIframe = (id: number | string) => {
  // @ts-ignore
  const iframe = document.hasOwnProperty("frames") ? document.frames[id] : document.getElementById(id);
  const iframeWindow = iframe.contentWindow || iframe;

  iframe.focus();
  iframeWindow.print();

  return false;
};

export const getRestStr = (str: string, subStr: string) => {
  if (str.indexOf(subStr) > -1) {
    return str.slice(subStr.length);
  } else {
    return "";
  }
};
