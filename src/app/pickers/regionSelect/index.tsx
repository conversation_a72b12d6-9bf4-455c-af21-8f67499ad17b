import React, { useEffect } from "react";

import { $regionItems } from "#stores/public";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const RegionSelect: React.FC<SelectUIPropTypes> = (props) => {
  const { t } = useTranslation();

  const regionItemsState = useStore($regionItems.store);

  useEffect(() => {
    if (!regionItemsState.data.length) {
      $regionItems.effect();
    }
  }, []);

  return (
    <SelectCustom loading={regionItemsState.loading} placeholder={t("placeholders.selectRegion")} {...props}>
      {regionItemsState.data.map((item) => (
        <SelectCustom.Option value={item.id} key={item.id}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
