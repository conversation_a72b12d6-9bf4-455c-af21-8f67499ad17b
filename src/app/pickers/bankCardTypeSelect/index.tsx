import React, { useEffect } from "react";

import { $bankCardTypes } from "#stores/public";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const BankCardTypesSelect: React.FC<SelectUIPropTypes> = (props) => {
  const { t } = useTranslation();

  const bankCardTypesState = useStore($bankCardTypes.store);

  useEffect(() => {
    if (!bankCardTypesState.data.length) {
      $bankCardTypes.effect();
    }
  }, []);

  return (
    <SelectCustom loading={bankCardTypesState.loading} placeholder={t("placeholders.selectPaymentSystem")} {...props}>
      {bankCardTypesState.data.map((item) => (
        <SelectCustom.Option value={item.code} key={item.code}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
