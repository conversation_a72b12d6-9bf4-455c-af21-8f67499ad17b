import React, { FC, useMemo } from "react";

import { api } from "#businessLogic/api";
import { IReferenceTypeItemModel, TReferenceTypesLookupParams } from "#businessLogic/models/public/referencesTypes";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type PropsTypes = SelectLookupPickerPropsTypes<TReferenceTypesLookupParams, IReferenceTypeItemModel>;

export const ReferenceTypeSelect: FC<PropsTypes> = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(api.public.getReferenceTypesLookup, new XHRState<IReferenceTypeItemModel[]>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder={t("placeholders.selectType")}
      showSearch={true}
      optionFilterProp="children"
      itemsStoreController={$items}
      {...props}
    />
  );
};
