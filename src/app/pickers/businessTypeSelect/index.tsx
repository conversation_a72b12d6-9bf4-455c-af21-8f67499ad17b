import React, { useEffect } from "react";

import { $businessTypes } from "#stores/enums";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const BusinessTypeSelect: React.FC<SelectUIPropTypes> = (props) => {
  const { t } = useTranslation();

  const businessTypesState = useStore($businessTypes.store);

  useEffect(() => {
    if (!businessTypesState.data.length) {
      $businessTypes.effect();
    }
  }, []);

  return (
    <SelectCustom
      allowClear={true}
      loading={businessTypesState.loading}
      placeholder={t("placeholders.selectBusinessType")}
      {...props}
    >
      {businessTypesState.data.map((item) => (
        <SelectCustom.Option value={item.code} key={item.code}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
