import React, { useEffect } from "react";

import { $vatItems } from "#stores/public";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const VatSelect: React.FC<SelectUIPropTypes> = (props) => {
  const { t } = useTranslation();

  const vatItemsState = useStore($vatItems.store);

  useEffect(() => {
    if (!vatItemsState.data.length) {
      $vatItems.effect();
    }
  }, []);

  return (
    <SelectCustom loading={vatItemsState.loading} placeholder={t("placeholders.selectVat")} {...props}>
      {vatItemsState.data.map((item) => (
        <SelectCustom.Option value={item.id} key={item.id}>
          {item.name} {item.percent !== null ? item.percent + "%" : ""}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
