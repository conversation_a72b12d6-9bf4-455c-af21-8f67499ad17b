import React, { FC, useMemo } from "react";

import { api } from "#businessLogic/api";
import { INeighborhoodLookupItemModel, TNeighborhoodQueryParams } from "#businessLogic/models/public/neighborhood";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type NeighborhoodSelectProps = SelectLookupPickerPropsTypes<TNeighborhoodQueryParams, INeighborhoodLookupItemModel>;

export const NeighborhoodSelect: FC<NeighborhoodSelectProps> = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(api.public.getNeighborhoodsLookup, new XHRState<INeighborhoodLookupItemModel[]>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder={t("placeholders.selectNeighborhood")}
      allowClear={true}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      itemsStoreController={$items}
      {...props}
    />
  );
};
