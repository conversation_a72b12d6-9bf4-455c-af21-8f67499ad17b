import React, { useEffect } from "react";

import { $deviceModels } from "#stores/customerDevices";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const DeviceModelSelect: React.FC<SelectUIPropTypes> = (props) => {
  const { t } = useTranslation();

  const deviceModelsState = useStore($deviceModels.store);

  useEffect(() => {
    if (!deviceModelsState.data.length) {
      $deviceModels.effect();
    }
  }, []);

  return (
    <SelectCustom loading={deviceModelsState.loading} placeholder={t("placeholders.selectModel")} {...props}>
      {deviceModelsState.data.map((item) => (
        <SelectCustom.Option value={item.id} key={item.id}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
