import React, { FC, useMemo } from "react";

import { api } from "#businessLogic/api";
import { IDeviceItemModel } from "#businessLogic/models/common";
import { TFiscalModuleLookupParams } from "#businessLogic/models/customerFiscalModules";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type KkmSerialNumberSelectComponentType = FC<SelectLookupPickerPropsTypes<TFiscalModuleLookupParams, IDeviceItemModel>>;

export const FiscalModuleSelect: KkmSerialNumberSelectComponentType = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(api.customerFiscalModule.getFiscalModulesLookup, new XHRState<Array<IDeviceItemModel>>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder={t("placeholders.selectFm")}
      allowClear={true}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      optionValue="id"
      optionName="serialNumber"
      itemsStoreController={$items}
      {...props}
    />
  );
};
