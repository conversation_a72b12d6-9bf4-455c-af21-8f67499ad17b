import React, { useEffect } from "react";

import { $customerAppTypes } from "#stores/enums";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const CustomerAppTypeSelect: React.FC<SelectUIPropTypes> = (props) => {
  const customerAppTypesState = useStore($customerAppTypes.store);

  const { t } = useTranslation();

  useEffect(() => {
    $customerAppTypes.effect();
  }, []);

  return (
    <SelectCustom
      allowClear={true}
      loading={customerAppTypesState.loading}
      placeholder={t("placeholders.selectAppType")}
      {...props}
    >
      {customerAppTypesState.data.map((item) => (
        <SelectCustom.Option value={item.code} key={item.code}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
