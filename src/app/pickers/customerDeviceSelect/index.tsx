import React, { FC, useMemo } from "react";

import { api } from "#businessLogic/api";
import { ICustomerDeviceLookupItemModel, TCustomerDevicesLookupParams } from "#businessLogic/models/customerDevices";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type CustomerDeviceSelectStoreItem = ICustomerDeviceLookupItemModel & {
  deviceId: number;
  deviceSerial: string;
};

type PropsTypes = SelectLookupPickerPropsTypes<TCustomerDevicesLookupParams, CustomerDeviceSelectStoreItem>;

export const CustomerDeviceSelect: FC<PropsTypes> = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(
      api.customerDevices.getCustomerDevicesLookup,
      new XHRState<Array<CustomerDeviceSelectStoreItem>>([]),
      {
        doneReducer: (state, response) => {
          return {
            loading: false,
            data: response.result.data.map((item) => ({
              ...item,
              deviceId: item.device.id,
              deviceSerial: item.device.serialNumber,
            })),
            error: null,
          };
        },
      },
    );
  }, []);

  return (
    <SelectCustom.Lookup
      allowClear={true}
      placeholder={t("placeholders.selectKkm")}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      optionName="deviceSerial"
      optionValue="deviceId"
      itemsStoreController={$items}
      {...props}
    />
  );
};
