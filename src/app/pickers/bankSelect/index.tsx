import React, { FC, useMemo } from "react";

import { api } from "#businessLogic/api";
import { TDefaultLookupQueryParams, TIdCodeNameModel } from "#businessLogic/models/common";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type PropsTypes = SelectLookupPickerPropsTypes<TDefaultLookupQueryParams, TIdCodeNameModel>;

export const BankSelect: FC<PropsTypes> = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(api.banks.getBanksLookup, new XHRState<Array<TIdCodeNameModel>>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder={t("placeholders.selectBank")}
      allowClear={true}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      itemsStoreController={$items}
      {...props}
    />
  );
};
