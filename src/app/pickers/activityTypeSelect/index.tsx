import React, { useEffect } from "react";

import { $activityTypeChildren, $activityTypeParents } from "#stores/public";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const ActivityTypeParentSelect: React.FC<SelectUIPropTypes> = (props) => {
  const activityTypeParentsState = useStore($activityTypeParents.store);

  const { t } = useTranslation();

  useEffect(() => {
    if (!activityTypeParentsState.data.length) {
      $activityTypeParents.effect({
        onlyParents: true,
      });
    }
  }, []);

  return (
    <SelectCustom
      allowClear={true}
      loading={activityTypeParentsState.loading}
      placeholder={t("placeholders.selectActivityTypeParent")}
      {...props}
    >
      {activityTypeParentsState.data.map((item) => (
        <SelectCustom.Option value={item.id} key={item.id}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};

export const ActivityTypeChildSelect: React.FC<SelectUIPropTypes & { parentId: number | undefined }> = (props) => {
  const { value, parentId, ...restProps } = props;

  const { t } = useTranslation();

  const activityTypeChildrenState = useStore($activityTypeChildren.store);

  useEffect(() => {
    if (parentId) {
      $activityTypeChildren.effect({
        parentId,
        onlyParents: false,
      });
    }
  }, [parentId]);

  return (
    <SelectCustom
      loading={activityTypeChildrenState.loading}
      placeholder={t("placeholders.selectActivityType")}
      disabled={!parentId}
      value={value && activityTypeChildrenState.data.length ? value : undefined}
      {...restProps}
    >
      {activityTypeChildrenState.data.map((item) => (
        <SelectCustom.Option value={item.id} key={item.id}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
