import React, { FC, useMemo } from "react";

import { api } from "#businessLogic/api";
import { TIdNameModel } from "#businessLogic/models/common";
import { TCustomerBranchesLookupParams } from "#businessLogic/models/customerBranches";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type PropsTypes = SelectLookupPickerPropsTypes<TCustomerBranchesLookupParams, TIdNameModel>;

export const CustomerBranchSelect: FC<PropsTypes> = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(api.customerBranches.getCustomerBranchesLookup, new XHRState<TIdNameModel[]>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder={t("placeholders.selectCustomerBranch")}
      allowClear={true}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      itemsStoreController={$items}
      optionDetails={true}
      {...props}
    />
  );
};
