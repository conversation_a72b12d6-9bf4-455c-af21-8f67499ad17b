import React, { FC, useEffect, useMemo, useState } from "react";

import { api } from "#businessLogic/api";
import { TDefaultLookupQueryParams, TIdNameModel } from "#businessLogic/models/common";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { withDebounce } from "#utils/helpers";
import { SelectProps } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

type BranchSelectComponentType = FC<SelectLookupPickerPropsTypes<TDefaultLookupQueryParams, TIdNameModel>> & {
  Multiple: typeof Multiple;
};

const BranchSelect: BranchSelectComponentType = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(api.branches.getBranchesLookup, new XHRState<TIdNameModel[]>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder={t("placeholders.selectBranch")}
      allowClear={true}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      itemsStoreController={$items}
      {...props}
    />
  );
};

type BranchSelectMultipleProps = {
  valueNames?: Array<string>;
  value?: Array<string>;
};

const Multiple: FC<BranchSelectMultipleProps & SelectProps> = (props) => {
  const { valueNames = [], value, ...restProps } = props;

  const { t } = useTranslation();

  const [isSearched, setIsSearched] = useState(false);

  const $items = useMemo(() => {
    return createXHRStore(api.branches.getBranchesLookup, new XHRState<TIdNameModel[]>([]));
  }, []);

  useEffect(() => {
    $items.effect({});
  }, []);

  const itemsState = useStore($items.store);

  const itemsData = useMemo(() => {
    const options = [...itemsState.data];

    valueNames.forEach((item, index) => {
      if (!itemsState.data.some((el) => String(el.id) === value[index])) {
        options.push({
          id: value[index],
          name: item,
        });
      }
    });

    return options;
  }, [itemsState.data, valueNames]);

  const onBlurBranches = () => {
    if (isSearched) {
      $items.effect({});
      setIsSearched(false);
    }
  };

  const onBranchesSearch = (search: string) => {
    withDebounce(() => {
      setIsSearched(!!search);
      $items.effect({ search });
    });
  };

  return (
    <SelectCustom
      showSearch
      placeholder={t("placeholders.selectBranches")}
      mode="multiple"
      loading={itemsState.loading}
      onBlur={onBlurBranches}
      onSearch={onBranchesSearch}
      filterOption={false}
      defaultActiveFirstOption={false}
      dropdownMatchSelectWidth={false}
      value={value}
      {...restProps}
    >
      {itemsData.map((item) => (
        <SelectCustom.Option value={item.id} key={item.id}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};

BranchSelect.Multiple = Multiple;

export { BranchSelect };
