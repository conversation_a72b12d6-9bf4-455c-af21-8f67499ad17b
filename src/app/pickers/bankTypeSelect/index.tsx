import React, { useEffect } from "react";

import { $bankTypes } from "#stores/public";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const BankTypeSelect: React.FC<SelectUIPropTypes> = (props) => {
  const { t } = useTranslation();

  const bankTypesState = useStore($bankTypes.store);

  useEffect(() => {
    if (!bankTypesState.data.length) {
      $bankTypes.effect();
    }
  }, []);

  return (
    <SelectCustom loading={bankTypesState.loading} placeholder={t("placeholders.selectBank")} {...props}>
      {bankTypesState.data.map((item) => (
        <SelectCustom.Option value={item.code} key={item.code}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
