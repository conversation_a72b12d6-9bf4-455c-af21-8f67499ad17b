import React, { useEffect } from "react";

import { $customerBranchTypes } from "#stores/public";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const CustomerBranchTypeSelect: React.FC<SelectUIPropTypes> = (props) => {
  const { t } = useTranslation();

  const customerBranchTypesState = useStore($customerBranchTypes.store);

  useEffect(() => {
    if (!customerBranchTypesState.data.length) {
      $customerBranchTypes.effect();
    }
  }, []);

  return (
    <SelectCustom loading={customerBranchTypesState.loading} placeholder={t("placeholders.selectType")} {...props}>
      {customerBranchTypesState.data.map((item) => (
        <SelectCustom.Option value={item.code} key={item.code}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
