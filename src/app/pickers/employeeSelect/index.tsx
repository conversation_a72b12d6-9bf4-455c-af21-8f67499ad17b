import React, { FC, useMemo } from "react";

import { api } from "#businessLogic/api";
import { IEmployeeLookupItemModel, TEmployeeLookupParams } from "#businessLogic/models/employee";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";

export const EmployeeSelect: FC<SelectLookupPickerPropsTypes<TEmployeeLookupParams, IEmployeeLookupItemModel>> = (
  props,
) => {
  const $items = useMemo(() => {
    return createXHRStore(api.employee.getEmployeesLookup, new XHRState<IEmployeeLookupItemModel[]>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder="Выберите сотрудника"
      allowClear={true}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      itemsStoreController={$items}
      {...props}
    />
  );
};
