import React, { FC, useMemo } from "react";

import { api } from "#businessLogic/api";
import { TCustomerBranchDevicesParams } from "#businessLogic/models/branchDevices";
import { IDeviceItemModel } from "#businessLogic/models/common";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type KkmSerialNumberSelectComponentType = FC<
  SelectLookupPickerPropsTypes<TCustomerBranchDevicesParams, IDeviceItemModel>
>;

export const CustomerBranchDeviceSelect: KkmSerialNumberSelectComponentType = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(api.branchDevices.getCustomerBranchDevices, new XHRState<Array<IDeviceItemModel>>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder={t("placeholders.selectSerialNumber")}
      allowClear={true}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      optionValue="id"
      optionName="serialNumber"
      itemsStoreController={$items}
      {...props}
    />
  );
};
