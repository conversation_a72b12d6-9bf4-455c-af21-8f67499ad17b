import React, { useEffect } from "react";

import { $districtItems } from "#stores/public";
import { SelectCustom, SelectUIPropTypes } from "#ui/selectCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const DistrictSelect: React.FC<SelectUIPropTypes & { regionId: number | undefined }> = (props) => {
  const { regionId, ...restProps } = props;

  const { t } = useTranslation();

  const districtItemsState = useStore($districtItems.store);

  useEffect(() => {
    if (regionId) {
      $districtItems.effect(regionId);
    }
  }, [regionId]);

  return (
    <SelectCustom
      loading={districtItemsState.loading}
      placeholder={t("placeholders.selectDistrict")}
      disabled={!regionId}
      {...restProps}
    >
      {districtItemsState.data.map((item) => (
        <SelectCustom.Option value={item.id} key={item.id}>
          {item.name}
        </SelectCustom.Option>
      ))}
    </SelectCustom>
  );
};
