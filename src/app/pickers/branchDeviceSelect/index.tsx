import React, { FC, useEffect, useMemo, useState } from "react";

import { api } from "#businessLogic/api";
import { TBranchDevicesLookupParams } from "#businessLogic/models/branchDevices";
import { IDeviceItemModel } from "#businessLogic/models/common";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";
import { SelectCustom, SelectLookupPickerPropsTypes } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type KkmSerialNumberSelectComponentType = FC<
  SelectLookupPickerPropsTypes<TBranchDevicesLookupParams, IDeviceItemModel>
>;

export const BranchDeviceSelect: KkmSerialNumberSelectComponentType = (props) => {
  const { t } = useTranslation();

  const $items = useMemo(() => {
    return createXHRStore(api.branchDevices.getBranchDevicesLookup, new XHRState<Array<IDeviceItemModel>>([]));
  }, []);

  return (
    <SelectCustom.Lookup
      placeholder={t("placeholders.selectSerialNumber")}
      allowClear={true}
      showSearch={true}
      filterOption={false}
      defaultActiveFirstOption={false}
      optionValue="id"
      optionName="serialNumber"
      itemsStoreController={$items}
      {...props}
    />
  );
};
