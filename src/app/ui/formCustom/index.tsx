import React, { <PERSON> } from "react";

import { VALIDATE_MESSAGES as defaultValidateMessages } from "#constants/index";
import { Button, Form } from "antd";
import { FormProps } from "antd/lib/form/Form";

import "./styles.scss";

type PropsTypes = {
  phantomSubmit?: boolean;
} & FormProps;

export const FormCustom: FC<PropsTypes> = (props) => {
  const { phantomSubmit = true, children, className, ...restProps } = props;

  return (
    <Form
      className={`custom-form${className ? ` ${className}` : ""}`}
      layout="vertical"
      validateMessages={defaultValidateMessages}
      {...restProps}
    >
      <>{children}</>
      {phantomSubmit && (
        <div className="phantomBtn">
          <Button htmlType="submit">Save</Button>
        </div>
      )}
    </Form>
  );
};
