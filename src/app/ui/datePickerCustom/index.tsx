import React, { FC } from "react";

import { RANGE_DATE_FORMAT, RANGE_DATE_TIME_FORMAT } from "#constants/index";
import { ELanguages } from "#localization/i18n.constants";
import { DatePicker, DatePickerProps } from "antd";
import { RangePickerProps } from "antd/lib/date-picker/generatePicker";
import moment from "moment/moment";
import { RangeValue } from "rc-picker/lib/interface";
import { useTranslation } from "react-i18next";

import "./styles.scss";

const pickerLocales: any = {
  [ELanguages.RU]: ELanguages.RU,
  [ELanguages.UZCY]: ELanguages.UZ,
  [ELanguages.UZ]: "uz-latn",
};

// в antd для datePicker используется uz-lat а для дат uz-cyrl
// костыль для решения проблемы с кирилицой когда выбрана латиница
const latUzLocale: any = {
  lang: {
    locale: "uz-latn",
    placeholder: "Select date",
    today: "Bugun",
    now: "Now",
    backToToday: "Back to today",
    ok: "OK",
    clear: "Clear",
    month: "Month",
    year: "Year",
    timeSelect: "Select time",
    dateSelect: "Select date",
    monthSelect: "Choose a month",
    yearSelect: "Choose a year",
    decadeSelect: "Choose a decade",
    yearFormat: "YYYY",
    dateFormat: "M/D/YYYY",
    dayFormat: "D",
    dateTimeFormat: "M/D/YYYY HH:mm:ss",
    monthFormat: "MMMM",
    monthBeforeYear: true,
    previousMonth: "Previous month (PageUp)",
    nextMonth: "Next month (PageDown)",
    previousYear: "Last year (Control + left)",
    nextYear: "Next year (Control + right)",
    previousDecade: "Last decade",
    nextDecade: "Next decade",
    previousCentury: "Last century",
    nextCentury: "Next century",
  },
};

type DatePickerType = FC<DatePickerProps> & {
  Range: typeof Range;
};

const monthCellRenderFunc = (date: any) => {
  const loc = localStorage.getItem("i18nextLng") || ELanguages.RU;
  const monthName = date.locale(pickerLocales[loc]).format("MMMM");

  return <div className="ant-picker-cell-inner">{monthName}</div>;
};

const DatePickerCustom: DatePickerType = (props) => {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language;

  return (
    <DatePicker
      placeholder={t("placeholders.selectDate")}
      monthCellRender={monthCellRenderFunc}
      locale={currentLanguage === ELanguages.UZ ? latUzLocale : undefined}
      // @ts-ignore
      showToday={false}
      {...props}
    />
  );
};

type RangePropsTypes = Omit<RangePickerProps<moment.Moment>, "onChange"> & {
  onChange: (params: { from: string | undefined; to: string | undefined }) => void;
  from: string | undefined;
  to: string | undefined;
  allowClear?: boolean;
  format?: string;
};

const Range: FC<RangePropsTypes> = (props) => {
  const { onChange, from, to, allowClear = true, format = RANGE_DATE_TIME_FORMAT, ...restProps } = props;

  const { t, i18n } = useTranslation();

  const currentLanguage = i18n.language;

  const onDateRange = (values: RangeValue<moment.Moment>) => {
    if (!values) {
      onChange({
        from: undefined,
        to: undefined,
      });
    } else {
      const from = moment(values[0]).startOf("day").format(format);
      const to = moment(values[1]).endOf("day").format(format);

      onChange({
        from,
        to,
      });
    }
  };

  return (
    <div className="custom-calendar-range-picker">
      <DatePicker.RangePicker
        placeholder={[t("startDate"), t("endDate")]}
        locale={currentLanguage === ELanguages.UZ ? latUzLocale : undefined}
        allowClear={allowClear}
        {...restProps}
        format={RANGE_DATE_FORMAT}
        monthCellRender={monthCellRenderFunc}
        value={from && to ? [moment(from), moment(to)] : undefined}
        onChange={onDateRange}
      />
    </div>
  );
};

DatePickerCustom.Range = Range;

export { DatePickerCustom };
