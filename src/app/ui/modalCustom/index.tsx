import React, { FC, PropsWithChildren, ReactNode, forwardRef } from "react";

import { CloseIcon } from "#assets/icons";
import { ErrorResponse } from "#types/api";
import { SpinCustom } from "#ui/spinCustom";
import { Alert, Modal, ModalProps } from "antd";

import "./styles.scss";

type TModalCustomProps = FC<ModalProps> & {
  Header: typeof Header;
  Footer: typeof Footer;
  Error: typeof Error;
  Loading: typeof Loading;
  Middle: typeof Middle;
  Title: typeof Title;
  Buttons: typeof Buttons;
};

const ModalCustom: TModalCustomProps = (props) => {
  const { className, width = 450, footer = false, ...restProps } = props;
  let classesCompose = "custom-modal";

  if (className) {
    classesCompose += ` ${className}`;
  }

  return (
    <Modal
      className={classesCompose}
      wrapClassName="custom-modal-wrap"
      width={width}
      footer={footer}
      destroyOnClose={true}
      closeIcon={<CloseIcon />}
      maskClosable={false}
      {...restProps}
    />
  );
};

type HeaderProps = {
  children?: ReactNode;
};

const Header = (props: HeaderProps) => {
  const { children } = props;

  return <div className="custom-modal__header">{children}</div>;
};

const Footer: FC<PropsWithChildren<unknown>> = (props) => {
  return <div className="custom-modal__footer">{props.children}</div>;
};

const Middle = forwardRef<HTMLDivElement, React.HTMLProps<HTMLDivElement>>((props, ref) => {
  return (
    <div className="custom-modal__middle u-fancy-scrollbar" ref={ref} {...props}>
      {props.children}
    </div>
  );
});

const Title: FC<PropsWithChildren<unknown>> = (props) => {
  return <div className="custom-modal__title">{props.children}</div>;
};

type TLoadingProps = {
  show?: boolean;
};

const Loading: FC<TLoadingProps> = ({ show }) => {
  if (show) {
    return (
      <div className="modal-loader">
        <SpinCustom size="large" />
      </div>
    );
  }

  return null;
};

type TErrorProps = {
  error: ErrorResponse | null;
};

const Error: FC<TErrorProps> = ({ error }) => {
  if (!error) return null;

  const message = (
    <div>
      <div>Title: {error.title}</div>
      <div>Message: {error.message}</div>
    </div>
  );

  return <Alert className="custom-modal__error" message={message} type="error" />;
};

type TButtons = FC<{
  place?: "center" | "right";
  children?: ReactNode;
}> & {
  Col: typeof ButtonCol;
};

const Buttons: TButtons = (props) => {
  const { place = "center" } = props;

  return <div className={`custom-modal__buttons custom-modal__buttons--${place}`}>{props.children}</div>;
};

const ButtonCol: FC<PropsWithChildren<unknown>> = (props) => {
  return <div className={`custom-modal__buttons__col`}>{props.children}</div>;
};

ModalCustom.Header = Header;
ModalCustom.Footer = Footer;
ModalCustom.Middle = Middle;
ModalCustom.Title = Title;
ModalCustom.Loading = Loading;
ModalCustom.Error = Error;
ModalCustom.Buttons = Buttons;
Buttons.Col = ButtonCol;

export { ModalCustom };
