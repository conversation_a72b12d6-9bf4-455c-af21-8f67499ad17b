@import "src/styles/variables.scss";

.ant-modal-wrap, .ant-modal-mask {
  z-index: 1050;
}

.custom-modal {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  top: 0;
  padding: 0;

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    max-height: 100%;
    max-width: 100%;
    padding: 20px;
    flex-grow: 1;
  }

  .ant-modal-close {
    top: 20px;
    right: 20px;

    .ant-modal-close-x {
      width: 24px;
      height: 24px;
      line-height: 25px;
      color: $primary;
      border: 1px solid rgba($primary, 0.16);
      border-radius: 6px;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

      &:hover {
        border-color: rgba($primary, 0.16);
        background-color: rgba($primary, 0.07);
      }

      svg {
        width: 14px;
        height: 14px;
      }
    }
  }

  .ant-modal-body {
    height: 100%;
    display: flex;
    padding: 0;
    overflow-x: hidden;
    flex-direction: column;
  }

  &-wrap {
    max-height: 100%;
    padding: 20px 0 40px;
  }

  &__header {
    padding-right: 40px;
    margin-bottom: 20px;
  }

  &__title {
    font-size: 18px;
    line-height: 22px;
    font-weight: 500;
  }

  &__middle {
    padding: 0 10px 0 10px;
    overflow-y: auto;
    scroll-behavior: smooth;
    margin: 0 -10px 0 -10px;
    position: relative;
    flex-grow: 1;
  }

  &__footer {
    padding-top: 15px;
    text-align: right;
  }

  &__error {
    margin: 0 0 20px;
  }
}

.custom-modal__buttons {
  display: flex;
  justify-content: center;
  margin: 0 -8px;
  column-gap: 10px;

  &__col {
    width: 50%;
    padding: 0 8px;
  }

  .custom-btn {
    width: 100%;
    padding-left: 34px;
    padding-right: 34px;
  }

  &-right {
    justify-content: flex-end;
  }
}


.modal-loader {
  position: absolute;
  z-index: 100;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.5);
}
