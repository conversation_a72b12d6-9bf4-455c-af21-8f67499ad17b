import React, { ReactNode, useEffect, useMemo, useState } from "react";

import { XHRState } from "#constructors/index";
import { HandlerType } from "#core/stateManager/types/handler";
import { CreateStoreReturnType } from "#core/stateManager/types/store";
import { SelectOptionType } from "#types/common";
import { withDebounce } from "#utils/helpers";
import { Select, SelectProps } from "antd";
import { useStore } from "effector-react";

import "./styles.scss";

const getOptions = (items: Array<any>, defaultOption: Record<any, any>, optionValue: string) => {
  const newArr: Array<any> = [];
  let isExist = false;

  items.forEach((item) => {
    newArr.push(item);

    if (String(item[optionValue]) === String(defaultOption[optionValue])) {
      isExist = true;
    }
  });

  if (!isExist) {
    newArr.push(defaultOption);
  }

  return newArr;
};

const isFilledDependencies = (deps: Array<any>) => {
  let filled = true;

  deps.forEach((item) => {
    if (!item) {
      filled = false;
    }
  });

  return filled;
};

type Single = {
  mode?: undefined;
  onChange?: (value: any, option: SelectOptionType<any>) => void;
};

type Tags = {
  mode: "tags";
  onChange?: (value: any[], option: SelectOptionType<any>[]) => void;
};

type Multi = {
  mode: "multiple";
  onChange?: (value: any[], option: SelectOptionType<any>[]) => void;
};

export type SelectUIPropTypes = Omit<SelectProps, "mode" | "onChange"> & (Single | Tags | Multi);

type SelectCustomComponent = {
  (props: SelectUIPropTypes): JSX.Element;
  Option: typeof Select.Option;
  Lookup: typeof SelectLookup;
};

export const SelectCustom: SelectCustomComponent = ({ mode, className, ...restProps }: SelectUIPropTypes) => {
  let classesCompose = "custom-select";

  if (className) {
    classesCompose += ` ${className}`;
  }

  // @ts-ignore
  return <Select mode={mode} className={classesCompose} {...restProps} />;
};

type RequestParams = { [key: string]: string | string[] | number | number[] | undefined | boolean };

export type SelectLookupPickerPropsTypes<Request, TItem> = Omit<SelectProps, "onChange"> & {
  value?: string | number | string[] | number[];
  optionValue?: keyof TItem;
  optionName?: keyof TItem;
  autoSingleSelect?: boolean;
  additionalOption?: { id: string | number | null; name: string };
  defaultOption?: null | { [key: string]: string | number };
  disabledOptionsIds?: { [key: string]: boolean };
  optionDetails?: boolean;
  requestParams?: Request;
  dependencies?: Array<string | number | undefined>;
  onChange?: (value: any, option?: any) => void;
};

type SelectLookupPropsTypes<S = any, P = any, R = any> = SelectLookupPickerPropsTypes<P, S> & {
  itemsStoreController: CreateStoreReturnType<HandlerType<P, R>, XHRState<S[]>>;
  optionChildren?: (prop: { item: S }) => ReactNode;
};

const SelectLookup = <Store = any, QueryParams extends RequestParams = any, Response = any>(
  props: SelectLookupPropsTypes<Store, QueryParams, Response>,
) => {
  const {
    itemsStoreController,
    value,
    autoSingleSelect,
    additionalOption,
    defaultOption,
    onChange,
    disabledOptionsIds = {},
    optionValue = "id",
    optionName = "name",
    optionDetails,
    requestParams = null,
    dependencies = [],
    optionChildren,
    disabled,
    ...restProps
  } = props;

  const params = requestParams ? requestParams : null;

  const [isSearched, setIsSearched] = useState(false);
  const [isSingleSelected, setIsSingleSelected] = useState(false);
  const [showDefaultOption, setShowDefaultOption] = useState(true);

  const itemsState = useStore(itemsStoreController.store);

  useEffect(() => {
    if (!dependencies.length) {
      itemsStoreController.effect(params as QueryParams);
    }
  }, []);

  useEffect(() => {
    if (dependencies.length) {
      const filled = isFilledDependencies(dependencies);

      if (filled) {
        itemsStoreController.effect(params as QueryParams);
      } else if (itemsState.data.length) {
        itemsStoreController.reset();
      }
    }
  }, dependencies);

  useEffect(() => {
    if (autoSingleSelect && !value && !isSingleSelected && itemsState.data.length === 1) {
      const item: any = itemsState.data[0];
      const val = item[optionValue];
      const option: any = {
        "data-option": optionDetails ? item : null,
        children: optionChildren ? optionChildren({ item }) : item[optionName],
      };

      onChange && onChange(val, option);
      setIsSingleSelected(true);
    }
  }, [itemsState.data]);

  const onSearch = (search: string) => {
    withDebounce(() => {
      setIsSearched(!!search);

      itemsStoreController.effect({ search, ...(params as QueryParams) });
    });
  };

  const onValueChange = (val: any, option: any) => {
    if (isSearched) {
      setIsSearched(false);
      itemsStoreController.effect(params as QueryParams);
    }

    if (showDefaultOption) {
      setShowDefaultOption(false);
    }

    onChange && onChange(val, option);
  };

  const selectOptions = useMemo(() => {
    const items = itemsState.data;
    const newArr: Array<ReactNode> = [];
    let isExist = false;

    items.forEach((item: any) => {
      newArr.push(
        <Select.Option
          value={item[optionValue]}
          key={item[optionValue]}
          data-option={optionDetails ? item : null}
          disabled={itemsState.loading || !!disabledOptionsIds[item[optionValue]]}
        >
          {optionChildren ? optionChildren({ item }) : item[optionName]}
        </Select.Option>,
      );

      if (defaultOption && String(item[optionValue]) === String(defaultOption[optionValue])) {
        isExist = true;
      }
    });

    if (!isExist && defaultOption) {
      newArr.push(
        <Select.Option
          value={defaultOption[optionValue]}
          key={defaultOption[optionValue]}
          data-option={optionDetails ? defaultOption : null}
          disabled={itemsState.loading || !!disabledOptionsIds[defaultOption[optionValue as string]]}
        >
          {optionChildren ? optionChildren({ item: defaultOption as Store }) : defaultOption[optionName]}
        </Select.Option>,
      );
    }

    return newArr;
  }, [itemsState.data, itemsState.loading, disabledOptionsIds, defaultOption, optionValue, optionName, optionDetails]);

  return (
    <Select
      className="custom-select"
      loading={itemsState.loading}
      onSearch={onSearch}
      onChange={onValueChange}
      disabled={disabled || (dependencies.length ? !isFilledDependencies(dependencies) : false)}
      value={value ? value : undefined}
      {...restProps}
    >
      {additionalOption && !isSearched && (
        <Select.Option value={additionalOption.id}>{additionalOption.name}</Select.Option>
      )}
      {selectOptions}
    </Select>
  );
};

SelectCustom.Option = Select.Option;
SelectCustom.Lookup = SelectLookup;
