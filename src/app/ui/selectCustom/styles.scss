@import "src/styles/variables.scss";

.custom-select {
  box-shadow: $formItemBoxShadow;

  .anticon {
    color: $textColor;
  }

  .ant-select-clear {
    right: 10px;
  }

  .ant-select-arrow {
    right: 8px;
    width: 14px;
    height: 14px;
    background-image: url("../../../assets/images/arrow-down.svg");

    svg {
      display: none;
    }
  }

  &.ant-select-multiple {
    .ant-select-selection-item {
      background: $gray02;
      border: none;
      border-radius: 3px;
      margin-top: 0;
      margin-bottom: 0;
      height: 28px;
      align-items: center;
    }

    .ant-select-selection-search {
      margin-left: 0;
    }
  }
}