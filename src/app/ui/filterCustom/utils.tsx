import { FilterWrapper, QueryParamsType, TFilterItem, TFilterUIItem } from "#ui/filterCustom/models";

export function isFilterWrapper<Q extends QueryParamsType, A extends QueryParamsType>(
  item: TFilterUIItem<Q, A>,
): item is FilterWrapper<Q, A> {
  return !!(item as FilterWrapper<Q, A>).wrapper;
}

export const isString = (param: unknown): param is string => {
  return typeof param === "string";
};

export const getSelectedFilterElements = <Q extends QueryParamsType, A extends QueryParamsType>(
  items: TFilterUIItem<Q, A>[],
  queryParams: Q,
): TFilterItem<Q, A>[] => {
  const filterItems: TFilterItem<Q, A>[] = [];

  const hasValue = (value: unknown) => {
    if (!value) {
      return false;
    }

    return !(Array.isArray(value) && !value.length);
  };

  items.forEach((item) => {
    if (isFilterWrapper(item)) {
      filterItems.push(...getSelectedFilterElements(item.children, queryParams));
    } else {
      const key = item.key;
      if (isString(key)) {
        const val = queryParams[key];
        if (hasValue(val)) {
          filterItems.push(item);
        }
      }

      if (Array.isArray(key)) {
        key.forEach((k) => {
          const val = queryParams[k];
          if (hasValue(val)) {
            filterItems.push(item);
          }
        });
      }
    }
  });

  return Array.from(new Set(filterItems)); // чтобы убрать дубликаты, которые могут возникнуть из-за key[]
};

export const setUndefinedValue = (params: QueryParamsType) => {
  const keys = Object.keys(params);
  keys.forEach((key) => {
    params[key] = undefined;
  });
};
