import React, { ReactNode, useMemo } from "react";

import { useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { FilterOnChangeType } from "#types/common";
import { ButtonCustom } from "#ui/buttonCustom";
import { DrawerCustom } from "#ui/drawerCustom";
import { FilterUIDrawerContent } from "#ui/filterCustom/filterDrawerContent";
import { QueryParamsType, TFilterItem, TFilterUIItem } from "#ui/filterCustom/models";
import { getSelectedFilterElements, isFilterWrapper } from "#ui/filterCustom/utils";
import { Tooltip } from "antd";
import cn from "classnames";
import { useTranslation } from "react-i18next";

import "./styles.scss";

export type TFilterUIItems<QueryParams extends QueryParamsType, AdditionalParams extends QueryParamsType> = Array<
  TFilterUIItem<QueryParams, AdditionalParams>
>;

export type FilterUIPropsTypes<QueryParams extends QueryParamsType, AdditionalParams extends QueryParamsType> = {
  className?: string;
  onFilterChange: FilterOnChangeType<QueryParams, AdditionalParams>;
  clearQueryParams?: () => void;
  queryParams?: QueryParams;
  additionalParams?: AdditionalParams;
  numberOfFilterItemsToRender?: number;
  items: TFilterUIItems<QueryParams, AdditionalParams>;
  showTags?: boolean;
};

export const FilterCustom = <QueryParams extends QueryParamsType, AdditionalParams extends QueryParamsType = {}>(
  props: FilterUIPropsTypes<QueryParams, AdditionalParams>,
) => {
  const {
    className,
    queryParams,
    clearQueryParams,
    onFilterChange,
    additionalParams,
    numberOfFilterItemsToRender = 1,
    items,
    showTags = true,
  } = props;

  const { t } = useTranslation();

  const filterModalControl = useModalControl();

  const itemsToRender = useMemo(
    () => items.slice(0, numberOfFilterItemsToRender),
    [queryParams, numberOfFilterItemsToRender],
  );
  // @ts-ignore
  const selectedFilterItems = useMemo(() => getSelectedFilterElements(items, queryParams), [items, queryParams]);

  const clearFilter = (item: TFilterItem<QueryParams, AdditionalParams>) => {
    const keys: Record<string, any> = {};
    if (Array.isArray(item.key)) {
      item.key.forEach((key) => {
        keys[key as string] = undefined;
      });
    } else {
      keys[item.key as string] = undefined;
    }

    onFilterChange(
      // @ts-ignore
      {
        ...queryParams,
        ...keys,
      },
      {
        ...additionalParams,
        ...(item.additionalKey ? { [item.additionalKey]: undefined } : {}),
      },
    );
  };

  const drawFilterItem = (item: TFilterUIItem<QueryParams, AdditionalParams>, inDrawer = false): ReactNode => {
    if (isFilterWrapper(item)) {
      return item.wrapper(<>{item.children.map((item) => drawFilterItem(item))}</>);
    } else {
      return (
        <div
          className={cn(
            { "filter-block__item": !inDrawer, "search-filter-input": item.fieldType === "search" },
            item.className,
          )}
          key={String(item.key)}
        >
          {item.render({
            // @ts-ignore
            params: queryParams,
            // @ts-ignore
            additional: additionalParams,
            // @ts-ignore
            onChange: onFilterChange,
          })}
        </div>
      );
    }
  };

  return (
    <>
      <div className={cn("filter-block", className)}>
        {itemsToRender.map((item) => {
          return drawFilterItem(item, false);
        })}

        {items.length - numberOfFilterItemsToRender > 0 && (
          <Tooltip placement="top" title={t("advancedFilter")}>
            <ButtonCustom type="primary" size="small" onClick={() => filterModalControl.openModal()}></ButtonCustom>
          </Tooltip>
        )}

        {clearQueryParams && (
          <div className="filter-block__buttons">
            <ButtonCustom size="small" type="default" onClick={() => clearQueryParams()}>
              {t("reset", { ns: namespaces.buttons })}
            </ButtonCustom>
            <ButtonCustom
              size="small"
              type="primary"
              // @ts-ignore
              onClick={() => onFilterChange(queryParams, additionalParams)}
            >
              {t("refresh", { ns: namespaces.buttons })}
            </ButtonCustom>
          </div>
        )}
        {items.length - numberOfFilterItemsToRender > 0 && (
          <DrawerCustom
            open={filterModalControl.visible}
            onCancel={filterModalControl.closeModal}
            afterClose={filterModalControl.resetModal}
          >
            <FilterUIDrawerContent
              onFilterChange={onFilterChange}
              modalControl={filterModalControl}
              queryParams={queryParams}
              additionalParams={additionalParams}
              items={items}
            />
          </DrawerCustom>
        )}
      </div>
      {/*{showTags && selectedFilterItems.length > 0 && (*/}
      {/*  <div className="filter-block__tags">*/}
      {/*    {selectedFilterItems.map((item) => (*/}
      {/*      <div className="filter-block__tags__item" key={String(item.key)}>*/}
      {/*        {item.label || item.key}*/}
      {/*        <div*/}
      {/*          className="filter-block__tags__item__remove"*/}
      {/*          // @ts-ignore*/}
      {/*          onClick={() => clearFilter(item)}*/}
      {/*        >*/}
      {/*          X*/}
      {/*        </div>*/}
      {/*      </div>*/}
      {/*    ))}*/}
      {/*  </div>*/}
      {/*)}*/}
    </>
  );
};
