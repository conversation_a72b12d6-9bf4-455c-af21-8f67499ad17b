import React, { MutableRefObject, Ref, forwardRef } from "react";

import cn from "classnames";

import { QueryParamsType, TFilterItem } from "#ui/filterCustom/models";

import { useForceUpdate } from "#hooks/useForceUpdate";

type TRefState<Q extends QueryParamsType, A extends QueryParamsType> = Ref<{
  query: Q;
  additional: A;
}>;

export type TFilterRefState<Q extends QueryParamsType, A extends QueryParamsType> = MutableRefObject<{
  query: Q;
  additional: A;
}>;

type Props<Q extends QueryParamsType, A extends QueryParamsType> = {
  item: TFilterItem<Q, A>;
  update: () => void;
};

export const FilterUiDrawerItem = forwardRef(
  <Q extends QueryParamsType, A extends QueryParamsType>(props: Props<Q, A>, ref: TRefState<Q, A>) => {
    const { item, update } = props;
    const mutableRef = ref as TFilterRefState<Q, A>;

    const forceUpdate = useForceUpdate();

    const onChangeFilter = (queryParams: Partial<Q>, additionalParams?: Partial<A>) => {
      mutableRef.current = {
        query: {
          ...mutableRef.current.query,
          ...queryParams,
          page: undefined,
        },
        additional: {
          ...mutableRef.current.additional,
          ...additionalParams,
        },
      };

      const keys = Object.keys(queryParams);

      forceUpdate();

      // в большинстве случаев когда передан несколько ключей в queryParams, то там есть зависимость между ними, и надо обновить их тоже
      if (keys.length > 1) {
        update(); // forceUpdate родителя
      }
    };

    return (
      <div className={cn({ "search-filter-input": item.fieldType === "search" })}>
        {item.label ? <div className="filter-block__item__label">{item.label}</div> : null}
        {item.render({
          params: mutableRef.current.query,
          additional: mutableRef.current.additional,
          onChange: onChangeFilter,
        })}
      </div>
    );
  },
);

FilterUiDrawerItem.displayName = "FilterUiDrawerItem";
