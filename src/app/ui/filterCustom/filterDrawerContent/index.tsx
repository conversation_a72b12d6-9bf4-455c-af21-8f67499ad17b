import React, { ReactNode, useRef } from "react";

import { useForceUpdate } from "#hooks/useForceUpdate";
import { ModalControlType } from "#hooks/useModalControl";
import { FilterOnChangeType } from "#types/common";
import { ButtonCustom } from "#ui/buttonCustom";
import { FilterUiDrawerItem } from "#ui/filterCustom/filterDrawerContent/filterUIItem";
import { QueryParamsType, TFilterUIItem } from "#ui/filterCustom/models";
import { isFilterWrapper, setUndefinedValue } from "#ui/filterCustom/utils";
import { ModalCustom } from "#ui/modalCustom";
import { Col, Row } from "antd";

import "./styles.scss";

type PropsTypes<Q extends QueryParamsType, A extends QueryParamsType> = {
  modalControl: ModalControlType;
  onFilterChange?: FilterOnChangeType<Q, A>;
  queryParams?: Q;
  additionalParams?: A;
  items: Array<TFilterUIItem<Q, A>>;
};

export const FilterUIDrawerContent = <Q extends QueryParamsType, A extends QueryParamsType>(
  props: PropsTypes<Q, A>,
) => {
  const { onFilterChange, modalControl, queryParams, additionalParams, items } = props;

  const forceUpdate = useForceUpdate();

  const refState = useRef({
    query: { ...queryParams } as Q,
    additional: { ...additionalParams } as A,
  });

  const drawFilterItem = (item: TFilterUIItem<Q, A>): ReactNode => {
    if (isFilterWrapper(item)) {
      return item.wrapper(<>{item.children.map((item) => drawFilterItem(item))}</>);
    } else {
      const key = String(item.key);

      return (
        <FilterUiDrawerItem
          key={key}
          ref={refState}
          update={forceUpdate}
          // @ts-ignore
          item={item}
        />
      );
    }
  };

  const clearLocalParams = () => {
    setUndefinedValue(refState.current.query);
    setUndefinedValue(refState.current.additional);
  };

  const syncParams = () => {
    onFilterChange?.({ ...refState.current.query }, refState.current.additional);
  };

  const handleApplyQueryParams = () => {
    syncParams();
    modalControl.closeModal();
  };

  const handleClearQueryParams = () => {
    clearLocalParams();
    forceUpdate();
  };

  return (
    <>
      <ModalCustom.Header>
        <div className="modalTitle">
          <ModalCustom.Title>
            <Row justify="space-between" align="middle">
              <Col>Расширенный фильтр</Col>
              <Col>
                <ButtonCustom type="default" onClick={handleClearQueryParams} size="small">
                  Сбросить
                </ButtonCustom>
              </Col>
            </Row>
          </ModalCustom.Title>
        </div>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        <div className={`groupContainer filter-drawer-content`}>{items.map((item) => drawFilterItem(item))}</div>
      </ModalCustom.Middle>

      <ModalCustom.Footer>
        <Row justify="space-between" gutter={16}>
          <Col span={12}>
            <ButtonCustom fullWidth type="default" onClick={modalControl.closeModal}>
              Закрыть
            </ButtonCustom>
          </Col>
          <Col span={12}>
            <ButtonCustom fullWidth type="primary" onClick={handleApplyQueryParams}>
              Применить
            </ButtonCustom>
          </Col>
        </Row>
      </ModalCustom.Footer>
    </>
  );
};
