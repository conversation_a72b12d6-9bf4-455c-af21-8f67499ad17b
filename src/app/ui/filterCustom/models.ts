import { ReactNode } from "react";

export type QueryParamsType = {
  [key: string]: string | number | null | string[] | number[] | boolean | undefined;
};

export type TFilterUIItem<Q extends QueryParamsType, A extends QueryParamsType> =
  | TFilterItem<Q, A>
  | FilterWrapper<Q, A>;

export type FilterWrapper<QueryParams extends QueryParamsType, AdditionalParams extends QueryParamsType> = {
  wrapper: (children: ReactNode) => ReactNode;
  children: Array<TFilterUIItem<QueryParams, AdditionalParams>>;
};

type TFilterItemRenderParams<QueryParams extends QueryParamsType, AdditionalParams extends QueryParamsType> = {
  params: QueryParams;
  additional: AdditionalParams;
  /**
   * Не разложите queryParams при вызове этой функции, это для оптимизации рендеров
   */
  onChange: (params: Partial<QueryParams>, additionalParams?: Partial<AdditionalParams>) => void;
  // updateParams?: (params: Partial<QueryParams>, additionalParams?: Partial<AdditionalParams>) => void // Вызовем только тогда когда нужно обновить значение какую-то
};

export type TFilterItem<QueryParams extends QueryParamsType, AdditionalParams extends QueryParamsType> = {
  key: keyof QueryParams | Array<keyof QueryParams>;
  render: (param: TFilterItemRenderParams<QueryParams, AdditionalParams>) => ReactNode;
  additionalKey?: keyof AdditionalParams;
  label?: ReactNode;
  className?: string;
  fieldType?: "search";
};
