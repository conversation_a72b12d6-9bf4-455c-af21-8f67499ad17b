import React, { FC, ReactNode, useMemo } from "react";

import { ArrowLeftIcon } from "#assets/icons";
import { namespaces } from "#localization/i18n.constants";
import { ButtonCustom } from "#ui/buttonCustom";
import { useTranslation } from "react-i18next";
import { Link, useHistory } from "react-router-dom";

import styles from "./styles.module.scss";

type PropsTypes = {
  children: ReactNode;
};

type TContentHeaderProps = FC<PropsTypes> & {
  Left: typeof ContentHeaderLeft;
  Right: typeof ContentHeaderRight;
  Title: typeof ContentTitle;
};

const ContentHeader: TContentHeaderProps = (props) => {
  const { children } = props;

  return <div className={styles.contentHeader}>{children}</div>;
};

type ContentTitlePropsTypes = {
  title: ReactNode;
  children?: ReactNode;
  backPath?: string;
  goBack?: boolean;
};

const ContentTitle: FC<ContentTitlePropsTypes> = (props) => {
  const { title, children, backPath, goBack } = props;

  const { t } = useTranslation();

  const history = useHistory();

  const element = useMemo(() => {
    if (!backPath && !goBack) {
      return null;
    }

    const content = (
      <ButtonCustom size="small" className="back-btn" {...(goBack ? { onClick: history.goBack } : {})}>
        <ArrowLeftIcon /> {t("back", { ns: namespaces.buttons })}
      </ButtonCustom>
    );

    if (backPath) {
      return <Link to={backPath}>{content}</Link>;
    }

    return content;
  }, [goBack, backPath]);

  return (
    <div className={styles.contentTitle}>
      {element}
      <h1>{title}</h1>
      {children}
    </div>
  );
};

type ContentHeaderLeftPropsTypes = {
  children: ReactNode;
};

export const ContentHeaderLeft: FC<ContentHeaderLeftPropsTypes> = (props) => {
  const { children } = props;

  return <div className={styles.contentHeaderLeft}>{children}</div>;
};

type ContentHeaderRightPropsTypes = {
  children: ReactNode;
};

export const ContentHeaderRight: FC<ContentHeaderRightPropsTypes> = (props) => {
  const { children } = props;

  return <div className={styles.contentHeaderRight}>{children}</div>;
};

ContentHeader.Left = ContentHeaderLeft;
ContentHeader.Right = ContentHeaderRight;
ContentHeader.Title = ContentTitle;

export { ContentHeader };
