import React, { ReactNode } from "react";

import "./styles.scss";

export type TStatusColors = "green" | "gray" | "red" | "yellow" | "orange" | "purple" | "blue";

type PropsType = {
  getStatusColor?: (status: string) => TStatusColors;
  status: string;
  children: ReactNode;
};

const getDefaultStatusColor = (status: string): TStatusColors => {
  switch (status) {
    case "DEFAULT":
      return "blue";
    case "ACTIVE":
    case "CONNECTED":
    case "ACCEPT":
      return "green";
    case "REJECTED":
    case "MISTAKE":
    case "NOT_CONNECTED":
      return "red";
    case "PENDING":
      return "yellow";
    case "IN_PROGRESS":
      return "orange";
    default:
      return "gray";
  }
};

export const StatusCustom = (props: PropsType) => {
  const { status, children, getStatusColor } = props;

  return (
    <div
      className={`status-block status-block__${
        getStatusColor ? getStatusColor(status) : getDefaultStatusColor(status)
      }`}
    >
      {children}
    </div>
  );
};
