import React, { FC, ReactNode } from "react";

import { namespaces } from "#localization/i18n.constants";
import { ErrorResponse } from "#types/api";
import { ButtonCustom } from "#ui/buttonCustom";
import { SpinCustom } from "#ui/spinCustom";
import { Result } from "antd";
import cn from "classnames";
import { useTranslation } from "react-i18next";

import "./styles.scss";

type PropsTypes = {
  loading?: boolean;
  error?: ErrorResponse | null;
  refresh?: () => void;
  children: ReactNode;
  className?: string;
};

export const ContentCustom: FC<PropsTypes> = (props) => {
  const { loading, error, children, refresh, className } = props;

  const { t } = useTranslation();

  const onRefresh = () => {
    if (refresh) {
      refresh();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className={cn("content-custom u-fancy-scrollbar", className)}>
      {loading && !error && (
        <div className="abs-loader">
          <SpinCustom />
        </div>
      )}

      {error ? (
        <div className="content-custom__error">
          <Result
            status={error.status >= 500 ? "500" : error.status === 404 ? "404" : "403"}
            title={`${error.status}: ${error.title}`}
            subTitle={error.detail}
            extra={
              <ButtonCustom type="primary" onClick={onRefresh} loading={loading}>
                {t("refresh", { ns: namespaces.buttons })}
              </ButtonCustom>
            }
          />
        </div>
      ) : (
        children
      )}
    </div>
  );
};
