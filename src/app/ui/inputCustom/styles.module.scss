@import "src/styles/variables.scss";

:global(.custom-input) {
  box-shadow: $formItemBoxShadow;
}

.searchInput {

  :global(.ant-input-suffix) {
    :global(.ant-input-clear-icon) {
      display: none;
      width: 16px;
      height: 12px;
      margin: 0;
      line-height: 0;
      text-align: center;

      :global(.anticon) {
        color: $textColor;
      }
    }
  }

  &:hover {
    :global(.ant-input-suffix) {
      :global(.ant-input-clear-icon) {
        display: block;

        & + svg {
          display: none;
        }

        &:global(.ant-input-clear-icon-hidden) {
          display: none;

          & + svg {
            display: block;
          }
        }
      }
    }
  }
}