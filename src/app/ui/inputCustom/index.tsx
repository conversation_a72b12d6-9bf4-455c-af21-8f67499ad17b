import React, { ChangeEvent, FC, useEffect, useState } from "react";

import { SearchIcon } from "#assets/icons";
import { withDebounce } from "#utils/helpers";
import { Input, InputProps } from "antd";
import { useTranslation } from "react-i18next";
import InputMask, { Props as TInputMaskProps } from "react-input-mask";

import styles from "./styles.module.scss";

type TInputCustomProps = FC<InputProps> & {
  Search: typeof SearchInput;
  TextArea: typeof Input.TextArea;
  Mask: typeof CustomInputMask;
};

const InputCustom: TInputCustomProps = (props) => {
  const { className, ...restProps } = props;

  return <Input className={`custom-input${className ? ` ${className}` : ""}`} {...restProps} />;
};

type TSearchProps = {
  onChange: (value: string | undefined) => void;
  value?: string;
} & Omit<InputProps, "onChange" | "value">;

const SearchInput: FC<TSearchProps> = (props) => {
  const { onChange, value, ...restProps } = props;

  const { t } = useTranslation();

  const [searchValue, setSearchValue] = useState(value);

  useEffect(() => {
    if (value !== searchValue) {
      setSearchValue(value);
    }
  }, [value]);

  const onFilterSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value;
    setSearchValue(search);

    withDebounce(() => {
      onChange(search ? search : undefined);
    });
  };

  return (
    <Input
      size="small"
      className={`${styles.searchInput} custom-input`}
      value={searchValue}
      placeholder={t("search")}
      onChange={onFilterSearchChange}
      allowClear
      suffix={<SearchIcon />}
      {...restProps}
    />
  );
};

const CustomInputMask: FC<TInputMaskProps> = (props) => {
  const { className, ...restProps } = props;

  return <InputMask className={`ant-input custom-input${className ? ` ${className}` : ""}`} {...restProps} />;
};

InputCustom.Search = SearchInput;
InputCustom.TextArea = Input.TextArea;
InputCustom.Mask = CustomInputMask;

export { InputCustom };
