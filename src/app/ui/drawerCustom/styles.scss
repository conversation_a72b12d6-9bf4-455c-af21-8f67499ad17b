@import "src/styles/variables.scss";

.custom-drawer {
  display: flex;

  .ant-drawer-mask {
    backdrop-filter: blur(4px);
    background: rgba($primary, 0.3);
  }

  &.ant-drawer-right > .ant-drawer-content-wrapper {
    top: 20px;
    right: 20px;
    bottom: 20px;
  }

  &.ant-drawer-left > .ant-drawer-content-wrapper {
    top: 20px;
    left: 20px;
    bottom: 20px;
  }

  & .ant-drawer-content-wrapper {
    box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.04);

    .ant-drawer-content {
      border-radius: 10px;
    }
  }

  & .ant-drawer-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0;
  }

  & .ant-drawer-header {
    position: relative;
    padding: 0;
    border: none;

    .ant-drawer-close {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 20px;
      right: 20px;
      width: 24px;
      height: 24px;
      z-index: 1;
      color: $primary;
      background-color: #fff;
      border: 1px solid rgba($primary, 0.16);
      padding: 0;
      margin: 0;
      border-radius: 6px;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

      &:hover {
        border-color: rgba($primary, 0.16);
        background-color: rgba($primary, 0.07);
      }

      svg {
        width: 10px;
        height: 10px;
      }
    }
  }

  &__inner {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    background: #fff;
  }

  & .custom-modal__header {


    & .custom-modal__title {

    }
  }

  & .custom-modal__footer {

  }

  & .custom-modal__middle {

  }

  & .custom-modal__buttons {

  }

  & .ant-form-item {

  }

  & .abs-loader {
    border-radius: 6px;
  }
}

