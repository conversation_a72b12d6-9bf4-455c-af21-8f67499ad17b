import React, { FC, ReactNode } from "react";

import { Drawer, DrawerProps } from "antd";

import "./styles.scss";

type DrawerPropTypes = {
  children: ReactNode;
  afterClose?: () => void;
  withoutInner?: boolean;
  onCancel: () => void;
} & Omit<DrawerProps, "onClose">;

export const DrawerCustom: FC<DrawerPropTypes & DrawerProps> = (props) => {
  const { children, className, withoutInner, afterClose, onCancel, afterOpenChange, ...restProps } = props;

  const defaultAfterOpenChange = (open: boolean) => {
    if (afterClose) {
      if (!open) {
        afterClose();
      }
    } else if (afterOpenChange) {
      afterOpenChange(open);
    }
  };

  return (
    <Drawer
      className={`custom-drawer ${className ? className : ""}`}
      width={560}
      destroyOnClose={true}
      afterOpenChange={defaultAfterOpenChange}
      onClose={onCancel}
      maskClosable={false}
      {...restProps}
    >
      {withoutInner ? <>{children}</> : <div className="custom-drawer__inner">{children}</div>}
    </Drawer>
  );
};
