import React from "react";

import { Button } from "antd";
import { ButtonProps } from "antd/lib/button/button";

import "./styles.scss";

export type TButtonCustomProps = ButtonProps & {
  fullWidth?: boolean;
  link?: string;
};

export const ButtonCustom = (props: TButtonCustomProps) => {
  const { className, fullWidth, link, children, ...restProps } = props;
  let classesCompose = "custom-btn";

  if (className) {
    classesCompose += ` ${className}`;
  }

  if (fullWidth) {
    classesCompose += " full-width";
  }

  return (
    <Button className={classesCompose} {...restProps}>
      {children}
    </Button>
  );
};
