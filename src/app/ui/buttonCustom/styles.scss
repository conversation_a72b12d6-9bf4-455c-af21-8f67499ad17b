@import "src/styles/variables.scss";

.custom-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;

  span + svg {
    margin: 0 0 0 5px;
  }

  svg + span {
    margin: 0 0 0 5px;
  }

  &.ant-btn {
    &-primary {
      border: none;

      &:not([disabled]) {
        box-shadow: 0 4px 4px 0 rgba($primary, 0.1);

        &:focus {
          color: #fff;
          background-color: $primary;
        }

        &:hover {
          color: #fff;
          background-color: rgba($primary, 0.8);
        }

        &:active {
          color: #fff;
          background-color: $primary;
        }
      }
    }

    &-default {
      &:not([disabled]) {
        border-color: rgba($primary, 0.16);
        color: $primary;

        &:focus {
          border-color: rgba($primary, 0.16);
          color: $primary;
        }

        &:hover {
          border-color: $primary;
          color: $primary;
        }

        &:active {
          border-color: rgba($primary, 0.16);
          color: $primary;
        }
      }
    }
  }
}

/* antd set padding-top: 0.01px !important; for a.ant-btn */
a.custom-btn.ant-btn {
  &:not(.ant-btn-lg, .ant-btn-sm) {
    padding-top: 8.8px !important;
  }

  &.ant-btn-lg {
    padding-top: 9.7px !important;
  }

  &.ant-btn-sm {
    padding-top: 4.6px !important;
  }
}
