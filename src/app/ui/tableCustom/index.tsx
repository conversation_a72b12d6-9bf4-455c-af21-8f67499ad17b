import React, { FC, useRef } from "react";

import { Table as AntTable, TableProps as AntTableProps, Pagination, TablePaginationConfig } from "antd";
import { FilterValue, SorterResult, TableCurrentDataSource } from "antd/es/table/interface";

import { SortOrderFromAntMap } from "#types/common";

import "./styles.scss";

type TableType<T> = AntTableProps<T>;

type TableProps<T> = TableType<T> & {
  fixedHead?: boolean;
  onSortChange?: ({ orderBy, sortOrder }: { orderBy?: string; sortOrder?: SortOrderFromAntMap }) => void;
};

const Table = <T extends object>(props: TableProps<T>) => {
  const { fixedHead, className, pagination, onSortChange, onChange, ...restProps } = props;

  const tableRef = useRef<HTMLDivElement>(null);

  let classesCompose = `custom-table${fixedHead ? " fixed-head-table" : ""} u-fancy-scrollbar`;

  if (className) {
    classesCompose = `${classesCompose} ${className}`;
  }

  const onTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<T> | SorterResult<T>[],
    extra: TableCurrentDataSource<T>,
  ) => {
    if (onSortChange && extra.action === "sort" && !Array.isArray(sorter)) {
      // onSortChange(sorter.field as string, sorter.order ? SortOrderFromAntMap[sorter.order] : undefined, sorter);
      onSortChange({
        orderBy: sorter.order ? (sorter.field as string) : undefined,
        sortOrder: sorter.order ? SortOrderFromAntMap[sorter.order] : undefined,
      });
      // { orderBy: order ? field : undefined, sortOrder: order }
    }

    if (onChange) {
      onChange(pagination, filters, sorter, extra);
    }
  };

  const onPaginationChange = (page: number, pageSize: number) => {
    if (pagination && pagination.onChange) {
      pagination.onChange(page, pageSize);

      setTimeout(() => {
        if (tableRef.current) {
          tableRef.current.scroll({
            top: 0,
            behavior: "smooth",
          });
        }
      }, 100);
    }
  };

  return (
    <>
      <div ref={tableRef} className={classesCompose}>
        <AntTable
          pagination={false}
          onChange={onSortChange || onChange ? onTableChange : undefined}
          {...restProps}
        />
      </div>
      {pagination && (
        <div className="custom-table__pagination">
          <div></div>
          <div>
            <Pagination
              hideOnSinglePage
              showSizeChanger={false}
              {...pagination}
              onChange={onPaginationChange}
            />
          </div>
        </div>
      )}
    </>
  );
};

const Expanded: FC<any> = ({ children }) => <div className="custom-table__expanded">{children}</div>;

const TableColumn = AntTable.Column;
const ColumnGroup = AntTable.ColumnGroup;

Table.Expanded = Expanded;
Table.Column = TableColumn;
Table.ColumnGroup = ColumnGroup;

type TableComponentType = typeof Table & {
  Expanded: typeof Expanded;
  Column: typeof TableColumn;
  ColumnGroup: typeof ColumnGroup;
};

const TableCustom: TableComponentType = Table;

type TTableNumber = {
  value: number;
}

const TableNumber: FC<TTableNumber> = (props) => {
  return (
    <div className="w-s-n table-number-col">
      {(props.value < 10 ? "0" : "")}{props.value}
    </div>
  )
};

export { TableCustom, TableNumber };
