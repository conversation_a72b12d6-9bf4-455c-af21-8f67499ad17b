@import "src/styles/variables.scss";

.custom-table {
  flex-grow: 1;
  overflow-y: auto;
  border: 1px solid $gray01;
  border-radius: 8px;

  & .ant-table-thead {
    th {
      font-size: 12px;
      font-weight: 400;
      line-height: 15px;

      &:before {
        display: none;
      }
    }
  }

  & .ant-table-tbody {
    & > tr {
      &:last-child {
        & > td {
          border-bottom-color: transparent;
        }
      }
    }
  }

  &.fixed-head-table {
    & .ant-table-thead {
      position: sticky;
      top: 0;
      z-index: 3;
    }
  }

  &__pagination {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
  }
}

.table-number-col {
  color: $gray03;
}