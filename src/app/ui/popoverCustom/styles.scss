@import "src/styles/variables.scss";

$btnPadding: 5px 10px;

.custom__popover {
  &__item {
    width: 100%;

    .custom-btn {
      padding: $btnPadding;
      color: $textColor;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 400;
      line-height: 18px;
      height: auto;
      width: 100%;
      justify-content: flex-start;
      box-shadow: none;
      border: none;

      &:not([disabled]) {
        &:hover {
          background-color: $gray02;
        }
      }

      &[disabled] {
        color: #97A6BA;
        border-color: #ECECEC;
        background: #f5f5f5;
        text-shadow: none;
        box-shadow: none;
      }
    }

    a.custom-btn.ant-btn {
      padding: $btnPadding !important;
    }
  }

  .ant-popover-inner {
    min-width: 218px;
  }

  .ant-popover-inner-content {
    width: 100%;
  }
}

.custom__popover__action-btn {
  border: none;
  background-color: transparent;
  padding-left: 7px;
  padding-right: 7px;
  box-shadow: none;

  &:hover {
    background-color: transparent;
  }
}