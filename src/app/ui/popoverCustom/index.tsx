import React, { FC, ReactNode, useRef, useState } from "react";

import { MenuIcon } from "#assets/icons";
import { ButtonCustom, TButtonCustomProps } from "#ui/buttonCustom";
import { Popconfirm, Popover } from "antd";
import { PopconfirmProps } from "antd/lib/popconfirm";
import { PopoverProps } from "antd/lib/popover";

import "./styles.scss";

type ItemProps = {
  children?: ReactNode;
};
export const Item = (props: ItemProps) => {
  const { children } = props;

  return <div className="custom__popover__item">{children}</div>;
};

type ConfirmItemPropsTypes = PopconfirmProps & {
  children?: ReactNode;
};

const ConfirmItem: FC<ConfirmItemPropsTypes> = (props) => {
  const { children, onConfirm, ...restProps } = props;

  const itemRef = useRef<HTMLDivElement>(null);
  const isOpened = useRef(false);

  return (
    <div
      className="custom__popover__item"
      ref={itemRef}
      onClick={(e) => {
        if (!isOpened.current) {
          e.stopPropagation();
        } else {
          isOpened.current = false;
        }
      }}
    >
      <Popconfirm
        placement="left"
        onConfirm={() => {
          onConfirm?.();
          isOpened.current = true;
          itemRef.current?.click();
        }}
        okText="Да"
        cancelText="Нет"
        {...restProps}
      >
        {children}
      </Popconfirm>
    </div>
  );
};

type ContentPropsType = PopoverProps & { icon?: ReactNode; buttonProps?: TButtonCustomProps; disabled?: boolean };
type ContextPopoverUIType = React.FC<ContentPropsType> & {
  Item: typeof Item;
  ConfirmItem: typeof ConfirmItem;
};

const PopoverCustom: ContextPopoverUIType = (props) => {
  const { content, icon, buttonProps, disabled, children, ...restProps } = props;

  const [popoverVisible, setPopoverVisible] = useState(false);

  return (
    <Popover
      overlayClassName="custom__popover"
      trigger="click"
      placement="left"
      showArrow={false}
      content={
        <div
          onClick={(event) => {
            event.stopPropagation();
            setPopoverVisible(false);
          }}
        >
          {content instanceof Function ? content() : content}
        </div>
      }
      open={popoverVisible}
      onOpenChange={(visible) => setPopoverVisible(disabled ? false : visible)}
      {...restProps}
    >
      <ButtonCustom
        className="custom__popover__action-btn"
        size="small"
        onClick={(event) => event.stopPropagation()}
        disabled={disabled}
        {...buttonProps}
      >
        {children ? children : <MenuIcon />}
      </ButtonCustom>
    </Popover>
  );
};

PopoverCustom.Item = Item;
PopoverCustom.ConfirmItem = ConfirmItem;

export { PopoverCustom };
