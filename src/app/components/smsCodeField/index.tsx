import React, { ChangeEvent, FC } from "react";

import { SMS_CODE_SIZE } from "#constants/index";
import { useCountDown } from "#hooks/useCountDown";
import { namespaces } from "#localization/i18n.constants";
import { Alert, Input } from "antd";
import { useTranslation } from "react-i18next";

import "./styles.scss";

export const SmsCodeField: FC<any> = (props) => {
  const {
    className = "",
    codeSize = SMS_CODE_SIZE,
    countDownActive,
    error,
    inputStyle = {},
    onChange,
    onTimerFinish,
    timerSeconds = 59,
  } = props;

  const { t } = useTranslation();

  const timerTime = useCountDown({ start: countDownActive, onTimerFinish, seconds: timerSeconds });

  const onSmsCodeChange = (e: ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  return (
    <>
      <Input onChange={onSmsCodeChange} placeholder={t("placeholders.inputCode")} />
      <Alert
        className="SCF__alert"
        message={
          <>
            <div>{t("smsSent", { ns: namespaces.notifications })}</div>
            <div>
              {t("codeExpiration")} 00:{timerTime < 10 ? `0${timerTime}` : timerTime}
            </div>
          </>
        }
        type="info"
        showIcon
      />
    </>
  );
};
