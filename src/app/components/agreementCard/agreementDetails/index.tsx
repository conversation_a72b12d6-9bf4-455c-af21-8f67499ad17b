import React from "react";

import { IAgreementModel } from "#businessLogic/models/agreement";
import { getAgreementRecurring, getAgreementTariff } from "#components/agreementCard/utils";
import { formatNumber } from "#utils/helpers";
import moment from "moment";
import { useTranslation } from "react-i18next";

type AgreementDetailsProps = {
  data: IAgreementModel;
  tin?: string;
  agreementId?: string;
  loading?: boolean;
  getAgreementDetails?: any;
  isDisabled?: boolean;
};

export const AgreementDetails = (props: AgreementDetailsProps) => {
  const { data } = props;

  const { t } = useTranslation();

  return (
    <div className="agreement-card__details-wrap">
      <div className="agreement-card__details">
        <div className="agreement-card__details__block">
          <div className="agreement-card__details__title">{t("service")}</div>
          <div className="agreement-card__details__value">{data.serviceType && data.serviceType.nameRu}</div>
        </div>
        <div className="agreement-card__details__block">
          <div className="agreement-card__details__title">{t("tariff")}</div>
          <div className="agreement-card__details__value">{getAgreementTariff(data)}</div>
        </div>
        <div className="agreement-card__details__block">
          <div className="agreement-card__details__title">{t("serviceType2")}</div>
          <div className="agreement-card__details__value">{getAgreementRecurring(data)}</div>
        </div>
        <div className="agreement-card__details__block">
          <div className="agreement-card__details__title">{t("status")}</div>
          <div className="agreement-card__details__value">{data.status && data.status.nameRu}</div>
        </div>
        <div className="agreement-card__details__block">
          <div className="agreement-card__details__title">{t("activationDate")}</div>
          <div className="agreement-card__details__value">
            {data.firstActivatedDate ? moment(data.firstActivatedDate).format("DD.MM.YYYY") : "-"}
          </div>
        </div>
        <div className="agreement-card__details__block">
          <div className="agreement-card__details__title">{t("nextPayment")}</div>
          <div className="agreement-card__details__value">
            {data.nextPeriodStart ? moment(data.nextPeriodStart).format("DD.MM.YYYY") : "-"}
          </div>
        </div>
        <div className="agreement-card__details__block">
          <div className="agreement-card__details__title">{t("amount")}</div>
          <div className="agreement-card__details__value">
            <span className="w-s-n">{formatNumber(data.total)} UZS</span>
          </div>
        </div>
      </div>
    </div>
  );
};
