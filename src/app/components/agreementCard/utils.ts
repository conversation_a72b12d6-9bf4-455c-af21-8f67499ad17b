import { IAgreementModel } from "#businessLogic/models/agreement";
import { i18n } from "#localization/i18n";
import { $statusColors } from "#styles/antModifyVars";
import { uniq } from "lodash";

export const agreementStatuses = {
  ACCEPTED: "ACCEPTED",
  ACTIVE: "ACTIVE",
  PAUSE: "PAUSE",
  INACTIVE: "INACTIVE",
};

export const getAgreementTariff = (agreement: IAgreementModel): string => {
  if (agreement.xizmat) {
    return agreement.xizmat.title;
  } else if (agreement.agreements) {
    return uniq(agreement.agreements.map((item) => getAgreementTariff(item))).join(", ");
  } else {
    return "-";
  }
};

export const getTariffRecurring = (xizmat: IAgreementModel["xizmat"]) => {
  return xizmat.recurring ? i18n.t("recurring") : i18n.t("singleService");
};

export const getAgreementRecurring = (agreement: IAgreementModel): string => {
  if (agreement.xizmat) {
    return getTariffRecurring(agreement.xizmat);
  } else if (agreement.agreements) {
    return uniq(agreement.agreements.map((item) => getAgreementRecurring(item))).join(", ");
  } else {
    return "-";
  }
};

export const getStatusColor = (statusCode: string) => {
  switch (statusCode) {
    case agreementStatuses.ACTIVE:
      return $statusColors.green;
    case agreementStatuses.INACTIVE:
      return $statusColors.red;
    case agreementStatuses.PAUSE:
      return $statusColors.orange;
  }

  return undefined;
};
