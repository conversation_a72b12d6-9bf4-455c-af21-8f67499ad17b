import React, { useEffect, useState } from "react";

import { ENUM_AGREEMENT_STATUSES } from "#businessLogic/models/agreement";
import { $agreementDetails } from "#stores/agreement";
import { ContentCustom } from "#ui/contentCustom";
import { ContentHeader } from "#ui/contentHeader";
import { StatusCustom } from "#ui/status";
import { getRestStr } from "#utils/helpers";
import { Alert, Menu, Spin } from "antd";
import { useStore } from "effector-react";
import { MenuClickEventHandler } from "rc-menu/lib/interface";
import { useTranslation } from "react-i18next";
import { Link, Route, Switch, useRouteMatch } from "react-router-dom";

import { AgreementDetails } from "./agreementDetails";
import { AgreementInvoices } from "./agreementInvoices";
import { AgreementQuoteList } from "./agreementQuote";
import { PublicOffer } from "./publicOffer";
import "./styles.scss";

type AgreementCardProps = {
  tin: string;
  match?: {
    params: { agreementId?: string };
    url?: string;
  };
  backUrl: string;
};

export const AgreementCard = (props: AgreementCardProps) => {
  const { tin, backUrl } = props;

  const { t } = useTranslation();

  const match = useRouteMatch();

  // @ts-ignore
  const agreementId = match?.params.agreementId;

  const agreementDetailsState = useStore($agreementDetails.store);

  const {
    data: agreementDetails,
    loading: agreementDetailsLoading,
    error: agreementDetailsError,
  } = agreementDetailsState;

  const openedTabFromUrl = getRestStr(location.pathname, match?.url || "").slice(1);
  const [openedTab, setOpenedTab] = useState(openedTabFromUrl || "/");

  const getAgreementDetails = () => {
    if (agreementId) {
      $agreementDetails.effect(agreementId);
    }
  };

  useEffect(() => {
    getAgreementDetails();
    return () => {
      $agreementDetails.reset();
    };
  }, [agreementId]);

  const onNavigationClick: MenuClickEventHandler = (e) => {
    setOpenedTab(e.key);
  };

  const isDisabled =
    agreementDetails && agreementDetails.status && agreementDetails.status.code === ENUM_AGREEMENT_STATUSES.INACTIVE;

  return (
    <Spin spinning={agreementDetailsLoading}>
      <ContentCustom className="agreement-card">
        {agreementDetails && (
          <>
            <ContentHeader>
              <ContentHeader.Title
                title={
                  <div className="headTitle">
                    {t("order")}: {agreementDetails.agreementNumber}
                    <StatusCustom status={agreementDetails.status?.code}>{agreementDetails.status.nameRu}</StatusCustom>
                  </div>
                }
                backPath={backUrl}
              />
            </ContentHeader>

            <div className="agreement-card__body">
              <div className="content-block agreement-card__side">
                <Menu
                  className="agreement-card__navigation"
                  selectedKeys={[openedTab]}
                  mode="vertical"
                  triggerSubMenuAction="click"
                  onClick={onNavigationClick}
                >
                  <Menu.Item key="/">
                    <div className="agreement-card__navigation__item">
                      <Link to={match?.url || ""}>{t("details")}</Link>
                    </div>
                  </Menu.Item>
                  <Menu.Item key="public-offer">
                    <div className="agreement-card__navigation__item">
                      <Link to={`${match?.url}/public-offer`}>{t("publicOffer")}</Link>
                    </div>
                  </Menu.Item>
                  <Menu.Item key="quote">
                    <div className="agreement-card__navigation__item">
                      <Link to={`${match?.url}/quote`}>{t("invoicePayment")}</Link>
                    </div>
                  </Menu.Item>
                  <Menu.Item key="invoices">
                    <div className="agreement-card__navigation__item">
                      <Link to={`${match?.url}/invoices`}>{t("invoices")}</Link>
                    </div>
                  </Menu.Item>
                </Menu>
              </div>

              <div className="content-block agreement-card__content">
                <Switch>
                  <Route
                    exact
                    path={match?.url}
                    render={(props) => (
                      <AgreementDetails
                        {...props}
                        tin={tin}
                        agreementId={agreementId}
                        data={agreementDetails}
                        loading={agreementDetailsLoading}
                        getAgreementDetails={getAgreementDetails}
                        isDisabled={isDisabled}
                      />
                    )}
                  />
                  <Route
                    exact
                    path={`${match?.url}/public-offer`}
                    render={(props) => <PublicOffer {...props} tin={tin} />}
                  />
                  <Route
                    exact
                    path={`${match?.url}/quote`}
                    render={() => <AgreementQuoteList agreementId={agreementId} isDisabled={!!isDisabled} />}
                  />
                  <Route
                    exact
                    path={`${match?.url}/invoices`}
                    render={(props) => <AgreementInvoices {...props} agreementId={agreementId} />}
                  />
                </Switch>
              </div>
            </div>
          </>
        )}
        {agreementDetailsError && (
          <div className="custom-content__error">
            <Alert message={agreementDetailsError.title} type="error" />
          </div>
        )}
      </ContentCustom>
    </Spin>
  );
};
