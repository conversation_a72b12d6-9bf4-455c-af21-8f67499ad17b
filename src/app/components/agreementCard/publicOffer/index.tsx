import React, { useEffect } from "react";

import { PrinterIcon } from "#assets/icons";
import { $publicOffer } from "#stores/billingServices";
import { ButtonCustom } from "#ui/buttonCustom";
import { printIframe } from "#utils/helpers";
import { Spin } from "antd";
import { useStore } from "effector-react";

import classes from "./styles.module.scss";

type PublicOfferProps = {
  tin?: string;
};

export const PublicOffer = (props: PublicOfferProps) => {
  const { tin } = props;

  const publicOfferState = useStore($publicOffer.store);
  const { data: publicOffer, loading: publicOfferLoading } = publicOfferState;

  useEffect(() => {
    if (tin) {
      $publicOffer.effect(tin);
    }
  }, []);

  const onPrintDocumentClick = (id: string) => {
    printIframe(id);
  };

  return (
    <Spin spinning={publicOfferLoading}>
      <div className={classes.cont}>
        <ButtonCustom type="primary" icon={<PrinterIcon />} onClick={() => onPrintDocumentClick("divToPrint")} />
        {publicOffer && <iframe id="divToPrint" width="100%" height="1000px" srcDoc={publicOffer.publicOfferContent} />}
      </div>
    </Spin>
  );
};
