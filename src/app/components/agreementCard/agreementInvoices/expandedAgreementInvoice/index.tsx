import React, { useEffect } from "react";

import { PrinterIcon } from "#assets/icons";
import { $generatedInvoicesMap } from "#stores/agreement";
import { ButtonCustom } from "#ui/buttonCustom";
import { printIframe } from "#utils/helpers";
import { Spin } from "antd";
import { useStore } from "effector-react";

import "./styles.scss";

type ExpandedAgreementInvoiceProps = {
  invoiceId: string;
};

export const ExpandedAgreementInvoice = (props: ExpandedAgreementInvoiceProps) => {
  const { invoiceId } = props;

  const generatedInvoicesMapState = useStore($generatedInvoicesMap.store);

  useEffect(() => {
    $generatedInvoicesMap.effect(invoiceId);
  }, [invoiceId]);

  const currentGeneratedInvoice = generatedInvoicesMapState[invoiceId];

  if (!currentGeneratedInvoice) {
    return null;
  }

  const generatedInvoiceLoading = currentGeneratedInvoice.loading;
  const generatedInvoiceData = currentGeneratedInvoice.data;

  return (
    <div className="expanded-agreement-invoice">
      <div className="current-order__document-btns">
        <ButtonCustom type="primary" icon={<PrinterIcon />} onClick={() => printIframe("divToPrint")} />
      </div>
      <iframe
        id="divToPrint"
        width="100%"
        height="1000px"
        srcDoc={generatedInvoiceData ? generatedInvoiceData : undefined}
      />
      {generatedInvoiceLoading && <Spin className="abs-loader" />}
    </div>
  );
};
