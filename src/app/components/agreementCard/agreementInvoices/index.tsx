import React, { useEffect, useMemo, useState } from "react";

import { IAgreementInvoicesModel } from "#businessLogic/models/agreement";
import { TListQueryParams } from "#businessLogic/models/common";
import { $agreementInvoices } from "#stores/agreement";
import { TableCustom } from "#ui/tableCustom";
import { formatNumber } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import moment from "moment";
import { useTranslation } from "react-i18next";

import { ExpandedAgreementInvoice } from "./expandedAgreementInvoice";

type AgreementInvoicesProps = {
  agreementId: number;
};

export const AgreementInvoices = (props: AgreementInvoicesProps) => {
  const { agreementId } = props;

  const { t } = useTranslation();

  const agreementInvoicesState = useStore($agreementInvoices.store);
  const [filterProps, setFilterProps] = useState<TListQueryParams>({});

  const { loading: invoicesLoading, data: invoicesData } = agreementInvoicesState;

  useEffect(() => {
    return () => {
      $agreementInvoices.reset();
    };
  }, []);

  useEffect(() => {
    $agreementInvoices.effect({
      agreementId,
      params: filterProps,
    });
  }, [agreementId, filterProps]);

  const onFilterChange = (fields: TListQueryParams) => {
    setFilterProps({ ...filterProps, page: 0, ...fields });
  };

  const onChangePagination = (page: number, size: number) => {
    onFilterChange({ ...filterProps, page: page - 1, size });
  };

  const columns: ColumnsType<IAgreementInvoicesModel> = useMemo(() => {
    return [
      {
        title: "",
        dataIndex: "num",
        width: 40,
        render: (_, row, index) => <div className="w-s-n">{invoicesData.size * invoicesData.number + index + 1}</div>,
      },
      {
        title: t("docNum"),
        dataIndex: "documentNumber",
        render: (_, row) => row.documentNumber,
      },
      {
        title: t("publicOffer"),
        dataIndex: "customerPublicOfferNumber",
        render: (_, row) => row.customerPublicOfferNumber,
      },
      {
        title: t("amountPrice"),
        dataIndex: "totalPriceWithVat",
        render: (_, row) => (
          <>
            <strong>{formatNumber(row.totalPriceWithVat)}</strong> {t("UZS")}
          </>
        ),
      },
      {
        title: t("date"),
        dataIndex: "invoiceDate",
        render: (_, row) => moment(row.invoiceDate).locale("ru").format("DD.MM.YYYY"),
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) => row.status?.nameRu,
      },
    ];
  }, [invoicesData.size, invoicesData.number]);

  return (
    <div>
      <TableCustom
        rowKey="id"
        dataSource={invoicesData.content}
        columns={columns}
        loading={invoicesLoading}
        pagination={{
          current: invoicesData.number + 1,
          total: invoicesData.totalElements,
          pageSize: invoicesData.size,
          hideOnSinglePage: true,
          showSizeChanger: true,
          onChange: onChangePagination,
        }}
        expandable={{
          expandedRowRender: (invoice: IAgreementInvoicesModel) => <ExpandedAgreementInvoice invoiceId={invoice.id} />,
        }}
      />
    </div>
  );
};
