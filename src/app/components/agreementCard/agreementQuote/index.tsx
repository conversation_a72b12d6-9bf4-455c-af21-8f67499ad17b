import React, { FC, useEffect, useMemo, useState } from "react";

import { IAgreementQuoteListItemModel } from "#businessLogic/models/agreement";
import { DATE_FORMAT } from "#constants/index";
import { namespaces } from "#localization/i18n.constants";
import { $agreementQuoteList, $createAgreementQuote } from "#stores/agreement";
import { ButtonCustom } from "#ui/buttonCustom";
import { TableCustom } from "#ui/tableCustom";
import { formatDate, openNotification } from "#utils/helpers";
import { faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { AgreementQuoteDetails } from "./details";

type PropsTypes = {
  agreementId: number;
  isDisabled: boolean;
};

export const AgreementQuoteList: FC<PropsTypes> = (props) => {
  const { agreementId, isDisabled } = props;

  const { t } = useTranslation();

  const agreementQuoteListState = useStore($agreementQuoteList.store);
  const createAgreementQuoteState = useStore($createAgreementQuote.store);

  const [params, setParams] = useState<any>({});

  const agreementQuoteListData = agreementQuoteListState.data;

  const getList = () => {
    $agreementQuoteList.effect({ ...params, agreementId });
  };

  useEffect(() => {
    return () => {
      $agreementQuoteList.reset();
    };
  }, []);

  useEffect(() => {
    getList();
  }, [params]);

  useEffect(() => {
    if (createAgreementQuoteState.success) {
      openNotification("success", t("success", { ns: namespaces.notifications }));
      $createAgreementQuote.reset();
      getList();
    }
  }, [createAgreementQuoteState.success]);

  const onPaginationChange = (page: number, size: number) => {
    setParams({ ...params, page: page - 1, size });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<IAgreementQuoteListItemModel> = [
      {
        title: "",
        dataIndex: "number",
        width: 40,
        render: (_, row, index) => (
          <div className="w-s-n">{agreementQuoteListData.size * agreementQuoteListData.number + index + 1}</div>
        ),
      },
      {
        title: t("docNum"),
        dataIndex: "documentNumber",
        render: (_, row) => row.documentNumber,
      },
      {
        title: t("creationDate"),
        dataIndex: "createdDate",
        render: (_, row) => formatDate(row.createdDate, DATE_FORMAT),
      },
      {
        title: t("status"),
        dataIndex: "documentNumber",
        render: (_, row) => row.status.nameRu,
      },
    ];

    return columns;
  }, [agreementQuoteListData.size, agreementQuoteListData.number]);

  return (
    <div>
      <div className="agreement-quote__actions">
        <ButtonCustom
          type="primary"
          size="small"
          icon={<FontAwesomeIcon className="svg" icon={faPlus} />}
          loading={createAgreementQuoteState.loading}
          onClick={() => {
            $createAgreementQuote.effect({
              agreementId,
            });
          }}
          disabled={agreementQuoteListData.totalElements > 0}
        >
          {t("invoicePayment")}
        </ButtonCustom>
      </div>
      <TableCustom
        rowKey="id"
        loading={agreementQuoteListState.loading}
        dataSource={agreementQuoteListData.content}
        columns={tableColumns}
        pagination={{
          total: agreementQuoteListData.totalElements,
          pageSize: agreementQuoteListData.size,
          current: params.page ? Number(params.page) + 1 : 1,
          onChange: onPaginationChange,
        }}
        expandable={{
          expandedRowRender: (item: IAgreementQuoteListItemModel) => (
            <AgreementQuoteDetails quoteId={item.id} isDisabled={isDisabled} />
          ),
        }}
      />
    </div>
  );
};
