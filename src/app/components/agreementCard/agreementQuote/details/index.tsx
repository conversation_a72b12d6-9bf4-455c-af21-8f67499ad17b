import React, { useEffect, useState } from "react";

import { DownloadIcon, PrinterIcon, UploadIcon } from "#assets/icons";
import { useModalControl } from "#hooks/useModalControl";
import { usePrint } from "#hooks/usePrint";
import { namespaces } from "#localization/i18n.constants";
import { $generatedQuote, $quoteDetails, $uploadQuoteFile } from "#stores/agreement";
import { ButtonCustom } from "#ui/buttonCustom";
import { ModalCustom } from "#ui/modalCustom";
import { isImage, isPdfOrImage, openNotification, printIframe } from "#utils/helpers";
import { Spin } from "antd";
import { useStore } from "effector-react";
import { PDFDocumentProxy } from "pdfjs-dist/types/src/display/api";
import { useTranslation } from "react-i18next";
import { Document, Page } from "react-pdf";

import { GENERATED_DOCUMENT, UPLOADED_DOCUMENT } from "../../constants";

import classes from "./styles.module.scss";
import { UploadDocumentModal } from "./uploadDocumentModal";

type AgreementQuoteProps = {
  quoteId: string;
  isDisabled: boolean;
};

export const AgreementQuoteDetails = (props: AgreementQuoteProps) => {
  const { quoteId, isDisabled } = props;

  const { t } = useTranslation();

  const quoteDetailsState = useStore($quoteDetails.store);
  const generatedQuoteState = useStore($generatedQuote.store);
  const uploadQuoteFileState = useStore($uploadQuoteFile.store);

  const currentQuoteDetailsState = quoteDetailsState[quoteId];
  const currentGeneratedQuoteState = generatedQuoteState[quoteId];

  const [activeDocument, setActiveDocument] = useState(GENERATED_DOCUMENT);
  const [pdfNumPages, setPdfNumPages] = useState(0);

  const uploadModalControl = useModalControl();

  const { uploadedDocumentRef, handlePrint } = usePrint();

  useEffect(() => {
    $quoteDetails.effect(quoteId);
  }, []);

  useEffect(() => {
    if (currentQuoteDetailsState && currentQuoteDetailsState.data) {
      if (quoteDetails.fileName && isPdfOrImage(quoteDetails.fileName)) {
        setActiveDocument(UPLOADED_DOCUMENT);
      } else {
        setActiveDocument(GENERATED_DOCUMENT);
        $generatedQuote.effect(quoteId);
      }
    }
  }, [currentQuoteDetailsState]);

  useEffect(() => {
    if (uploadQuoteFileState.success) {
      openNotification(
        "success",
        t("success", { ns: namespaces.notifications }),
        t("docUploaded", { ns: namespaces.notifications }),
      );

      $quoteDetails.effect(quoteId);
      $uploadQuoteFile.reset();
    }
  }, [uploadQuoteFileState.success]);

  const onGenerateDocumentClick = () => {
    setActiveDocument(GENERATED_DOCUMENT);

    if (!currentGeneratedQuoteState) {
      $generatedQuote.effect(quoteId);
    }
  };

  const onPrintDocumentClick = (id: string) => {
    if (activeDocument === GENERATED_DOCUMENT) {
      printIframe(id);
    }
  };

  const showUploadedDocument = () => {
    setActiveDocument(UPLOADED_DOCUMENT);
  };

  const onUploadClick = () => {
    uploadModalControl.openModal();
  };

  const onUploadSubmit = (formData: FormData) => {
    $uploadQuoteFile.effect({
      id: quoteId,
      file: formData,
    });
  };

  const generatedDocRender = () => {
    return (
      <Spin spinning={currentGeneratedQuoteState ? currentGeneratedQuoteState.loading : false}>
        <div className={classes.document}>
          <div className="current-order__document-btns">
            <ButtonCustom
              size="small"
              type="default"
              icon={<PrinterIcon />}
              onClick={() => onPrintDocumentClick("divToPrint")}
            />
            <ButtonCustom
              type="default"
              size="small"
              icon={<UploadIcon />}
              disabled={isDisabled}
              onClick={onUploadClick}
            />
          </div>
          <iframe
            id="divToPrint"
            width="100%"
            height="1000px"
            srcDoc={
              currentGeneratedQuoteState && currentGeneratedQuoteState.data
                ? currentGeneratedQuoteState.data
                : undefined
            }
          />
        </div>
      </Spin>
    );
  };

  if (!currentQuoteDetailsState || currentQuoteDetailsState.loading) {
    return (
      <div>
        <Spin size="large" />
      </div>
    );
  }

  const quoteDetails = currentQuoteDetailsState.data!;

  return (
    <div className={classes.cont}>
      <div className={classes.top}>
        <div className="current-order__top-left"></div>
        <div className={classes.topRight}>
          {quoteDetails.fileName && (
            <div className={classes.btns}>
              <ButtonCustom
                size="small"
                type="default"
                onClick={onGenerateDocumentClick}
                loading={currentGeneratedQuoteState ? currentGeneratedQuoteState.loading : false}
                disabled={activeDocument === GENERATED_DOCUMENT}
              >
                {t("original")}
              </ButtonCustom>
              <div className="current-order__uploaded-file">
                {isPdfOrImage(quoteDetails.fileName) ? (
                  <ButtonCustom
                    size="small"
                    type="default"
                    onClick={showUploadedDocument}
                    disabled={activeDocument === UPLOADED_DOCUMENT}
                  >
                    {t("uploaded")}
                  </ButtonCustom>
                ) : (
                  <a
                    href={quoteDetails.fileUri}
                    target="_blank"
                    download
                    className="ant-btn file-download"
                    rel="noreferrer"
                  >
                    {quoteDetails.fileName}
                  </a>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      <div>
        {activeDocument === GENERATED_DOCUMENT ? (
          generatedDocRender()
        ) : (
          <div className={classes.document}>
            <div className={classes.btns}>
              <ButtonCustom size="small" type="default" icon={<PrinterIcon />} onClick={handlePrint} />
              <a
                href={quoteDetails.fileUri}
                target="_blank"
                download
                className="ant-btn ant-btn-primary file-download"
                rel="noreferrer"
              >
                <DownloadIcon />
              </a>
              <div className="current-order__upload">
                <ButtonCustom
                  type="default"
                  size="small"
                  icon={<UploadIcon />}
                  disabled={isDisabled}
                  onClick={onUploadClick}
                />
              </div>
            </div>
            <div className="current-order__document-content" ref={uploadedDocumentRef}>
              {quoteDetails.fileName && isImage(quoteDetails.fileName) ? (
                <div className="current-order__document-img">
                  <img src={quoteDetails.fileUri} alt="" />
                </div>
              ) : (
                <Document
                  file={quoteDetails.fileUri}
                  //@ts-ignore
                  onLoadSuccess={(pdf: PDFDocumentProxy) => setPdfNumPages(pdf.numPages)}
                  className="doc"
                >
                  {Array(pdfNumPages)
                    .fill(null)
                    .map((_, index) => (
                      <Page key={index} pageNumber={index + 1} width={1200} />
                    ))}
                </Document>
              )}
            </div>
          </div>
        )}
      </div>

      <ModalCustom open={uploadModalControl.visible} onCancel={uploadModalControl.closeModal}>
        <UploadDocumentModal
          modalControl={uploadModalControl}
          onUploadSubmit={onUploadSubmit}
          loading={uploadQuoteFileState.loading}
          success={uploadQuoteFileState.success}
          error={uploadQuoteFileState.error}
        />
      </ModalCustom>
    </div>
  );
};
