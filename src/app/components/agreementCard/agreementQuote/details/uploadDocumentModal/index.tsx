import React, { useEffect, useState } from "react";

import { namespaces } from "#localization/i18n.constants";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { Button, Form, Upload, message } from "antd";
import { RcFile } from "antd/lib/upload/interface";
import { useTranslation } from "react-i18next";

type UploadDocumentModalProps = {
  modalControl?: any;
  onUploadSubmit: (data: FormData) => void;
  loading?: boolean;
  success?: boolean;
  error?: string | any;
};

export const UploadDocumentModal = (props: UploadDocumentModalProps) => {
  const { modalControl, onUploadSubmit, loading, success, error } = props;

  const { t } = useTranslation();

  const [form] = Form.useForm();

  const [uploadedDocument, setUploadedDocument] = useState<any>([]);
  const [fieldsErrors, setFieldsErrors] = useState<any>({});

  useEffect(() => {
    if (success) {
      modalControl.closeModal();
    }
  }, [success]);

  const validateForm = () => {
    const notFilledMessage = t("validations.notFilled");

    const errors: any = {};

    if (!uploadedDocument.length) errors.document = notFilledMessage;

    return errors;
  };

  const onSubmit = () => {
    const errors = validateForm();

    setFieldsErrors(errors);

    if (Object.keys(errors).length) {
      message.error(t("validations.error"));
      return;
    }

    const formData = new FormData();
    formData.append("file", uploadedDocument[0]);

    onUploadSubmit(formData);
  };

  const uploadProps = {
    onRemove: () => {
      setUploadedDocument([]);
    },
    beforeUpload: (file: RcFile) => {
      setUploadedDocument([file]);

      return false;
    },
    fileList: uploadedDocument,
  };

  const onSubmitForm = () => {
    form.submit();
  };

  return (
    <>
      <ModalCustom.Header>
        <ModalCustom.Title>{t("uploadDoc", { ns: namespaces.buttons })}</ModalCustom.Title>
      </ModalCustom.Header>

      <ModalCustom.Error error={error} />
      <ModalCustom.Loading show={!!loading} />

      <ModalCustom.Middle>
        <FormCustom form={form} onFinish={onSubmit} phantomSubmit={true}>
          <Form.Item label={t("doc")}>
            <Upload {...uploadProps}>
              <Button>{t("selectFile", { ns: namespaces.buttons })}</Button>
            </Upload>
          </Form.Item>
        </FormCustom>
      </ModalCustom.Middle>

      <ModalCustom.Footer>
        <ModalCustom.Buttons>
          <ButtonCustom type="default" onClick={modalControl.closeModal}>
            {t("cancel", { ns: namespaces.buttons })}
          </ButtonCustom>
          <ButtonCustom type="primary" onClick={onSubmitForm}>
            {t("send", { ns: namespaces.buttons })}
          </ButtonCustom>
        </ModalCustom.Buttons>
      </ModalCustom.Footer>
    </>
  );
};
