import React, { Component, ReactNode } from "react";

import { ErrorView } from "#components/errorView";
import { useLocation } from "react-router-dom";

interface ErrorBoundaryProps {
  location?: any;
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundaryInner extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error: error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    if (this.props.location?.pathname !== prevProps.location?.pathname) {
      this.setState({ hasError: false, error: null });
    }
  }

  render() {
    if (this.state.hasError && this.state.error) {
      return <ErrorView title={this.state.error.name} message={this.state.error.message} />;
    }

    return this.props.children;
  }
}

export const ErrorBoundary = (props: { children: React.ReactNode }) => {
  const location = useLocation();

  return <ErrorBoundaryInner location={location} {...props} />;
};
