import React, { FC, useEffect } from "react";

import { requiredRules } from "#constants/index";
import { XHRState } from "#constructors/index";
import { HandlerType } from "#core/stateManager/types/handler";
import { CreateStoreReturnType } from "#core/stateManager/types/store";
import { useFileUpload } from "#hooks/useFileUpload";
import { ModalControlType } from "#hooks/useModalControl";
import { i18n } from "#localization/i18n";
import { namespaces } from "#localization/i18n.constants";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification } from "#utils/helpers";
import { Form, Select, Upload } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

const { Option } = Select;

type TFormValues = {
  documentType: string;
};

export type TUploadFileModalControlType<T> = {
  params?: T;
  filterBy?: (items: Array<any>) => Array<any>;
};

type TUploadFileModalProps<T> = {
  uploadAction: (data: { file: FormData; documentType?: string; params: T }) => void;
  type?: string;
  callback?: () => void;
  title?: string;
  label?: string;
  modalControl: ModalControlType<TUploadFileModalControlType<T>>;
  successNotificationMessage?: string;
  uploadFileStoreController: CreateStoreReturnType<HandlerType<any, any>, any>;
  documentTypesStoreController?: CreateStoreReturnType<HandlerType<any, any>, XHRState<any>>;
};

export const UploadFileModal = <T extends {}>(props: TUploadFileModalProps<T>) => {
  const {
    uploadAction,
    type,
    callback,
    title = i18n.t("uploadDoc", { ns: namespaces.buttons }),
    label = i18n.t("docType"),
    modalControl,
    successNotificationMessage = i18n.t("fileUploaded", { ns: namespaces.notifications }),
    uploadFileStoreController,
    documentTypesStoreController,
  } = props;

  const { t } = useTranslation();

  const { params, filterBy, closeModal } = modalControl;

  const [form] = Form.useForm();
  const { uploadedDocuments, uploadProps } = useFileUpload(type || "");

  const uploadFileState = useStore<any>(uploadFileStoreController.store);
  const documentTypesState = documentTypesStoreController ? useStore<any>(documentTypesStoreController.store) : {};

  useEffect(() => {
    if (documentTypesStoreController) {
      documentTypesStoreController.effect({});
    }

    return () => {
      uploadFileStoreController.reset();
    };
  }, []);

  useEffect(() => {
    if (uploadFileState.success) {
      openNotification("success", successNotificationMessage);
      callback && callback();
      closeModal();
    }
  }, [uploadFileState.success]);

  const onFinish = (formFields: TFormValues) => {
    const formData = new FormData();
    formData.append("file", uploadedDocuments[0]);

    const data = { ...params, file: formData };

    if (documentTypesState.data) {
      uploadAction({
        file: formData,
        documentType: formFields.documentType,
        params: params as T,
      });
    } else {
      uploadAction({
        file: formData,
        params: params as T,
      });
    }
  };

  return (
    <>
      <ModalCustom.Loading show={uploadFileState.loading} />

      <ModalCustom.Header>
        <ModalCustom.Title>{title}</ModalCustom.Title>
      </ModalCustom.Header>

      <ModalCustom.Error error={uploadFileState.error} />

      <ModalCustom.Middle>
        <FormCustom form={form} onFinish={onFinish} phantomSubmit>
          {documentTypesState.data && (
            <Form.Item label={label} name="documentType" rules={[{ required: true }]}>
              <Select loading={documentTypesState.data.loading} placeholder={t("placeholders.selectDocType")}>
                {(filterBy ? filterBy(documentTypesState.data) : documentTypesState.data).map((docType: any) => (
                  <Option value={docType.code} key={docType.code}>
                    {docType.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
          <Form.Item label={type ? `${t("doc")} (${t("only")} ${type})` : t("doc")} name="upload" rules={requiredRules}>
            <Upload {...uploadProps}>
              <ButtonCustom type="default">{t("selectFile", { ns: namespaces.buttons })}</ButtonCustom>
            </Upload>
          </Form.Item>
        </FormCustom>
      </ModalCustom.Middle>

      <ModalCustom.Footer>
        <ModalCustom.Buttons>
          <ButtonCustom type="primary" onClick={form.submit}>
            {t("send", { ns: namespaces.buttons })}
          </ButtonCustom>
        </ModalCustom.Buttons>
      </ModalCustom.Footer>
    </>
  );
};
