import React, { memo, useEffect, useMemo, useState } from "react";

import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { ButtonCustom } from "#ui/buttonCustom";
import { ModalCustom } from "#ui/modalCustom";
import qs from "query-string";
import { useTranslation } from "react-i18next";

const CREATE_CLIENT_TT_SET_MAP_DATA = "CREATE_CLIENT_TT_SET_MAP_DATA";

const areAddressEqual = (prevProps: any, nextProps: any): any => {
  try {
    return JSON.stringify(prevProps.address) === JSON.stringify(nextProps.address);
  } catch (e) {}
};

type TMessageData = {
  action: {
    data: {
      pmCoords: [number, number];
      centerCoords: [number, number];
    };
    type: string;
  };
};

export type TMapAddress = {
  regionName?: string;
  districtName?: string;
  street?: string;
  house?: string;
};

export type TMapData = {
  pmCoords: [number, number];
  centerCoords: [number, number];
} | null;

export type CoordsMapModalTypes = {
  address: TMapAddress;
  mapData: TMapData;
  onMapDataIncome: (data: TMapData) => void;
};

type PropsTypes = {
  modalControlType: ModalControlType<CoordsMapModalTypes>;
};

export const CoordsMap = memo((props: PropsTypes) => {
  const { modalControlType } = props;

  const { address, closeModal, mapData, onMapDataIncome } = modalControlType;

  const { t } = useTranslation();

  const [renderIframe, setRenderIframe] = useState(false);
  const [loadingIframe, setLoadingIframe] = useState(true);

  useEffect(() => {
    const onMessageCome = (e: MessageEvent<TMessageData>) => {
      if (e.data && e.data.action) {
        if (e.data.action.type === CREATE_CLIENT_TT_SET_MAP_DATA) {
          onMapDataIncome(e.data.action.data);
        }
      }
    };

    window.addEventListener("message", onMessageCome, false);

    setTimeout(() => {
      // Solving the problem of slowing the ascent of the modal
      setRenderIframe(true);
    }, 200);

    return () => {
      window.removeEventListener("message", onMessageCome);
    };
  }, []);

  const mapQueryParams = useMemo(() => {
    const { regionName, districtName, street, house } = address;
    const searchAddress = `${t("uzbekistan")}, ${regionName || ""}${districtName ? ", " + districtName : ""}${
      street ? ", " + street : ""
    }${house ? ", " + house : ""}`;

    let zoom = 10;

    if (districtName) {
      zoom = 13;
      if (street) {
        zoom = 15;
        if (house) {
          zoom = 17;
        }
      }
    }

    const pmCoords = mapData?.pmCoords || "";
    const centerCoords = mapData?.centerCoords || mapData?.pmCoords || "";

    if (pmCoords) {
      // если есть точные координаты, то поиск по название городов, областей, улиц и домов не нужен
      return {
        zoom,
        pmCoords,
        centerCoords,
      };
    }

    return {
      search: searchAddress,
      zoom,
      pmCoords,
      centerCoords,
    };
  }, [mapData, address]);

  return (
    <>
      <ModalCustom.Header>
        <ModalCustom.Title>{t("showOnMap")}</ModalCustom.Title>
      </ModalCustom.Header>

      <ModalCustom.Loading show={loadingIframe} />

      <ModalCustom.Middle>
        {renderIframe && (
          <iframe
            onLoad={() => setLoadingIframe(false)}
            src={`https://support24.uz/ya-map?${qs.stringify(mapQueryParams, { arrayFormat: "comma" })}`}
            width="100%"
            height="600px"
          />
        )}
      </ModalCustom.Middle>

      <ModalCustom.Footer>
        <ButtonCustom type="primary" size="small" onClick={closeModal}>
          {t("ready", { ns: namespaces.buttons })}
        </ButtonCustom>
      </ModalCustom.Footer>
    </>
  );
}, areAddressEqual);
