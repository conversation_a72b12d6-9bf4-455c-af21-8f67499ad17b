import React, { <PERSON> } from "react";

import { ErrorResponse } from "#types/api";
import { ContentCustom } from "#ui/contentCustom";

type PropsTypes = {
  title: string;
  message: string;
};

export const ErrorView: FC<PropsTypes> = (props) => {
  const { title, message } = props;

  const error: ErrorResponse = {
    title,
    detail: message,
    status: 500,
  };

  return <ContentCustom error={error}>{""}</ContentCustom>;
};
