import React, { FC, useEffect, useState } from "react";

import { XHRState } from "#constructors/index";
import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { ButtonCustom } from "#ui/buttonCustom";
import { ModalCustom } from "#ui/modalCustom";
import { formatFileSize } from "#utils/helpers";
import { faFile } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";

import "./styles.scss";

export type UploadCaseFileModalType = {
  uploadedFile: {
    file: File;
    type: "doc" | "image";
  };
};

type PropsTypes = {
  modalControl: ModalControlType<UploadCaseFileModalType>;
  callback: () => void;
  onSendFileMessage: (data: any) => void;
  uploadFileState: XHRState<any>;
};

export const UploadChatFileModal: FC<PropsTypes> = (props) => {
  const { modalControl, callback, onSendFileMessage, uploadFileState } = props;

  const { t } = useTranslation();

  const { uploadedFile, closeModal } = modalControl;

  const [imageSrc, setImageSrc] = useState<any>(null);
  const [docFile, setDocFile] = useState<File | null>(null);

  useEffect(() => {
    if (uploadedFile.type === "image") {
      const reader = new FileReader();
      reader.onload = function (loadEvent) {
        if (loadEvent.target) {
          setImageSrc(loadEvent.target.result);
        }
      };
      reader.readAsDataURL(uploadedFile.file);
    }

    if (uploadedFile.type !== "image") {
      setDocFile(uploadedFile.file);
    }
  }, []);

  useEffect(() => {
    if (uploadFileState.data) {
      closeModal();
      callback();
    }
  }, [uploadFileState.data]);

  const renderName = (str: string) => {
    const lastDotIndex = str.lastIndexOf(".");

    return (
      <>
        <div className="uploaded-file-block__title__first">{str.slice(0, lastDotIndex)}</div>
        <div>{str.slice(lastDotIndex)}</div>
      </>
    );
  };

  const onFinish = () => {
    const file = new FormData();

    file.append("file", uploadedFile.file);
    onSendFileMessage({ file });
  };

  return (
    <>
      <ModalCustom.Loading show={uploadFileState.loading} />

      <ModalCustom.Header>
        <ModalCustom.Title>{t("fileTitle")}</ModalCustom.Title>
      </ModalCustom.Header>

      <ModalCustom.Middle>
        {imageSrc && (
          <div style={{ border: "1px solid #f0f0f0" }}>
            <img src={imageSrc} alt="pasted" style={{ maxWidth: "100%" }} />
          </div>
        )}
        {docFile && (
          <div className="uploaded-file-block">
            <div className="uploaded-file-block__icon">
              <FontAwesomeIcon className="svg" icon={faFile} />
            </div>
            <div>
              <div className="uploaded-file-block__title">{renderName(docFile.name)}</div>
              <div className="uploaded-file-block__size">{formatFileSize(docFile.size)}</div>
            </div>
          </div>
        )}
      </ModalCustom.Middle>

      <ModalCustom.Footer>
        <ModalCustom.Buttons>
          <ButtonCustom onClick={closeModal}>{t("cancel", { ns: namespaces.buttons })}</ButtonCustom>
          <ButtonCustom type="primary" onClick={() => onFinish()}>
            {t("send", { ns: namespaces.buttons })}
          </ButtonCustom>
        </ModalCustom.Buttons>
      </ModalCustom.Footer>
    </>
  );
};
