import React, { useEffect } from "react";

import { ICaseNoteEmployeeItemModel } from "#businessLogic/models/caseNote";
import { $caseNoteEmployees } from "#stores/caseNote";
import { useStore } from "effector-react";

type PropsTypes = {
  open: boolean;
  searchParam: string;
  onChange: (item: ICaseNoteEmployeeItemModel) => void;
};

export const ChatEmployeeSelect: React.FC<PropsTypes> = (props) => {
  const { searchParam, open, onChange } = props;

  const caseNoteEmployeesState = useStore($caseNoteEmployees.store);

  useEffect(() => {
    if (open) {
      $caseNoteEmployees.effect({
        size: 10,
        search: searchParam ? searchParam.replaceAll("_", " ") : undefined,
      });
    }
  }, [searchParam, open]);

  return (
    <div className={`tag-user-popup ${open ? "active" : ""} u-fancy-scrollbar`}>
      {caseNoteEmployeesState.data.map((item) => (
        <div key={item.id} className="tag-user-popup__item" onClick={() => onChange(item)}>
          {item.name} - <strong>{item.login}</strong>
        </div>
      ))}
    </div>
  );
};
