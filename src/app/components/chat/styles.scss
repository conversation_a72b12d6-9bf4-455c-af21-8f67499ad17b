@import "src/styles/variables.scss";

.custom-chat {
  position: relative;
  height: 100%;

  &__loading {
    position: absolute;
  }

  &__wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  &__head {
    font-size: 16px;
    line-height: 20px;
    font-weight: 500;
    padding: 0 0 20px 10px;
  }

  &__content-wrap {
    flex-grow: 1;
    padding: 10px 5px 10px 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border-radius: 6px;

    &:not(:hover) {
      .custom-chat__content {
        &.u-fancy-scrollbar {
          &::-webkit-scrollbar-track-piece {
            background: transparent;
          }

          &::-webkit-scrollbar-thumb:horizontal,
          &::-webkit-scrollbar-thumb:vertical {
            background: transparent;
          }
        }
      }
    }
  }

  &__content {
    min-height: 25px;
    display: flex;
    flex-direction: column-reverse;
    padding-right: 5px;
    overflow: auto;
    flex-grow: 1;
    scroll-behavior: smooth;
  }

  &__item {
    display: inline-flex;
    flex-direction: column;

    &.new-item {
      transition: height 0.2s ease, opacity 0.2s ease;
      opacity: 0;
      height: 0;

      &.expand {
        opacity: 1;
        height: auto;
      }
    }


    &:not(:first-child) {
      margin-bottom: 20px;
    }

    .messageText {
      word-break: break-all;
    }

    &.messageAuthor {
      align-items: end;
      padding-left: 30%;

      .messageContent {
        background-color: $primary;
        color: #fff;
        text-align: right;
      }

      .messageTop {
        justify-content: end;
      }
    }

    &.notMessageAuthor {
      align-items: start;
      padding-right: 30%;

      .messageContent {
        a {
          color: $textColor;
        }
      }
    }

    .messageTop {
      display: inline-flex;
      align-items: end;
      gap: 10px;
      margin-bottom: 5px;
      font-style: italic;
    }

    .messageContent {
      padding: 6px 10px;
      background-color: $gray05;
      border-radius: 4px;
      display: inline-flex;
      flex-direction: column;
      color: $textColor;
      font-weight: 500;
      white-space: pre-wrap;

      &.messageFileContent {
        padding: 10px;
      }

      &__date {
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        margin-top: 4px;
      }

      a {
        color: #fff;
      }
    }
  }

  .messageItemWrap {
    position: relative;

    textarea {
      min-height: 76px;
    }
  }

  &__form {
    padding-top: 10px;

    form {
      width: 100%;
    }

    &__actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;

      .uploadImg {

        .ant-upload.ant-upload-select-picture-card {
          width: auto;
          height: auto;
          margin: 0;
          border: none;
          background-color: transparent;

          .anticon {
            color: #BFBFBF;
          }
        }

        &__title {
          margin-left: 4px;
          color: $primary;
        }
      }
    }
  }
}