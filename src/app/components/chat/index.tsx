import React, { FC, useEffect, useRef, useState } from "react";

import { AttachmentIcon } from "#assets/icons";
import excel from "#assets/images/excel.png";
import pdf from "#assets/images/pdf.webp";
import { ICaseNoteByCaseListItemModel, ICaseNoteEmployeeItemModel } from "#businessLogic/models/caseNote";
import { DATE_FORMAT } from "#constants/index";
import { XHRState } from "#constructors/index";
import { useChatInput } from "#hooks/useChatInput";
import { useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $currentUser } from "#stores/account";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { formatDate, isImage } from "#utils/helpers";
import { Form, Image, Spin, Upload } from "antd";
import TextArea from "antd/es/input/TextArea";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { ChatEmployeeSelect } from "./employeeSelect";
import "./styles.scss";
import { UploadCaseFileModalType, UploadChatFileModal } from "./uploadFileModal";

enum messageType {
  TEXT = "TEXT",
  FILE = "FILE",
}

type CommonMessageType = ICaseNoteByCaseListItemModel & { isNew?: boolean }; // move it

type PropsTypes = {
  getMessages: () => void;
  messages: Array<CommonMessageType> | null;
  loading: boolean;
  onSendMessage: (data: any) => void;
  onSendFileMessage: (data: any) => void;
  createState: XHRState<any>;
  uploadFileState: XHRState<any>;
};

export const ChatComponent: FC<PropsTypes> = (props) => {
  const { getMessages, messages, loading, onSendMessage, onSendFileMessage, createState, uploadFileState } = props;

  const { t } = useTranslation();

  const chatParent = useRef<HTMLDivElement>(null);

  const currentUserState = useStore($currentUser.store);

  const {
    form,
    getTaggedEmployeeIds,
    onEmployeeSelect,
    onPressEnter,
    onSelectMessage,
    handlePaste,
    uploadedFile,
    setUploadedFile,
    openUserSelectPopover,
    userSearchVal,
    messageTextareaRef,
  } = useChatInput<ICaseNoteEmployeeItemModel>();

  const uploadFileModalControl = useModalControl<UploadCaseFileModalType>();

  useEffect(() => {
    if (createState.data) {
      form.resetFields();
    }
  }, [createState.data]);

  useEffect(() => {
    if (uploadedFile) {
      uploadFileModalControl.openModal({ uploadedFile });
    }
  }, [uploadedFile]);

  const onFinish = () => {
    const formFields = form.getFieldsValue(true);

    onSendMessage({
      message: formFields.message,
      taggedEmployeeIds: getTaggedEmployeeIds(),
    });
  };

  return (
    <div className="custom-chat">
      {loading && (
        <div className="abs-loader">
          <Spin size="small" />
        </div>
      )}

      <div className="custom-chat__wrap" ref={chatParent}>
        <div className="custom-chat__head">{t("chat")}</div>
        <div className="custom-chat__content-wrap">
          <div className="custom-chat__content u-fancy-scrollbar">
            {messages && (
              <>
                {messages.length > 0 ? (
                  <>
                    {messages.map((item) => (
                      <ChatMessageItem key={item.id} item={item} currentUserId={currentUserState.data!.id} />
                    ))}
                  </>
                ) : (
                  <>{t("noRecords")}</>
                )}
              </>
            )}
          </div>
        </div>

        {/*<div style={{ border: "1px solid transparent" }} ref={messagesEndRef} />*/}

        <div className="custom-chat__form">
          <FormCustom form={form} onFinish={onFinish} phantomSubmit>
            <div className="messageItemWrap">
              <ChatEmployeeSelect
                open={openUserSelectPopover}
                onChange={onEmployeeSelect}
                searchParam={userSearchVal}
              />

              <Form.Item name="message" noStyle>
                <TextArea
                  ref={messageTextareaRef}
                  placeholder={t("messageText")}
                  onPressEnter={onPressEnter}
                  onPaste={handlePaste}
                  onSelect={onSelectMessage}
                  autoSize
                />
              </Form.Item>
            </div>
          </FormCustom>
          <div className="custom-chat__form__actions">
            <div className="uploadImg">
              <Upload
                name="file"
                listType="picture-card"
                showUploadList={false}
                beforeUpload={(value) => {
                  if (value.type.indexOf("image") === 0) {
                    setUploadedFile({
                      type: "image",
                      file: value,
                    });
                  } else {
                    setUploadedFile({
                      type: "doc",
                      file: value,
                    });
                  }
                }}
              >
                <AttachmentIcon />
                <span className="uploadImg__title">{t("uploadFile", { ns: namespaces.buttons })}</span>
              </Upload>
            </div>
            <ButtonCustom type="primary" onClick={form.submit} loading={createState.loading}>
              {t("send", { ns: namespaces.buttons })}
            </ButtonCustom>
          </div>
        </div>
      </div>
      <ModalCustom
        open={uploadFileModalControl.visible}
        onCancel={() => {
          setUploadedFile(null);
          uploadFileModalControl.closeModal();
        }}
      >
        <UploadChatFileModal
          modalControl={uploadFileModalControl}
          onSendFileMessage={onSendFileMessage}
          uploadFileState={uploadFileState}
          callback={() => {
            console.log("callback");
          }}
        />
      </ModalCustom>
    </div>
  );
};

const ChatMessageItem: FC<{ item: CommonMessageType; currentUserId: number }> = (props) => {
  const { item, currentUserId } = props;

  const { t } = useTranslation();

  const ref = useRef<HTMLDivElement>(null);

  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    const el = ref.current;
    if (el && item.isNew) {
      const height = el.scrollHeight;
      el.style.height = height + "px";
      // @ts-ignore
      el.style.opacity = 1;

      const onTransitionEnd = () => {
        el.style.height = "auto";
        el.removeEventListener("transitionend", onTransitionEnd);
      };

      el.addEventListener("transitionend", onTransitionEnd);
      setExpanded(true);
    }
  }, []);

  return (
    <div
      ref={ref}
      className={`custom-chat__item ${item.creatorId === currentUserId ? "messageAuthor" : "notMessageAuthor"} ${
        item.isNew ? "new-item" : ""
      } ${expanded ? "expand" : ""}`}
    >
      {item.messageType === messageType.TEXT && (
        <>
          <div className="messageTop">
            <span>{item.creatorFullName}</span>
          </div>

          <div className="messageContent">
            <div className="messageText">{item.message}</div>
            <div className="messageContent__date">{formatDate(item.createdDate, DATE_FORMAT)}</div>
          </div>
        </>
      )}

      {item.messageType === messageType.FILE && (
        <>
          <div className="messageTop">
            <span>{item.creatorFullName}</span>
            <span className="date">{formatDate(item.createdDate, DATE_FORMAT)}</span>
          </div>

          <div className="messageContent messageFileContent">
            {isImage(item.message) ? (
              <Image className="img" src={item.message} alt="image" />
            ) : (
              <a href={item.message} target="_blank" rel="noreferrer">
                <img
                  style={{ width: 40, height: 40, objectFit: "contain" }}
                  src={/pdf$/.test(item.message) ? pdf : excel}
                  alt={item.message}
                />
                {t("attachedFile")}
              </a>
            )}
          </div>
        </>
      )}
    </div>
  );
};
