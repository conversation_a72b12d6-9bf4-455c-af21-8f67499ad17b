import React from "react";

import { ConfigProvider } from "antd";
import ruLocale from "antd/es/locale/ru_RU";
import uzLocale from "antd/es/locale/uz_UZ";
import { I18nextProvider } from "react-i18next";
import { BrowserRouter, Route, Switch } from "react-router-dom";

import { i18n } from "#localization/i18n";
import { ELanguages } from "#localization/i18n.constants";

import "#styles/styles.scss";

import { ResetPassword } from "./screens/auth/resetPassword";
import { SignIn } from "./screens/auth/signIn";
import { Main } from "./screens/main";

const locales: Record<string, any> = {
  [ELanguages.RU]: ruLocale,
  [ELanguages.UZ]: uzLocale,
};

export const App = () => {
  return (
    <ConfigProvider locale={locales[localStorage.getItem("i18nextLng") || ELanguages.RU]}>
      <I18nextProvider i18n={i18n}>
        <div className="site-main-wrapper">
          <div className="site-bg"></div>
          <BrowserRouter>
            <Switch>
              <Route path="/sign-in" component={SignIn} />
              <Route path="/reset-password" component={ResetPassword} />
              <Route component={Main} />
            </Switch>
          </BrowserRouter>
        </div>
      </I18nextProvider>
    </ConfigProvider>
  );
};
