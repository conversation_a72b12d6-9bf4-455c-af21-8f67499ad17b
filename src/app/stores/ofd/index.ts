import { api } from "#businessLogic/api";
import { ICheckCompanyByTinModel } from "#businessLogic/models/ofd";
import { XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $checkCompanyByTin = createXHRStore(
  api.ofd.checkCompanyByTin,
  new XHRState<ICheckCompanyByTinModel | null>(null),
);

export const $confirmCustomer = createXHRStore(api.ofd.confirmCustomerFiscal, new XHRSuccessState());
