import { api } from "#businessLogic/api";
import { PaginationListModel, TIdCodeNameModel } from "#businessLogic/models/common";
import { ICustomerDevicesListItemModel } from "#businessLogic/models/customerDevices";
import { PaginationList, XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $customerDevicesList = createXHRStore(
  api.customerDevices.getCustomerDevicesList,
  new XHRState<PaginationListModel<ICustomerDevicesListItemModel>>(new PaginationList()),
);

export const $createCustomerDevice = createXHRStore(api.customerDevices.createCustomerDevice, new XHRSuccessState());

export const $deviceModels = createXHRStore(
  api.customerDevices.getDeviceModels,
  new XHRState<Array<TIdCodeNameModel>>([]),
  {
    doneReducer: (state, response) => {
      return {
        loading: false,
        data: response.result.data.filter((item) => item.code === "M1" || item.code === "P10"),
        error: null,
      };
    },
  },
);
