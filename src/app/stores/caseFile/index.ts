import { api } from "#businessLogic/api";
import { ICaseFileByCaseListItemModel } from "#businessLogic/models/caseFile";
import { PaginationListModel } from "#businessLogic/models/common";
import { PaginationList, XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $caseFiles = createXHRStore(
  api.caseFile.getCaseFile,
  new XHRState<PaginationListModel<ICaseFileByCaseListItemModel>>(new PaginationList()),
);

export const $uploadCaseDocument = createXHRStore(api.caseFile.uploadCaseDocument, new XHRSuccessState());
