import { api } from "#businessLogic/api";
import { TRecordStore } from "#businessLogic/models/common";
import { IServiceBalance } from "#businessLogic/models/ledger";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $serviceBalanceInfo = createXHRStore(
  api.ledger.getServiceBalanceInfo,
  {} as TRecordStore<XHRState<IServiceBalance | null>>,
  {
    zeroReducer: (state, params) => {
      return {
        ...state,
        [params]: {
          loading: true,
          data: null,
          error: null,
        },
      };
    },
    doneReducer: (state, response) => {
      return {
        ...state,
        [response.params]: {
          loading: false,
          data: response.result.data,
          error: null,
        },
      };
    },
    failReducer: (state, response) => {
      return {
        ...state,
        [response.params]: {
          loading: false,
          data: null,
          error: response.error.response?.data,
        },
      };
    },
  },
);
