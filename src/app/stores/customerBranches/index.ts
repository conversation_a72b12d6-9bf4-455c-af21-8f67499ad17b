import { api } from "#businessLogic/api";
import { PaginationListModel } from "#businessLogic/models/common";
import {
  ICheckedCustomerBranchAddressDetailsModel,
  ICustomerBranchesListItemModel,
} from "#businessLogic/models/customerBranches";
import { PaginationList, XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $customerBranchesList = createXHRStore(
  api.customerBranches.getCustomerBranchesList,
  new XHRState<PaginationListModel<ICustomerBranchesListItemModel>>(new PaginationList()),
);

export const $createCustomerBranch = createXHRStore(api.customerBranches.createCustomerBranch, new XHRSuccessState());

export const $confirmCustomerBranch = createXHRStore(api.customerBranches.confirmCustomerBranch, new XHRSuccessState());

export const $uploadCustomerBranchFile = createXHRStore(
  api.customerBranches.uploadCustomerBranchFile,
  new XHRSuccessState(),
);

export const $checkedCustomerBranchAddress = createXHRStore(
  api.customerBranches.checkCustomerBranchAddress,
  new XHRState<ICheckedCustomerBranchAddressDetailsModel | null>(null),
);
