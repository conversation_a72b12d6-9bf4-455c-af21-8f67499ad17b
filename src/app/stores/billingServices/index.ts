import { api } from "#businessLogic/api";
import {
  IBillingGroupedServiceChildrenItemModel,
  IBillingGroupedServiceItemModel,
  IBillingServicesAgreementsListItemModel,
  IBillingSingleServiceItemModel,
  IPublicOfferModel,
} from "#businessLogic/models/billingServices";
import { PaginationListModel, TRecordStore } from "#businessLogic/models/common";
import { PaginationList, XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $agreementsList = createXHRStore(
  api.billingServices.getAgreementList,
  new XHRState<PaginationListModel<IBillingServicesAgreementsListItemModel>>(new PaginationList()),
);

export const $activateAgreement = createXHRStore(api.billingServices.activateAgreement, new XHRSuccessState());

export const $publicOffer = createXHRStore(
  api.billingServices.getPublicOffer,
  new XHRState<IPublicOfferModel | null>(null),
);

export const $groupedXizmats = createXHRStore(
  api.billingServices.getGroupedXizmats,
  new XHRState<Array<IBillingGroupedServiceItemModel>>([]),
);

export const $groupedXizmatChildren = createXHRStore(
  api.billingServices.getGroupedXizmatChildren,
  {} as TRecordStore<XHRState<Array<IBillingGroupedServiceChildrenItemModel>>>,
  {
    zeroReducer: (state, params) => {
      return {
        ...state,
        [params]: {
          loading: true,
          fulfilled: false,
          data: [],
          error: null,
        },
      };
    },
    doneReducer: (state, response) => {
      return {
        ...state,
        [response.params]: {
          loading: false,
          fulfilled: true,
          data: response.result.data,
          error: null,
        },
      };
    },
    failReducer: (state, response) => {
      return {
        ...state,
        [response.params]: {
          loading: false,
          fulfilled: true,
          data: [],
          error: response.error.response?.data,
        },
      };
    },
  },
);

export const $customerBillingBalance = createXHRStore(
  api.billingServices.getCustomerBillingBalance,
  new XHRState<number | null>(null),
  {},
);

export const $singleXizmats = createXHRStore(
  api.billingServices.getSingleXizmats,
  new XHRState<Array<IBillingSingleServiceItemModel>>([]),
);

export const $createAgreement = createXHRStore(api.billingServices.createAgreement, new XHRSuccessState());

export const $serviceCalculation = createXHRStore(
  api.billingServices.serviceCalculation,
  new XHRState<any | null>(null),
);
