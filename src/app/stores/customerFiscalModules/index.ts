import { api } from "#businessLogic/api";
import { TCustomerBalance } from "#businessLogic/models/customerFiscalModules";
import { XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $createCustomerFiscalModule = createXHRStore(
  api.customerFiscalModule.createCustomerFiscalModule,
  new XHRSuccessState(),
);

export const $fiscalization = createXHRStore(api.customerFiscalModule.fiscalization, new XHRSuccessState());

export const $customerBalance = createXHRStore(
  api.customerFiscalModule.getCustomerBalance,
  new XHRState<TCustomerBalance | null>(null),
);
