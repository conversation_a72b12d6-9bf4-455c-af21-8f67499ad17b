import { api } from "#businessLogic/api";
import { XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $downloadKKMApplicationFile = createXHRStore(
  api.customerBranchDevice.downloadDeviceApplicationFile,
  new XHRState<Blob | null>(null),
  {
    doneReducer: (state, payload) => {
      const url = URL.createObjectURL(payload.result.data);
      window.open(url, "_blank");

      return {
        ...state,
        loading: false,
      };
    },
  },
);

export const $customerBranchDevicesSyncSmartpos = createXHRStore(
  api.customerBranchDevice.customerBranchDevicesSyncSmartpos,
  new XHRSuccessState(),
);
