import { api } from "#businessLogic/api";

import { createXHRStore } from "#core/stateManager/createXHRStore";

import { PaginationList, XHRState } from "#constructors/index";
import { IApplicationsListItemModel } from "#businessLogic/models/applications";
import { PaginationListModel } from "#businessLogic/models/common";

export const $applicationsList = createXHRStore(
  api.applications.getApplicationsList,
  new XHRState<PaginationListModel<IApplicationsListItemModel>>(new PaginationList())
);
