import { api } from "#businessLogic/api";
import { TCodeNameModel, TIdNameModel, TRecordStore } from "#businessLogic/models/common";
import { IActivityTypesLookupModel } from "#businessLogic/models/public/activityTypes";
import { IReferenceTypeItemModel } from "#businessLogic/models/public/referencesTypes";
import { VatItemModel } from "#businessLogic/models/public/vat";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $referenceItemsTypes = createXHRStore(
  api.public.getReferenceItemsByType,
  {} as TRecordStore<XHRState<Array<IReferenceTypeItemModel>>>,
  {
    zeroReducer: (state, params) => {
      return {
        ...state,
        [params.type]: {
          loading: true,
          data: [],
          error: null,
        },
      };
    },
    doneReducer: (state, response) => {
      return {
        ...state,
        [response.params.type]: {
          fulfilled: true,
          loading: false,
          data: response.result.data,
          error: null,
        },
      };
    },
    failReducer: (state, response) => {
      return {
        ...state,
        [response.params.type]: {
          loading: false,
          data: [],
          error: response.error.response,
        },
      };
    },
  },
);

export const $activityTypeChildren = createXHRStore(
  api.public.getActivityTypesLookup,
  new XHRState<IActivityTypesLookupModel[]>([]),
);

export const $activityTypeParents = createXHRStore(
  api.public.getActivityTypesLookup,
  new XHRState<IActivityTypesLookupModel[]>([]),
);

export const $vatItems = createXHRStore(api.public.getVatItems, new XHRState<Array<VatItemModel>>([]));

export const $regionItems = createXHRStore(api.public.getRegionItems, new XHRState<TIdNameModel[]>([]));

export const $districtItems = createXHRStore(api.public.getDistrictItems, new XHRState<Array<TIdNameModel>>([]));

export const $customerBranchTypes = createXHRStore(
  api.public.getCustomerBranchTypes,
  new XHRState<Array<TCodeNameModel>>([]),
);

export const $bankTypes = createXHRStore(api.public.getBankTypes, new XHRState<TCodeNameModel[]>([]));

export const $bankCardTypes = createXHRStore(api.public.getBankCardTypes, new XHRState<TCodeNameModel[]>([]));
