import { api } from "#businessLogic/api";
import { PaginationListModel } from "#businessLogic/models/common";
import { ICustomerDetailsModel, ICustomersListItemModel } from "#businessLogic/models/customers";
import { PaginationList, XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $customersList = createXHRStore(
  api.customers.getCustomersList,
  new XHRState<PaginationListModel<ICustomersListItemModel>>(new PaginationList()),
);

export const $customerDetails = createXHRStore(
  api.customers.getCustomerDetails,
  new XHRState<ICustomerDetailsModel | null>(null),
);

export const $createCustomer = createXHRStore(api.customers.createCustomer, new XHRSuccessState());

export const $customerSendPublicOffer = createXHRStore(api.customers.sendCustomersPublicOffer, new XHRSuccessState());

export const $customerVerifyPublicOffer = createXHRStore(
  api.customers.verifyCustomersPublicOffer,
  new XHRSuccessState(),
);

export const $verifyCustomer = createXHRStore(api.customers.verifyCustomer, new XHRSuccessState());

export const $customerActivationCode = createXHRStore(api.customers.getCustomerActivationCode, new XHRSuccessState());
