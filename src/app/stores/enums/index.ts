import { api } from "#businessLogic/api";
import { TCodeNameModel } from "#businessLogic/models/common";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $businessTypes = createXHRStore(api.enums.getBusinessTypes, new XHRState<Array<TCodeNameModel>>([]));

export const $customerAppTypes = createXHRStore(api.enums.getCustomerAppTypes, new XHRState<Array<TCodeNameModel>>([]));

export const $customerBranchDocumentTypes = createXHRStore(
  api.enums.getCustomerBranchDocumentTypes,
  new XHRState<Array<TCodeNameModel>>([]),
);
