import { api } from "#businessLogic/api";
import { XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $customerBankInfo = createXHRStore(api.customerBank.getCustomerBankInfo, new XHRState<any | null>(null));

export const $addCustomerBank = createXHRStore(api.customerBank.addCustomerBank, new XHRSuccessState());

export const $updateCustomerBank = createXHRStore(api.customerBank.updateCustomerBank, new XHRSuccessState());
