import { api } from "#businessLogic/api";
import {
  IAgreementInvoicesModel,
  IAgreementModel,
  IAgreementQuoteListItemModel,
  IQuoteDetailsModel,
} from "#businessLogic/models/agreement";
import { PaginationListModel, TRecordStore } from "#businessLogic/models/common";
import { PaginationList, XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $generatedInvoicesMap = createXHRStore(
  api.agreement.generatedInvoices,
  {} as TRecordStore<XHRState<string | null>>,
  {
    zeroReducer: (state, params) => {
      return {
        ...state,
        [params]: {
          loading: true,
          data: null,
          error: null,
        },
      };
    },
    doneReducer: (state, response) => ({
      ...state,
      [response.params]: {
        fulfilled: true,
        loading: false,
        data: response.result.data,
        error: null,
      },
    }),
    failReducer: (state, response) => ({
      ...state,
      [response.params]: {
        loading: false,
        data: null,
        error: response.error.response && response.error.response.data,
      },
    }),
  },
);

export const $quoteDetails = createXHRStore(
  api.agreement.getQuoteDetails,
  {} as TRecordStore<XHRState<IQuoteDetailsModel | null>>,
  {
    zeroReducer: (state, params) => {
      return {
        ...state,
        [params]: {
          loading: true,
          data: null,
          error: null,
        },
      };
    },
    doneReducer: (state, response) => {
      return {
        ...state,
        [response.params]: {
          loading: false,
          fulfilled: true,
          data: response.result.data,
          error: null,
        },
      };
    },
    failReducer: (state, response) => {
      return {
        ...state,
        [response.params]: {
          loading: false,
          fulfilled: true,
          data: null,
          error: response.error.response?.data,
        },
      };
    },
  },
);

export const $generatedQuote = createXHRStore(
  api.agreement.getGeneratedQuote,
  {} as TRecordStore<XHRState<string | null>>,
  {
    zeroReducer: (state, params) => {
      return {
        ...state,
        [params]: {
          loading: true,
          data: null,
          error: null,
        },
      };
    },
    doneReducer: (state, response) => {
      return {
        ...state,
        [response.params]: {
          loading: false,
          fulfilled: true,
          data: response.result.data,
          error: null,
        },
      };
    },
    failReducer: (state, response) => {
      return {
        ...state,
        [response.params]: {
          loading: false,
          fulfilled: true,
          data: null,
          error: response.error.response?.data,
        },
      };
    },
  },
);

export const $uploadQuoteFile = createXHRStore(api.agreement.uploadQuoteFile, new XHRSuccessState());

export const $agreementQuoteList = createXHRStore(
  api.agreement.getAgreementQuoteList,
  new XHRState<PaginationListModel<IAgreementQuoteListItemModel>>(new PaginationList()),
);

export const $createAgreementQuote = createXHRStore(api.agreement.createAgreementQuote, new XHRSuccessState());

export const $agreementInvoices = createXHRStore(
  api.agreement.getAgreementInvoices,
  new XHRState<PaginationListModel<IAgreementInvoicesModel>>(new PaginationList()),
);

export const $agreementDetails = createXHRStore(
  api.agreement.getAgreementDetails,
  new XHRState<IAgreementModel | undefined>(undefined),
);
