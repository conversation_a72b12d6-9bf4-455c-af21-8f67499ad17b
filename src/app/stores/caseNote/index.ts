import { api } from "#businessLogic/api";
import { ICaseNoteByCaseListItemModel, ICaseNoteEmployeeItemModel } from "#businessLogic/models/caseNote";
import { TRecordStore } from "#businessLogic/models/common";
import { XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $caseNotes = createXHRStore(
  api.caseNote.getNotesByCase,
  {} as TRecordStore<XHRState<Array<ICaseNoteByCaseListItemModel>>>,
  {
    zeroReducer: (state, params) => {
      return {
        ...state,
        [params.caseId]: {
          fulfilled: state[params.caseId]?.fulfilled || false,
          loading: true,
          data: [],
          error: null,
        },
      };
    },
    doneReducer: (state, response) => {
      return {
        ...state,
        [response.params.caseId]: {
          fulfilled: true,
          loading: false,
          data: response.result.data.content,
          error: null,
        },
      };
    },
    failReducer: (state, response) => {
      return {
        ...state,
        [response.params.caseId]: {
          fulfilled: true,
          loading: false,
          data: [],
          error: response.error.response?.data,
        },
      };
    },
  },
);

export const $createCaseNote = createXHRStore(
  api.caseNote.createCaseNote,
  new XHRState<ICaseNoteByCaseListItemModel | null>(null),
);

export const $caseNoteEmployees = createXHRStore(
  api.caseNote.getCaseNoteEmployees,
  new XHRState<Array<ICaseNoteEmployeeItemModel>>([]),
);

export const $uploadCaseNoteFile = createXHRStore(
  api.caseNote.uploadCaseNoteFile,
  new XHRState<ICaseNoteByCaseListItemModel | null>(null),
);
