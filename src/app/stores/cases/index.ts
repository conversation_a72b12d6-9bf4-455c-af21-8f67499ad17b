import { api } from "#businessLogic/api";
import { ICaseDetailsModel, ICaseStatusHistoriesListItemModel, ICasesListItemModel } from "#businessLogic/models/cases";
import { IChangesLogsListItemModel } from "#businessLogic/models/changesLogs";
import { PaginationListModel } from "#businessLogic/models/common";
import { PaginationList, XHRState, XHRSuccessState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $customerCasesList = createXHRStore(
  api.cases.getCasesList,
  new XHRState<PaginationListModel<ICasesListItemModel>>(new PaginationList()),
);

export const $caseDetails = createXHRStore(api.cases.getCaseDetails, new XHRState<ICaseDetailsModel | null>(null));

export const $createCase = createXHRStore(api.cases.createCase, new XHRState<number | null>(null));

export const $updateCase = createXHRStore(api.cases.updateCase, new XHRState<number | null>(null));

export const $caseChangesLogs = createXHRStore(
  api.changesLogs.getChangesLogs,
  new XHRState<PaginationListModel<IChangesLogsListItemModel>>(new PaginationList()),
);

export const $caseStatusHistories = createXHRStore(
  api.cases.getCaseStatusHistories,
  new XHRState<Array<ICaseStatusHistoriesListItemModel>>([]),
);

export const $documentsByCase = createXHRStore(api.cases.getCaseTemplate, new XHRState<Array<string>>([]));

export const $changeCaseStatus = createXHRStore(api.cases.changeCaseStatus, new XHRSuccessState());
