import { api } from "#businessLogic/api";
import { PaginationListModel } from "#businessLogic/models/common";
import { IMerchantAcquiringListItemModel } from "#businessLogic/models/merchantAcquiring";
import { PaginationList, XHRState } from "#constructors/index";
import { createXHRStore } from "#core/stateManager/createXHRStore";

export const $merchantAcquiringList = createXHRStore(
  api.merchantAcquiring.getMerchantAcquiringList,
  new XHRState<PaginationListModel<IMerchantAcquiringListItemModel>>(new PaginationList()),
);

type TReferenceItemsTypesStore = Record<string, XHRState<number | null>>;

export const $checkMerchantAcquiringTurnover = createXHRStore(
  api.merchantAcquiring.merchantAcquiringTurnover,
  {} as TReferenceItemsTypesStore,
  {
    zeroReducer: (state, params) => {
      return {
        ...state,
        [params.tin + params.deviceSerial]: {
          loading: true,
          data: null,
          error: null,
        },
      };
    },
    doneReducer: (state, response) => {
      return {
        ...state,
        [response.params.tin + response.params.deviceSerial]: {
          loading: false,
          data: response.result.data,
          error: null,
        },
      };
    },
    failReducer: (state, response) => {
      return {
        ...state,
        [response.params.tin + response.params.deviceSerial]: {
          loading: false,
          data: null,
          error: response.error.response,
        },
      };
    },
  },
);
