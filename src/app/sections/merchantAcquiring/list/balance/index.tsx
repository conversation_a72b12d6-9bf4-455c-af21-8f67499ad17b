import React, { FC } from "react";

import hideText from "#assets/images/hide-text.svg";
import { TMerchantAcquiringListParams } from "#businessLogic/models/merchantAcquiring";
import { Price } from "#components/price";
import { $checkMerchantAcquiringTurnover } from "#stores/merchantAcquiring";
import { ButtonCustom } from "#ui/buttonCustom";
import { getLastMonthRange } from "#utils/helpers";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

type PropsTypes = {
  id: number;
  deviceSerial: string;
  tin: string;
  queryParams: TMerchantAcquiringListParams;
};

export const MerchantAcquiringBalance: FC<PropsTypes> = (props) => {
  const { deviceSerial, tin, queryParams } = props;

  const { t } = useTranslation();

  const checkMerchantAcquiringTurnoverState = useStore($checkMerchantAcquiringTurnover.store);

  const currentBalance = checkMerchantAcquiringTurnoverState[`${tin}${deviceSerial}`];

  const onCheckBalance = () => {
    const { from, to } = getLastMonthRange();
    $checkMerchantAcquiringTurnover.effect({
      deviceSerial: deviceSerial,
      tin: tin,
      fromDate: queryParams.from || from,
      toDate: queryParams.to || to,
    });
  };

  return (
    <div className={`table-balance ${currentBalance && !currentBalance.error ? "isRequested" : ""}`}>
      {currentBalance && currentBalance.data !== null && <Price value={currentBalance.data} />}

      {(currentBalance === undefined || currentBalance.data === null) && (
        <>
          <div className="hiddenImg">
            <img src={hideText} alt="" />
          </div>
          <div className="table-balance__button">
            <ButtonCustom
              type="default"
              size="small"
              fullWidth={true}
              onClick={onCheckBalance}
              loading={currentBalance?.loading || false}
            >
              {t("check")}
            </ButtonCustom>
          </div>
        </>
      )}
    </div>
  );
};
