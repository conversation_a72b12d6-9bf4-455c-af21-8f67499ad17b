import React, { FC } from "react";

import {
  TMerchantAcquiringListAdditionalParams,
  TMerchantAcquiringListParams,
} from "#businessLogic/models/merchantAcquiring";
import { RANGE_DATE_FORMAT } from "#constants/index";
import { FilterOnChangeType } from "#types/common";
import { DatePickerCustom } from "#ui/datePickerCustom";
import { FilterCustom } from "#ui/filterCustom";
import { InputCustom } from "#ui/inputCustom";
import { SelectCustom } from "#ui/selectCustom";
import { useTranslation } from "react-i18next";

type PropsTypes = {
  queryParams: TMerchantAcquiringListParams;
  additionalParams: TMerchantAcquiringListAdditionalParams;
  clearFilter: () => void;
  onFilterChange: FilterOnChangeType<TMerchantAcquiringListParams, TMerchantAcquiringListAdditionalParams>;
};

export const MerchantAcquiringFilter: FC<PropsTypes> = (props) => {
  const { queryParams, additionalParams, onFilterChange, clearFilter } = props;

  const { t } = useTranslation();

  return (
    <FilterCustom
      queryParams={queryParams}
      additionalParams={additionalParams}
      onFilterChange={onFilterChange}
      clearQueryParams={clearFilter}
      numberOfFilterItemsToRender={5}
      items={[
        {
          key: "search",
          label: t("search"),
          fieldType: "search",
          render: ({ params, onChange }) => (
            <InputCustom.Search value={params.search} onChange={(search) => onChange({ search })} />
          ),
        },
        // {
        //   key: "status",
        //   label: t("status"),
        //   render: () => (
        //
        //   )
        // },
        {
          key: ["from", "to"],
          label: t("dates"),
          render: ({ params, onChange }) => (
            <DatePickerCustom.Range
              size="small"
              from={params.from}
              to={params.to}
              format={RANGE_DATE_FORMAT}
              onChange={({ from, to }) =>
                onChange({
                  from,
                  to,
                })
              }
            />
          ),
        },
      ]}
    />
  );
};
