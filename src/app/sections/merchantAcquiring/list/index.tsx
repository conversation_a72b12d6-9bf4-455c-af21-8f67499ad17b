import React, { FC, useEffect, useMemo } from "react";

import { IMerchantAcquiringListItemModel } from "#businessLogic/models/merchantAcquiring";
import { useQueryParams } from "#hooks/useQueryParams";
import { $merchantAcquiringList } from "#stores/merchantAcquiring";
import { ContentCustom } from "#ui/contentCustom";
import { ContentHeader } from "#ui/contentHeader";
import { StatusCustom } from "#ui/status";
import { TableCustom, TableNumber } from "#ui/tableCustom";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { getAcquiringStatusColor } from "../helpers";
import { $merchantAcquiringFilterProps } from "../models";

import { MerchantAcquiringBalance } from "./balance";
import { MerchantAcquiringFilter } from "./filter";

export const MerchantAcquiringList: FC = () => {
  const { t } = useTranslation();

  const merchantAcquiringListState = useStore($merchantAcquiringList.store);

  const { queryParams, additionalParams, clearFilter, onFilterChange } = useQueryParams($merchantAcquiringFilterProps);

  const merchantAcquiringListData = merchantAcquiringListState.data;

  const getList = () => {
    $merchantAcquiringList.effect(queryParams);
  };

  useEffect(() => {
    getList();
  }, [queryParams]);

  const onPaginationChange = (page: number, size: number) => {
    onFilterChange({ page: page - 1 });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<IMerchantAcquiringListItemModel> = [
      {
        title: "#",
        dataIndex: "num",
        width: 30,
        render: (_, row, index) => (
          <TableNumber value={merchantAcquiringListData.size * merchantAcquiringListData.number + index + 1} />
        ),
      },
      {
        title: t("company"),
        dataIndex: "customer",
        render: (_, row) => (
          <>
            <div>
              <Link to={`/customers/${row.customer.id}`}>{row.customer.name}</Link>
            </div>
            <div>{row.customer.tin || row.customer.pinfl}</div>
          </>
        ),
      },
      {
        title: t("accountNumber"),
        dataIndex: "accountNumber",
        render: (_, row) => row.accountNumber,
      },
      {
        title: t("mfo"),
        dataIndex: "mfo",
        render: (_, row) => row.mfo,
      },
      {
        title: t("kkm"),
        dataIndex: "deviceSerialNumber",
        render: (_, row) => row.deviceSerialNumber,
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) => (
          <StatusCustom status={row.status.code} getStatusColor={getAcquiringStatusColor}>
            {row.status.name}
          </StatusCustom>
        ),
      },
      {
        title: t("operatorStatus"),
        dataIndex: "operatorStatus",
        render: (_, row) => <StatusCustom status={row.operatorStatus.code}>{row.operatorStatus.name}</StatusCustom>,
      },
      {
        title: t("turnover"),
        dataIndex: "turnover",
        width: 170,
        render: (_, row) => (
          <MerchantAcquiringBalance
            id={row.id}
            tin={row.customer?.tin || ""}
            deviceSerial={row.deviceSerialNumber}
            queryParams={queryParams}
          />
        ),
      },
    ];

    return columns;
  }, [merchantAcquiringListData.size, merchantAcquiringListData.number, queryParams]);

  return (
    <ContentCustom>
      <ContentHeader>
        <ContentHeader.Left>
          <MerchantAcquiringFilter
            queryParams={queryParams}
            additionalParams={additionalParams}
            clearFilter={clearFilter}
            onFilterChange={onFilterChange}
          />
        </ContentHeader.Left>
      </ContentHeader>
      <TableCustom
        fixedHead={true}
        rowKey="id"
        loading={merchantAcquiringListState.loading}
        dataSource={merchantAcquiringListData.content}
        columns={tableColumns}
        pagination={{
          total: merchantAcquiringListData.totalElements,
          pageSize: merchantAcquiringListData.size,
          current: queryParams.page ? queryParams.page + 1 : 1,
          onChange: onPaginationChange,
        }}
      />
    </ContentCustom>
  );
};
