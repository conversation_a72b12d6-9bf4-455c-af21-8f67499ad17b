import React, { FC, useEffect } from "react";

import { $customerDetails } from "#stores/customers";
import { ContentCustom } from "#ui/contentCustom";
import { useStore } from "effector-react";
import { RouteComponentProps } from "react-router-dom";

import { CustomerBranches } from "./branches";
import { CustomerCases } from "./cases";
import { ConnectedServices } from "./connectedServices";
import { CustomerDevices } from "./devices";
import { CustomerInfo } from "./info";
import "./styles.scss";

type PropsTypes = {} & RouteComponentProps<{
  id: string;
}>;

export const CustomerDetails: FC<PropsTypes> = (props) => {
  const { match } = props;

  const customerId = Number(match.params.id);

  const customerDetailsState = useStore($customerDetails.store);

  const getDetails = () => {
    $customerDetails.effect(customerId);
  };

  useEffect(() => {
    getDetails();

    return () => {
      $customerDetails.reset();
    };
  }, []);

  return (
    <ContentCustom error={customerDetailsState.error} loading={customerDetailsState.loading} refresh={getDetails}>
      {customerDetailsState.data && (
        <>
          <CustomerInfo data={customerDetailsState.data} />
          <CustomerBranches customerId={customerId} tin={customerDetailsState.data!.tin} />
          <CustomerDevices customerId={customerId} tin={customerDetailsState.data!.tin} />
          <ConnectedServices customerId={customerId} tin={customerDetailsState.data!.tin} />
          <CustomerCases customerId={customerId} tin={customerDetailsState.data!.tin} />
        </>
      )}
    </ContentCustom>
  );
};
