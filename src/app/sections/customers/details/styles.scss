@import "src/styles/variables.scss";

.customer-details {
  &__entity {
    &:not(:last-child) {
      margin-bottom: 26px;
    }

    &-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      &__actions {
        display: flex;
        align-items: center;

        & > * {
          margin-left: 20px;
        }

        .custom-select {
          min-width: 170px;

          .ant-select-selector {
            border-color: rgba($primary, 0.16);
          }

          .ant-select-selection-placeholder {
            color: $primary;
          }
        }
      }
    }
  }

  &__table-title {
    font-size: 18px;
    line-height: 22px;
    font-weight: 500;
  }

  .application-file-wrap {
    display: flex;
    align-items: center;
    margin: 3px 0 0;

    &:first-child {
      margin-top: 0;
    }

    &__type {
      font-size: 11px;
      color: #8a8a92;
    }
  }
}