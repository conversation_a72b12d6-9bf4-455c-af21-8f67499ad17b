import React, { FC, useEffect } from "react";

import { IBillingGroupedServiceItemModel, IBillingSingleServiceItemModel } from "#businessLogic/models/billingServices";
import { ModalControlType, useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $groupedXizmats, $publicOffer } from "#stores/billingServices";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification } from "#utils/helpers";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router";

import { ActivateServiceModal, ActivateServiceModalType } from "./components/activateServiceModal";
import { BalanceInfo } from "./components/balanceInfo";
import { ConfirmOfferModal, ConfirmOfferModalType } from "./components/confirmOfferModal";
import { GroupedServices } from "./components/groupedServices";
import { SingleServices } from "./components/singleServices";
import "./styles.scss";

export type AllServicesModalType = {
  customerId: number;
  tin: string;
};

type PropsTypes = {
  modalControl: ModalControlType<AllServicesModalType>;
  callback: () => void;
};

export const AllServicesModal: FC<PropsTypes> = (props) => {
  const { modalControl, callback } = props;

  const { customerId, tin } = modalControl;

  const { t } = useTranslation();

  const history = useHistory();

  const publicOfferState = useStore($publicOffer.store);
  const groupedXizmatsState = useStore($groupedXizmats.store);

  const activateServiceModalControl = useModalControl<ActivateServiceModalType>();
  const confirmOfferModalControl = useModalControl<ConfirmOfferModalType>();

  useEffect(() => {
    $publicOffer.effect(tin);

    return () => {
      $publicOffer.reset();
    };
  }, []);

  useEffect(() => {
    if (publicOfferState.error && publicOfferState.error.status === 404) {
      openNotification("warning", publicOfferState.error.title, publicOfferState.error.detail);
      confirmOfferModalControl.openModal({ customerId });
    }
  }, [publicOfferState.error]);

  const onActivateService = (e: any) => {
    const service: IBillingGroupedServiceItemModel | IBillingSingleServiceItemModel = JSON.parse(
      e.currentTarget.getAttribute("data-option"),
    );

    activateServiceModalControl.openModal({
      service,
      tin,
      customerId,
    });
  };

  const cancelCallback = () => {
    confirmOfferModalControl.closeModal();
  };

  return (
    <>
      <ModalCustom.Loading show={publicOfferState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>
          <div className="all-services-head">
            <div className="all-services-head__title">
              {t("tariffsForYou", { ns: namespaces.customers })} ({groupedXizmatsState.data.length})
            </div>
            <div className="all-services-head__balance">
              <BalanceInfo tin={tin} />
            </div>
          </div>
        </ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        {publicOfferState.data && (
          <>
            <GroupedServices tin={tin} onActivateService={onActivateService} />

            <SingleServices tin={tin} onActivateService={onActivateService} />

            <ModalCustom open={activateServiceModalControl.visible} onCancel={activateServiceModalControl.closeModal}>
              <ActivateServiceModal modalControl={activateServiceModalControl} />
            </ModalCustom>
          </>
        )}
      </ModalCustom.Middle>
      <ModalCustom.Footer></ModalCustom.Footer>

      <ModalCustom open={confirmOfferModalControl.visible} onCancel={cancelCallback}>
        <ConfirmOfferModal modalControl={confirmOfferModalControl} cancelCallback={cancelCallback} />
      </ModalCustom>
    </>
  );
};
