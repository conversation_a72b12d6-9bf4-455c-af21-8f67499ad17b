import React, { FC, useEffect } from "react";

import { IBillingGroupedServiceItemModel, IBillingSingleServiceItemModel } from "#businessLogic/models/billingServices";
import { Price } from "#components/price";
import { requiredRules } from "#constants/index";
import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $currentUser } from "#stores/account";
import { $createAgreement, $serviceCalculation } from "#stores/billingServices";
import { $customerDetails } from "#stores/customers";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification, validateFormNumberInput } from "#utils/helpers";
import { Form, Input } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export type ActivateServiceModalType = {
  tin: string;
  customerId: number;
  service: IBillingGroupedServiceItemModel | IBillingSingleServiceItemModel;
};

type PropsTypes = {
  modalControl: ModalControlType<ActivateServiceModalType>;
};

export const ActivateServiceModal: FC<PropsTypes> = (props) => {
  const { modalControl } = props;

  const { t } = useTranslation();

  const [form] = Form.useForm();

  const currentUserState = useStore($currentUser.store);
  const createAgreementState = useStore($createAgreement.store);
  const serviceCalculationState = useStore($serviceCalculation.store);

  const currentUser = currentUserState.data;
  const { tin: customerTin, service, customerId, closeModal } = modalControl;

  useEffect(() => {
    return () => {
      $createAgreement.reset();
      $serviceCalculation.reset();
    };
  }, []);

  useEffect(() => {
    if (createAgreementState.success) {
      openNotification("success", t("success", { ns: namespaces.notifications }));
      closeModal();
      $customerDetails.effect(customerId);
    }
  }, [createAgreementState.success]);

  const calcServicePrice = (instance: number) => {
    $serviceCalculation.effect({
      xizmatId: service.id,
      tin: customerTin,
      instance,
    });
  };

  const onIndividualCountChange = () => {
    $serviceCalculation.reset();
  };

  const onSubmit = () => {
    const individualCount = form.getFieldValue("individualCount");

    if (service.individual && !serviceCalculationState.data) {
      calcServicePrice(individualCount);

      return;
    }

    $createAgreement.effect({
      inn: customerTin,
      serviceType: service.serviceType,
      instances: service.individual ? individualCount : null,
      xizmatId: service.id,
      firstName: currentUser!.firstName,
      lastName: currentUser!.lastName,
      smartposCreatorId: currentUser!.id,
    });
  };

  return (
    <>
      <ModalCustom.Loading show={createAgreementState.loading} />

      <ModalCustom.Header>
        <ModalCustom.Title>{t("connectService")}</ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        <FormCustom form={form} onFinish={onSubmit} phantomSubmit>
          <div>
            <div className="m-b-10">
              {t("title")}: <strong>{service.title}</strong>
            </div>
            <div>
              {service.individual ? (
                <>
                  {serviceCalculationState.data && (
                    <>
                      {t("servicePrice")}: <Price value={serviceCalculationState.data} />
                    </>
                  )}
                  <Form.Item
                    name="individualCount"
                    label={t("value")}
                    rules={[...requiredRules, { validator: validateFormNumberInput(3), min: 1 }]}
                  >
                    <Input
                      placeholder={t("placeholders.inputQty")}
                      className="custom-input"
                      onChange={onIndividualCountChange}
                    />
                  </Form.Item>
                </>
              ) : (
                <>
                  {t("servicePrice")}: <Price value={service.calculatedFee} />
                </>
              )}
            </div>
          </div>
        </FormCustom>
      </ModalCustom.Middle>

      <ModalCustom.Footer>
        <ModalCustom.Buttons>
          <ButtonCustom size="small" type="primary" onClick={() => form.submit()}>
            {service.individual && !serviceCalculationState.data
              ? t("calcPrice")
              : t("connect", { ns: namespaces.buttons })}
          </ButtonCustom>
        </ModalCustom.Buttons>
      </ModalCustom.Footer>
    </>
  );
};
