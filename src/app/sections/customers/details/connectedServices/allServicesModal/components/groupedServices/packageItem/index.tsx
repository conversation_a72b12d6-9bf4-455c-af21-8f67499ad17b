import React, { FC, useState } from "react";

import { ArrowDownIcon } from "#assets/icons";
import { IBillingGroupedServiceItemModel, IBillingSingleServiceItemModel } from "#businessLogic/models/billingServices";
import { i18n } from "#localization/i18n";
import { namespaces } from "#localization/i18n.constants";
import { ButtonCustom } from "#ui/buttonCustom";
import { formatNumber } from "#utils/helpers";
import { Collapse } from "antd";
import { useTranslation } from "react-i18next";

import { PackageItemChildren } from "./packageChildren";

type PropsTypes = {
  packageItem: IBillingGroupedServiceItemModel;
  onActivateService: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
};

export const getServiceRecurringPeriod = (
  packageItem: IBillingGroupedServiceItemModel | IBillingSingleServiceItemModel,
) => {
  if (!packageItem.recurring) {
    return "";
  }

  return `${i18n.t("intervalCount", {
    ns: namespaces.customers,
    // @ts-ignore
    intervalCount: packageItem.recurringModel.intervalCount,
    // @ts-ignore
    intervalName: packageItem.recurringModel.interval.nameRu,
  })}`;
};

export const PackageItem: FC<PropsTypes> = (props) => {
  const { packageItem, onActivateService } = props;

  const { t } = useTranslation();

  const [openedDescription, setOpenedDescription] = useState(false);

  const onChangeCollapse = () => setOpenedDescription((prev) => !prev);

  return (
    <div className="packageServicesListItemOuter">
      <div className="packageService-cont">
        <div className="packageService-content">
          <div className="packageService-name">{packageItem.title}</div>
          <div className="priceCont">
            <span className="servicePrice w-s-n">
              <div className="servicePrice__sum">
                <strong>{formatNumber(packageItem.calculatedFee)}</strong> <span>UZS</span>
              </div>
              <div className="servicePrice__recurring">{getServiceRecurringPeriod(packageItem)}</div>
            </span>
          </div>
          <Collapse className="wFull" onChange={onChangeCollapse} expandIcon={ArrowDownIcon}>
            <Collapse.Panel
              key="1"
              header={openedDescription ? t("hideDescription") : t("more", { ns: namespaces.buttons })}
              className="wFull"
            >
              <PackageItemChildren id={packageItem.id} />
            </Collapse.Panel>
          </Collapse>
        </div>
        <div className="buttonCont">
          <ButtonCustom
            type="primary"
            size="small"
            data-option={JSON.stringify(packageItem)}
            onClick={onActivateService}
            fullWidth
            block
          >
            {t("connect", { ns: namespaces.buttons })}
          </ButtonCustom>
        </div>
      </div>
    </div>
  );
};
