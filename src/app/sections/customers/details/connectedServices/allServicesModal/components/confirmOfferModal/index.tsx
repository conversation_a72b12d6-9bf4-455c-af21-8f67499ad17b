import React, { FC, useState } from "react";

import { ModalControlType } from "#hooks/useModalControl";

import { ConfirmOfferStep1 } from "./step1";
import { ConfirmOfferStep2 } from "./step2";

export type ConfirmOfferModalType = {
  customerId: number;
};

type PropsTypes = {
  modalControl: ModalControlType<ConfirmOfferModalType>;
  cancelCallback: () => void;
};

export const ConfirmOfferModal: FC<PropsTypes> = (props) => {
  const { modalControl, cancelCallback } = props;

  const [step, setStep] = useState(1);

  return (
    <>
      {step === 1 && (
        <ConfirmOfferStep1 setStep={setStep} closeModal={cancelCallback} customerId={modalControl.customerId} />
      )}
      {step === 2 && (
        <ConfirmOfferStep2 setStep={setStep} closeModal={cancelCallback} customerId={modalControl.customerId} />
      )}
    </>
  );
};
