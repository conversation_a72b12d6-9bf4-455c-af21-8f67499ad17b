import React, { FC, useEffect } from "react";

import { Price } from "#components/price";
import { $customerBillingBalance } from "#stores/billingServices";
import { Spin } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

type PropsTypes = {
  tin: string;
};

export const BalanceInfo: FC<PropsTypes> = (props) => {
  const { tin } = props;

  const { t } = useTranslation();

  const { data: customerBalance, loading } = useStore($customerBillingBalance.store);

  useEffect(() => {
    if (customerBalance === null) {
      $customerBillingBalance.effect(tin);
    }

    return () => {
      $customerBillingBalance.reset();
    };
  }, []);

  const val = customerBalance || 0;

  return (
    <Spin spinning={loading}>
      <div>
        <div>
          {t("balance")}:{" "}
          <strong className={val < 0 ? "danger-color" : ""}>
            <Price value={val} />
          </strong>
        </div>
      </div>
    </Spin>
  );
};
