import React, { FC, useEffect } from "react";

import { DoneIcon } from "#assets/icons";
import { $groupedXizmatChildren } from "#stores/billingServices";
import { SpinCustom } from "#ui/spinCustom";
import { useStore } from "effector-react";

type PropsTypes = {
  id: number;
};

export const PackageItemChildren: FC<PropsTypes> = (props) => {
  const { id } = props;

  const groupedXizmatChildrenState = useStore($groupedXizmatChildren.store);

  const currentItemChildrenState = groupedXizmatChildrenState[id];

  useEffect(() => {
    $groupedXizmatChildren.effect(id);
  }, []);

  if (!currentItemChildrenState || currentItemChildrenState.loading) {
    return (
      <div className="packageContLoading">
        <SpinCustom size="small" />
      </div>
    );
  }

  return (
    <div className="packageCont">
      {currentItemChildrenState.data.map((item) => (
        <div key={item.id} className="packageItem">
          <div className="packageItemIcon">
            <DoneIcon />
          </div>
          <div className="packageItemName">{item.title}</div>
        </div>
      ))}
    </div>
  );
};
