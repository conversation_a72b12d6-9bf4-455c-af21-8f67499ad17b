@import "src/styles/variables.scss";

.packageServicesListItemOuter {
  padding: 0 8px 16px;
  width: 25%;
  max-width: 400px;
  min-width: 300px;
}

.wFull {
  width: 100%;
}

.packageService-cont {
  border: 1px solid $gray01;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.packageService-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 20px 20px 12px 20px;

  .ant-collapse {
    background-color: transparent;

    & > .ant-collapse-item {
      border-color: rgba($primary, 0.16);

      & > .ant-collapse-header {
        justify-content: center;
        align-items: center;
        color: $primary;

        .ant-collapse-header-text {
          flex: none;
          order: 1;
          margin-right: 4px;
          font-weight: 500;
        }

        .ant-collapse-expand-icon {
          order: 2;
          line-height: 0;

          svg {
            margin: 0;
            width: 14px;
            height: 14px;
          }
        }
      }
    }
  }
}

.packageService-name {
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  min-height: 40px;
}

.priceCont {
  & .w-s-n {
    white-space: normal;
  }

  & .servicePrice {
    display: block;
    margin-bottom: 24px;

    &__sum {
      display: block;
      font-size: 30px;
      line-height: 40px;
      font-weight: 700;

      & strong {
        color: #1D2939;
        font-size: 30px;
        font-weight: 700;
      }
    }

    &__recurring {
      display: block;
      color: #667085;
      font-size: 12px;
      font-weight: 400;
    }
  }
}

.packageItem {
  display: flex;
  margin-bottom: 10px;
}

.packageItemIcon {
  padding: 1px 0 0;
  color: #16B364;
  font-size: 0;
  line-height: 0;
}

.packageItemName {
  margin-left: 9px;
  font-weight: 500;
  font-size: 12px;
  color: #1D2939;
}

.buttonCont {
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
}

.packageCont {
  display: flex;
  width: 100%;
  flex-direction: column;
}

.packageContLoading {
  text-align: center;
}

.packageServicesList {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px 0;
}