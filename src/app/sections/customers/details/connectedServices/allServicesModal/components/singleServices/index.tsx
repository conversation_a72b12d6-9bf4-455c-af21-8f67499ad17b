import React, { FC, useEffect } from "react";

import { IBillingSingleServiceItemModel } from "#businessLogic/models/billingServices";
import { namespaces } from "#localization/i18n.constants";
import { $singleXizmats } from "#stores/billingServices";
import { ButtonCustom } from "#ui/buttonCustom";
import { TableCustom, TableNumber } from "#ui/tableCustom";
import { formatNumber } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { getServiceRecurringPeriod } from "../groupedServices/packageItem";

type PropsTypes = {
  onActivateService: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  tin: string;
};

export const SingleServices: FC<PropsTypes> = (props) => {
  const { onActivateService, tin } = props;

  const { t } = useTranslation();

  const singleXizmatsState = useStore($singleXizmats.store);

  const getList = () => {
    $singleXizmats.effect({
      companyInn: tin,
    });
  };

  useEffect(() => {
    getList();

    return () => {
      $singleXizmats.reset();
    };
  }, []);

  const columns: ColumnsType<IBillingSingleServiceItemModel> = [
    {
      title: "#",
      dataIndex: "number",
      width: 40,
      render: (_, row, index) => <TableNumber value={index + 1} />,
    },
    {
      title: t("title"),
      dataIndex: "title",
      render: (_, row) => row.title,
    },
    {
      title: t("purchaseType"),
      dataIndex: "recurring",
      render: (_, row) => (row.recurring ? t("recurring") : t("singleService")),
    },
    {
      title: t("tariffType"),
      dataIndex: "pricingModel",
      render: (_, row) => (row.pricingModel ? row.pricingModel.nameRu : "-"),
    },
    {
      title: t("serviceAmount"),
      dataIndex: "price",
      render: (_, row) => (
        <span className="servicePrice w-s-n">
          <div className="servicePrice__sum">
            <strong>{formatNumber(row.calculatedFee)}</strong> <span>UZS</span>
          </div>
          <div className="servicePrice__recurring">{getServiceRecurringPeriod(row)}</div>
        </span>
      ),
    },
    {
      title: "",
      dataIndex: "action",
      width: 120,
      render: (_, row) => (
        <ButtonCustom type="primary" size="small" data-option={JSON.stringify(row)} onClick={onActivateService}>
          {t("connect", { ns: namespaces.buttons })}
        </ButtonCustom>
      ),
    },
  ];

  return (
    <TableCustom
      dataSource={singleXizmatsState.data}
      columns={columns}
      loading={singleXizmatsState.loading}
      pagination={{
        pageSize: 20,
        hideOnSinglePage: true,
      }}
      sticky
      expandable={{ showExpandColumn: false }}
    />
  );
};
