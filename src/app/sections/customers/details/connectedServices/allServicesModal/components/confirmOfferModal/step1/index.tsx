import React, { FC, useEffect } from "react";

import { namespaces } from "#localization/i18n.constants";
import { $customerSendPublicOffer } from "#stores/customers";
import { ButtonCustom } from "#ui/buttonCustom";
import { ModalCustom } from "#ui/modalCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

type PropsTypes = {
  setStep: React.Dispatch<React.SetStateAction<number>>;
  closeModal: () => void;
  customerId: number;
};

export const ConfirmOfferStep1: FC<PropsTypes> = (props) => {
  const { setStep, closeModal, customerId } = props;

  const customerSendPublicOfferState = useStore($customerSendPublicOffer.store);

  const { t } = useTranslation();

  useEffect(() => {
    if (customerSendPublicOfferState.success) {
      $customerSendPublicOffer.reset();
      setStep(2);
    }
  }, [customerSendPublicOfferState.success]);

  const onConfirmOfferStart = () => {
    $customerSendPublicOffer.effect({
      customerId,
    });
  };

  return (
    <>
      <ModalCustom.Loading show={customerSendPublicOfferState.loading} />

      <ModalCustom.Header>
        <ModalCustom.Title>{t("offerNotActivated", { ns: namespaces.customers })}</ModalCustom.Title>
      </ModalCustom.Header>

      <ModalCustom.Error error={customerSendPublicOfferState.error} />

      <ModalCustom.Footer>
        <ModalCustom.Buttons>
          <ButtonCustom type="default" onClick={closeModal}>
            {t("cancel", { ns: namespaces.buttons })}
          </ButtonCustom>
          <ButtonCustom type="primary" onClick={onConfirmOfferStart}>
            {t("sign", { ns: namespaces.buttons })}
          </ButtonCustom>
        </ModalCustom.Buttons>
      </ModalCustom.Footer>
    </>
  );
};
