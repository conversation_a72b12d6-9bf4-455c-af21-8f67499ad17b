import React, { FC, useEffect, useState } from "react";

import { SmsCodeField } from "#components/smsCodeField";
import { namespaces } from "#localization/i18n.constants";
import { $customerSendPublicOffer, $customerVerifyPublicOffer } from "#stores/customers";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification } from "#utils/helpers";
import { Form } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

type PropsTypes = {
  setStep: React.Dispatch<React.SetStateAction<number>>;
  closeModal: () => void;
  customerId: number;
};

type TInitialValues = {
  code: string;
};

export const ConfirmOfferStep2: FC<PropsTypes> = (props) => {
  const { setStep, closeModal, customerId } = props;

  const { t } = useTranslation();

  const customerVerifyPublicOfferState = useStore($customerVerifyPublicOffer.store);

  const [countDownActive, setCountDownActive] = useState(true);
  const [form] = Form.useForm<TInitialValues>();

  useEffect(() => {
    return () => {
      $customerVerifyPublicOffer.reset();
      $customerSendPublicOffer.reset();
    };
  }, []);

  useEffect(() => {
    if (customerVerifyPublicOfferState.success) {
      openNotification("success", t("successSmsVerification", { ns: namespaces.notifications }));
      closeModal();

      $customerVerifyPublicOffer.reset();
    }
  }, [customerVerifyPublicOfferState.success]);

  useEffect(() => {
    if (customerVerifyPublicOfferState.error && customerVerifyPublicOfferState.error.status === 400) {
      form.setFields([{ name: "code", errors: [customerVerifyPublicOfferState.error.title] }]);
    }
  }, [customerVerifyPublicOfferState.error]);

  const onResentSmsCode = () => {
    setCountDownActive(true);

    $customerSendPublicOffer.effect({
      customerId,
    });
  };

  const onFinish = (values: TInitialValues) => {
    $customerVerifyPublicOffer.effect({
      customerId,
      code: values.code,
    });
  };

  return (
    <>
      <ModalCustom.Loading show={customerVerifyPublicOfferState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>{t("confirmSms")}</ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        <FormCustom form={form} onFinish={onFinish} phantomSubmit>
          <Form.Item
            label={t("smsCode")}
            name="code"
            rules={[
              {
                pattern: /^(?:\d*)$/,
                message: t("onlyNum", { ns: namespaces.customers }),
              },
              {
                pattern: /^[\d]{6}$/,
                message: t("validations.sms"),
              },
            ]}
          >
            <SmsCodeField
              codeSize={6}
              error={form.getFieldError("code").length}
              countDownActive={countDownActive}
              onTimerFinish={() => setCountDownActive(false)}
            />
          </Form.Item>
          {countDownActive === false && (
            <>
              <ButtonCustom type="primary" size="small" onClick={onResentSmsCode}>
                {t("sendAggAgain", { ns: namespaces.buttons })}
              </ButtonCustom>
            </>
          )}
        </FormCustom>
      </ModalCustom.Middle>

      <ModalCustom.Footer>
        <ModalCustom.Buttons>
          <ButtonCustom type="default" onClick={() => setStep(2)}>
            {t("cancel", { ns: namespaces.buttons })}
          </ButtonCustom>
          <ButtonCustom type="primary" onClick={form.submit} disabled={countDownActive === false}>
            {t("confirm", { ns: namespaces.buttons })}
          </ButtonCustom>
        </ModalCustom.Buttons>
      </ModalCustom.Footer>
    </>
  );
};
