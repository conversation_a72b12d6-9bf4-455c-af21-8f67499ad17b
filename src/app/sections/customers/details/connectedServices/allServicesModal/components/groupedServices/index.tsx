import React, { FC, useEffect } from "react";

import { $groupedXizmats } from "#stores/billingServices";
import { useStore } from "effector-react";

import { PackageItem } from "./packageItem";
import "./styles.scss";

type PropsTypes = {
  tin: string;
  onActivateService: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
};

export const GroupedServices: FC<PropsTypes> = (props) => {
  const { tin, onActivateService } = props;

  const { data: groupedXizmats } = useStore($groupedXizmats.store);

  useEffect(() => {
    $groupedXizmats.effect({
      companyInn: tin,
    });

    return () => {
      $groupedXizmats.reset();
    };
  }, []);

  return (
    <div className="packageServicesList">
      {groupedXizmats.map((item) => (
        <PackageItem key={item.id} packageItem={item} onActivateService={onActivateService} />
      ))}
    </div>
  );
};
