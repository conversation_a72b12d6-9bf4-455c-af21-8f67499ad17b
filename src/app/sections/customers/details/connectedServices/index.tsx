import React, { FC, useEffect, useMemo, useState } from "react";

import {
  IBillingServicesAgreementsListItemModel,
  TBillingServicesAgreementsListParamsType,
} from "#businessLogic/models/billingServices";
import { TCustomerBranchesListParams } from "#businessLogic/models/customerBranches";
import { Price } from "#components/price";
import { DATE_FORMAT } from "#constants/index";
import { useModalControl } from "#hooks/useModalControl";
import { i18n } from "#localization/i18n";
import { namespaces } from "#localization/i18n.constants";
import { $activateAgreement, $agreementsList } from "#stores/billingServices";
import { ButtonCustom } from "#ui/buttonCustom";
import { DrawerCustom } from "#ui/drawerCustom";
import { PopoverCustom } from "#ui/popoverCustom";
import { SelectCustom } from "#ui/selectCustom";
import { StatusCustom } from "#ui/status";
import { TableCustom } from "#ui/tableCustom";
import { formatDate, openNotification } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { AllServicesModal, AllServicesModalType } from "./allServicesModal";

type PropsTypes = {
  customerId: number;
  tin: string;
};

const uniqArray = (array: Array<any>) => {
  const hash: any = {};
  return array.filter((item) => {
    return hash[item] ? false : (hash[item] = true);
  });
};

const getAgreementTariff = (agreement: IBillingServicesAgreementsListItemModel): any => {
  if (agreement.xizmat) {
    return agreement.xizmat.title;
  } else if (agreement.agreements) {
    return uniqArray(agreement.agreements?.map((item: any) => getAgreementTariff(item))).join(", ");
  } else {
    return "-";
  }
};

const getAgreementRecurring = (agreement: IBillingServicesAgreementsListItemModel): any => {
  if (agreement.xizmat) {
    return agreement.xizmat.recurring ? i18n.t("recurring") : i18n.t("singleService");
  } else if (agreement.agreements) {
    return uniqArray(agreement.agreements?.map((item: any) => getAgreementRecurring(item))).join(", ");
  } else {
    return "-";
  }
};

export const ConnectedServices: FC<PropsTypes> = (props) => {
  const { customerId, tin } = props;

  const { t } = useTranslation();

  const agreementsListState = useStore($agreementsList.store);
  const activateAgreementState = useStore($activateAgreement.store);

  const [queryParams, setQueryParams] = useState<TBillingServicesAgreementsListParamsType>({
    companyId: customerId,
    inn: tin,
  });

  const allServicesModalControl = useModalControl<AllServicesModalType>();

  const { loading: agreementsLoading, data: agreementsData } = agreementsListState;

  const getList = () => {
    $agreementsList.effect(queryParams);
  };

  useEffect(() => {
    getList();

    return () => {
      $agreementsList.reset();
    };
  }, [queryParams, customerId]);

  useEffect(() => {
    if (activateAgreementState.success) {
      openNotification("success", t("serviceConfirmed", { ns: namespaces.customers }));
      $activateAgreement.reset();
      getList();
    }
  }, [activateAgreementState.success]);

  const onFilterChange = (params: Partial<TCustomerBranchesListParams>) => {
    setQueryParams({
      ...queryParams,
      ...params,
    });
  };

  const onPaginationChange = (page: number, size: number) => {
    onFilterChange({ page: page - 1 });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<IBillingServicesAgreementsListItemModel> = [
      {
        title: t("number"),
        dataIndex: "agreementNumber",
        render: (_, row) => (
          <Link to={`/customers/${customerId}/connected-services/${row.id}`}>{row.agreementNumber}</Link>
        ),
      },
      {
        title: t("service"),
        dataIndex: "serviceType",
        render: (_, row) => row.serviceType?.nameRu,
      },
      {
        title: t("tariff"),
        dataIndex: "tariff",
        render: (_, row) => getAgreementTariff(row),
      },
      {
        title: t("serviceType2"),
        dataIndex: "recurring",
        render: (_, row) => getAgreementRecurring(row),
      },
      {
        title: t("amount"),
        dataIndex: "total",
        render: (_, row) => <Price value={row.total} />,
      },
      {
        title: t("activationDate"),
        dataIndex: "firstActivatedDate",
        render: (_, row) => (row.firstActivatedDate ? formatDate(row.firstActivatedDate, DATE_FORMAT) : "-"),
      },
      {
        title: t("endDate"),
        dataIndex: "lastPeriodEnd",
        render: (_, row) => (row.lastPeriodEnd ? formatDate(row.lastPeriodEnd, DATE_FORMAT) : "-"),
      },
      {
        title: t("nextPayment"),
        dataIndex: "nextPeriodStart",
        render: (_, row) => (row.nextPeriodStart ? formatDate(row.nextPeriodStart, DATE_FORMAT) : "-"),
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) => <StatusCustom status={row.status.code}>{row.status.nameRu}</StatusCustom>,
      },
      {
        title: "",
        dataIndex: "actions",
        render: (_, row) => (
          <PopoverCustom
            placement="bottomRight"
            trigger="click"
            content={
              <>
                <PopoverCustom.ConfirmItem
                  title={t("areYouSure")}
                  onConfirm={() => {
                    $activateAgreement.effect({
                      agreementId: row.id,
                    });
                  }}
                  disabled={row.status.code !== "CREATED"}
                >
                  <ButtonCustom loading={activateAgreementState.loading} disabled={row.status.code !== "CREATED"}>
                    {t("confirm", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </PopoverCustom.ConfirmItem>
              </>
            }
          />
        ),
      },
    ];

    return columns;
  }, [agreementsData.size, agreementsData.number, activateAgreementState.loading]);

  return (
    <div className="customer-details__entity">
      <div className="customer-details__entity-head">
        <div className="customer-details__table-title">{t("connectedServices")}</div>
        <div className="customer-details__entity-head__actions">
          <SelectCustom
            size="small"
            allowClear={true}
            placeholder={t("placeholders.selectServiceType")}
            value={queryParams.recurring}
            onChange={(recurring) => onFilterChange({ recurring })}
          >
            {[
              { label: t("singleService"), code: "false" },
              { label: t("recurring"), code: "true" },
            ].map((item) => (
              <SelectCustom.Option value={item.code} key={item.code}>
                {item.label}
              </SelectCustom.Option>
            ))}
          </SelectCustom>

          <ButtonCustom
            type="primary"
            size="small"
            onClick={() => {
              allServicesModalControl.openModal({
                customerId,
                tin,
              });
            }}
          >
            {t("connectService")}
          </ButtonCustom>
        </div>
      </div>

      <TableCustom
        rowKey="id"
        dataSource={agreementsData.content}
        columns={tableColumns}
        loading={agreementsLoading}
        pagination={{
          total: agreementsData.totalElements,
          pageSize: agreementsData.size,
          current: queryParams.page ? queryParams.page + 1 : 1,
          onChange: onPaginationChange,
        }}
      />

      <DrawerCustom open={allServicesModalControl.visible} onCancel={allServicesModalControl.closeModal} width={1070}>
        <AllServicesModal modalControl={allServicesModalControl} callback={getList} />
      </DrawerCustom>
    </div>
  );
};
