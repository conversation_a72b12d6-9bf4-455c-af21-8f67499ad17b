import React, { useEffect } from "react";

import { AgreementCard } from "#components/agreementCard";
import { $customerDetails } from "#stores/customers";
import { SpinCustom } from "#ui/spinCustom";
import { useStore } from "effector-react";
import { useParams, useRouteMatch } from "react-router-dom";

export const ConnectedServicesDetails = () => {
  const match = useRouteMatch();
  const { customerId } = useParams<{ customerId: string }>();

  const customerDetailsState = useStore($customerDetails.store);

  useEffect(() => {
    $customerDetails.effect(Number(customerId));
  }, [customerId]);

  if (!customerDetailsState.data) {
    return <SpinCustom size="large" />;
  }

  const { tin } = customerDetailsState.data;

  return <AgreementCard tin={tin} match={match} backUrl={`/customers/${customerId}`} />;
};
