@import "src/styles/variables.scss";

.customer-details__info {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: $gray02;
  padding: 20px;
  margin-bottom: 16px;
  border-radius: 8px;

  &__left {

  }

  &__right {
    display: flex;
    flex-wrap: wrap;
  }

  &__field {
    padding: 0 40px;

    &__label {
      font-size: 12px;
      line-height: 15px;
      color: $gray03;
      margin-bottom: 6px;
    }

    &__value {
      line-height: 20px;
    }
  }

  &__name {
    font-size: 18px;
    line-height: 20px;
    font-weight: 500;
    text-transform: uppercase;
  }
}

.customer-details__activate {
  margin-bottom: 10px;
  column-gap: 10px;
}