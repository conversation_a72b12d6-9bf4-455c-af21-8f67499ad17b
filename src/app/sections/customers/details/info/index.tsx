import React, { FC, ReactNode, useMemo } from "react";

import { ICustomerDetailsModel } from "#businessLogic/models/customers";
import { useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $customerDetails } from "#stores/customers";
import { ButtonCustom } from "#ui/buttonCustom";
import { DrawerCustom } from "#ui/drawerCustom";
import { StatusCustom } from "#ui/status";
import { formatPhoneNumber } from "#utils/helpers";
import { Row } from "antd";
import { useTranslation } from "react-i18next";

import { CustomerActivationModal, TCustomerActivationModalProps } from "./components/activationCustomerModal";
import {
  ADD_EDIT_BANK_MODAL_TYPES,
  CustomerAddEditBankModal,
  TCustomerAddEditBankModalProps,
} from "./components/customerAddEditBankModal";
import "./styles.scss";

type PropsTypes = {
  data: ICustomerDetailsModel;
};

export const CustomerInfo: FC<PropsTypes> = (props) => {
  const { data } = props;

  const { t } = useTranslation();

  const customerActivationModalControl = useModalControl<TCustomerActivationModalProps>();
  const customerAddEditBankModalControl = useModalControl<TCustomerAddEditBankModalProps>();

  const fields = useMemo(() => {
    const columns: Array<{ label: string; value: ReactNode }> = [
      { label: t("oked"), value: data.oked || "-" },
      {
        label: t("activityType"),
        value: data.activityType?.name || "-",
      },
      {
        label: t("address"),
        value: data.address ? (
          <>
            <div>
              {data.address.region.name}, {data.address.district.name}
            </div>
          </>
        ) : (
          "-"
        ),
      },
      {
        label: t("phone"),
        value: formatPhoneNumber(data.phone),
      },
      {
        label: t("status"),
        value: <StatusCustom status={data.status.code}>{data.status.name}</StatusCustom>,
      },
    ];

    return columns;
  }, [data]);

  return (
    <>
      <Row justify="end" className="customer-details__activate">
        <ButtonCustom onClick={() => customerAddEditBankModalControl.openModal({ customerId: data.id })} size="small">
          {t("addBank", { ns: namespaces.buttons })}
        </ButtonCustom>
        <ButtonCustom
          disabled={data.status?.code !== "PENDING"}
          onClick={() => customerActivationModalControl.openModal({ customerId: data.id })}
          size="small"
        >
          {t("activate", { ns: namespaces.customers })}
        </ButtonCustom>
      </Row>
      <div className="customer-details__info">
        <div className="customer-details__info__left">
          <div className="customer-details__info__name">
            <div>{data.name}</div>
            <div>
              {data.businessType.name}&nbsp; {data.tin || data.pinfl}
            </div>
          </div>
        </div>
        <div className="customer-details__info__right">
          {fields.map((item, index) => (
            <div className="customer-details__info__field" key={index}>
              <div className="customer-details__info__field__label">{item.label}</div>
              <div className="customer-details__info__field__value">{item.value}</div>
            </div>
          ))}
        </div>
      </div>
      <DrawerCustom open={customerActivationModalControl.visible} onCancel={customerActivationModalControl.closeModal}>
        <CustomerActivationModal
          modalControl={customerActivationModalControl}
          callback={() => {
            $customerDetails.effect(data.id);
          }}
        />
      </DrawerCustom>
      <DrawerCustom
        open={customerAddEditBankModalControl.visible}
        onCancel={customerAddEditBankModalControl.closeModal}
      >
        <CustomerAddEditBankModal
          modalControl={customerAddEditBankModalControl}
          afterClose={() => {
            $customerDetails.effect(data.id);
          }}
          modalType={ADD_EDIT_BANK_MODAL_TYPES.ADD}
        />
      </DrawerCustom>
    </>
  );
};
