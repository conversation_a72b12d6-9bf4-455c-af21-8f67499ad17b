import React, { FC, useEffect } from "react";

import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $customerActivationCode, $verifyCustomer } from "#stores/customers";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification } from "#utils/helpers";
import { Col, Form, Input, Row } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";

import classes from "./styles.module.scss";

export type TCustomerActivationModalProps = {
  customerId: number;
};

type PropsTypes = {
  callback?: () => void;
  modalControl: ModalControlType<TCustomerActivationModalProps>;
};

type TFormFields = {
  verificationCode: string;
};

export const CustomerActivationModal: FC<PropsTypes> = (props) => {
  const { callback, modalControl } = props;

  const { t } = useTranslation();

  const { customerId, closeModal } = modalControl;

  const history = useHistory();
  const [form] = Form.useForm<TFormFields>();

  const verifyCustomerState = useStore($verifyCustomer.store);
  const customerActivationCodeState = useStore($customerActivationCode.store);

  useEffect(() => {
    return () => {
      $verifyCustomer.reset();
      $customerActivationCode.reset();
    };
  }, []);

  useEffect(() => {
    if (customerActivationCodeState.success) {
      openNotification("success", t("activationSent", { ns: namespaces.notifications }));
    }
  }, [customerActivationCodeState.success]);

  useEffect(() => {
    if (verifyCustomerState.success) {
      $verifyCustomer.reset();
      openNotification("success", t("customerVerified", { ns: namespaces.notifications }));
      callback?.();
      history.push("/customers");
    }
  }, [verifyCustomerState.success]);

  const onSendActivationKey = () => {
    if (!customerActivationCodeState.success) {
      $customerActivationCode.effect(customerId);
      setTimeout($customerActivationCode.reset, 60000);
    }
  };

  const onSubmitClick = () => {
    form.submit();
  };

  const onFinish = (formFields: TFormFields) => {
    const formData = {
      id: customerId,
      activationCode: formFields.verificationCode,
    };

    $verifyCustomer.effect(formData);
  };

  return (
    <>
      <ModalCustom.Loading show={verifyCustomerState.loading} />

      <ModalCustom.Header>
        <ModalCustom.Title>{t("activation", { ns: namespaces.customers })}</ModalCustom.Title>
      </ModalCustom.Header>

      <ModalCustom.Error error={verifyCustomerState.error} />

      <ModalCustom.Middle>
        <div className={classes.sendActivationBtn}>
          <ButtonCustom
            disabled={customerActivationCodeState.success}
            type="default"
            size="small"
            onClick={onSendActivationKey}
          >
            {t("sendSmsConfirm", { ns: namespaces.buttons })}
          </ButtonCustom>
        </div>

        <FormCustom form={form} onFinish={onFinish} phantomSubmit>
          <Form.Item label={t("verificationCode")} name="verificationCode" rules={[{ required: true }]}>
            <Input type="number" placeholder={t("placeholders.inputVerificationCode")} />
          </Form.Item>
        </FormCustom>
      </ModalCustom.Middle>
      <ModalCustom.Footer>
        <Row justify="space-between" gutter={16}>
          <Col span={12}>
            <ButtonCustom size="small" fullWidth type="default" onClick={closeModal}>
              {t("cancel", { ns: namespaces.buttons })}
            </ButtonCustom>
          </Col>
          <Col span={12}>
            <ButtonCustom size="small" fullWidth type="primary" onClick={onSubmitClick}>
              {t("activate", { ns: namespaces.buttons })}
            </ButtonCustom>
          </Col>
        </Row>
      </ModalCustom.Footer>
    </>
  );
};
