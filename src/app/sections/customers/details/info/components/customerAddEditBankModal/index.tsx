import React, { FC, useEffect } from "react";

import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { BankSelect } from "#pickers/bankSelect";
import { $addCustomerBank, $customerBankInfo, $updateCustomerBank } from "#stores/customerBank";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification } from "#utils/helpers";
import { Checkbox, Form } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import InputMask from "react-input-mask";

export const ADD_EDIT_BANK_MODAL_TYPES = {
  ADD: "ADD",
  EDIT: "EDIT",
};

const initialValues = {
  default: false,
};

export type TCustomerAddEditBankModalProps = {
  customerId: number;
};

type PropsTypes = {
  afterClose: () => void;
  modalControl: ModalControlType<TCustomerAddEditBankModalProps>;
  modalType: (typeof ADD_EDIT_BANK_MODAL_TYPES)[keyof typeof ADD_EDIT_BANK_MODAL_TYPES];
};

export const CustomerAddEditBankModal: FC<PropsTypes> = ({ afterClose, modalControl, modalType }) => {
  const { t } = useTranslation();

  const { customerId, closeModal } = modalControl;

  const [form] = Form.useForm();

  const customerBankInfoState = useStore($customerBankInfo.store);
  const updateCustomerBankState = useStore($updateCustomerBank.store);
  const addCustomerBankState = useStore($addCustomerBank.store);

  useEffect(() => {
    if (modalType === ADD_EDIT_BANK_MODAL_TYPES.EDIT && customerId) {
      $customerBankInfo.effect(customerId);
    }
    return () => {
      $customerBankInfo.reset();
      $updateCustomerBank.reset();
      $addCustomerBank.reset();
    };
  }, []);

  useEffect(() => {
    if (customerBankInfoState.data) {
      const data = {
        bankId: customerBankInfoState.data.bank.id,
        accountNumber: customerBankInfoState.data.accountNumber,
        default: customerBankInfoState.data.default,
      };

      form.setFieldsValue(data);
    }
  }, [customerBankInfoState.data]);

  useEffect(() => {
    if (updateCustomerBankState.success || addCustomerBankState.success) {
      const successMessage =
        ADD_EDIT_BANK_MODAL_TYPES.EDIT === modalType
          ? t("bankUpdated", { ns: namespaces.notifications })
          : t("bankAdded", { ns: namespaces.notifications });
      openNotification("success", successMessage);
      afterClose();
      closeModal();
    }
  }, [updateCustomerBankState.success, addCustomerBankState.success, modalType]);

  const onFinish = (formFields: any) => {
    const formData = {
      customerId,
      bankId: formFields.bankId,
      accountNumber: formFields.accountNumber,
      default: formFields.default,
    };

    if (modalType === ADD_EDIT_BANK_MODAL_TYPES.EDIT) {
      $updateCustomerBank.effect({ ...formData, id: customerBankInfoState.data.id });
    } else {
      $addCustomerBank.effect(formData);
    }
  };

  return (
    <>
      <ModalCustom.Loading show={updateCustomerBankState.loading || addCustomerBankState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>
          {modalType === ADD_EDIT_BANK_MODAL_TYPES.EDIT
            ? t("editBank", { ns: namespaces.buttons })
            : t("addBank", { ns: namespaces.buttons })}
        </ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Error error={updateCustomerBankState.error || addCustomerBankState.error} />
      <ModalCustom.Middle>
        <FormCustom form={form} onFinish={onFinish} phantomSubmit initialValues={initialValues}>
          <Form.Item label={t("bank")} name="bankId" rules={[{ required: true }]}>
            <BankSelect defaultOption={customerBankInfoState.data?.bank} />
          </Form.Item>
          <Form.Item
            label={t("accountNumber")}
            name="accountNumber"
            rules={[
              {
                required: true,
                validator(_, value) {
                  if (value.length === 24) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("validations.wrongAccountNumber")));
                },
              },
            ]}
          >
            <InputMask
              className="ant-input custom-input"
              placeholder="XXXXX XXX X XXXXXXXX XXX"
              mask="99999 999 9 ******** 999"
              maskChar=""
            />
          </Form.Item>
          <Form.Item name="default" valuePropName="checked">
            <Checkbox className="custom-checkbox-2">{t("mainBak")}</Checkbox>
          </Form.Item>
        </FormCustom>
      </ModalCustom.Middle>

      <ModalCustom.Footer>
        <ModalCustom.Buttons>
          <ButtonCustom type="default" onClick={closeModal}>
            {t("cancel", { ns: namespaces.buttons })}
          </ButtonCustom>
          <ButtonCustom
            type="primary"
            onClick={() => {
              form.submit();
            }}
          >
            {modalType === ADD_EDIT_BANK_MODAL_TYPES.EDIT
              ? t("refresh", { ns: namespaces.buttons })
              : t("add", { ns: namespaces.buttons })}
          </ButtonCustom>
        </ModalCustom.Buttons>
      </ModalCustom.Footer>
    </>
  );
};
