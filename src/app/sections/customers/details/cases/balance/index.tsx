import React, { <PERSON> } from "react";

import hideText from "#assets/images/hide-text.svg";
import { Price } from "#components/price";
import { $serviceBalanceInfo } from "#stores/ledger";
import { ButtonCustom } from "#ui/buttonCustom";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

type PropsTypes = {
  tin: string;
};

export const CasesBalance: FC<PropsTypes> = (props) => {
  const { tin } = props;

  const { t } = useTranslation();

  const serviceBalanceInfoState = useStore($serviceBalanceInfo.store);

  const currentBalance = serviceBalanceInfoState[tin];

  const onCheckBalance = () => {
    $serviceBalanceInfo.effect(tin);
  };

  return (
    <div className={`table-balance ${currentBalance && !currentBalance.error ? "isRequested" : ""}`}>
      {currentBalance && currentBalance.data !== null && (
        <div>
          {currentBalance.data.needAmount < 0 ? t("debt") : t("prepayment")}:&nbsp;
          <strong className={currentBalance.data.needAmount < 0 ? "danger-color" : ""}>
            <Price value={currentBalance.data.needAmount} />
          </strong>
        </div>
      )}

      {(currentBalance === undefined || currentBalance.data === null) && (
        <>
          <div className="hiddenImg">
            <img src={hideText} alt="" />
          </div>
          <div className="table-balance__button">
            <ButtonCustom
              type="default"
              size="small"
              fullWidth={true}
              onClick={onCheckBalance}
              loading={currentBalance?.loading || false}
            >
              {t("check")}
            </ButtonCustom>
          </div>
        </>
      )}
    </div>
  );
};
