import React, { FC } from "react";

import { TCasesListAdditionalParams, TCasesListParams } from "#businessLogic/models/cases";
import { FilterOnChangeType } from "#types/common";
import { FilterCustom } from "#ui/filterCustom";
import { InputCustom } from "#ui/inputCustom";
import { useTranslation } from "react-i18next";

type PropsTypes = {
  queryParams: TCasesListParams;
  additionalParams: TCasesListAdditionalParams;
  clearFilter: () => void;
  onFilterChange: FilterOnChangeType<TCasesListParams, TCasesListAdditionalParams>;
};

export const CustomerCasesFilter: FC<PropsTypes> = (props) => {
  const { queryParams, additionalParams, onFilterChange, clearFilter } = props;

  const { t } = useTranslation();

  return (
    <FilterCustom
      queryParams={queryParams}
      additionalParams={additionalParams}
      onFilterChange={onFilterChange}
      items={[
        {
          key: "search",
          label: t("search"),
          fieldType: "search",
          render: ({ params, onChange }) => (
            <InputCustom.Search value={params.search} onChange={(search) => onChange({ search })} />
          ),
        },
      ]}
    />
  );
};
