import React, { FC, useEffect, useMemo, useState } from "react";

import { ICasesListItemModel, TCasesListParams } from "#businessLogic/models/cases";
import { useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $customerCasesList } from "#stores/cases";
import { ButtonCustom } from "#ui/buttonCustom";
import { DrawerCustom } from "#ui/drawerCustom";
import { PopoverCustom } from "#ui/popoverCustom";
import { StatusCustom } from "#ui/status";
import { TableCustom, TableNumber } from "#ui/tableCustom";
import { formatDate } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { getCaseStatusColor } from "../helpers";

import { AddEditCaseModal, AddEditCaseModalType } from "./addModal";
import { CasesBalance } from "./balance";
import { CustomerCasesFilter } from "./filter";

type PropsTypes = {
  customerId: number;
  tin: string;
};

export const CustomerCases: FC<PropsTypes> = (props) => {
  const { customerId, tin } = props;

  const { t } = useTranslation();

  const customerCasesListState = useStore($customerCasesList.store);

  const [queryParams, setQueryParams] = useState<TCasesListParams>({
    customerId,
  });

  const addEditCaseBranchModalControl = useModalControl<AddEditCaseModalType>();

  const customerBranchesListData = customerCasesListState.data;

  const getList = () => {
    $customerCasesList.effect(queryParams);
  };

  useEffect(() => {
    return () => {
      $customerCasesList.reset();
    };
  }, []);

  useEffect(() => {
    getList();
  }, [queryParams]);

  const onFilterChange = (params: TCasesListParams) => {
    setQueryParams({
      ...queryParams,
      ...params,
    });
  };

  const clearFilter = () => {
    setQueryParams({
      customerId,
    });
  };

  const onPaginationChange = (page: number, size: number) => {
    onFilterChange({ page: page - 1 });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<ICasesListItemModel> = [
      {
        title: "#",
        dataIndex: "rowNum",
        width: 30,
        render: (_, row, index) => (
          <TableNumber value={customerBranchesListData.size * customerBranchesListData.number + index + 1} />
        ),
      },
      {
        title: t("number"),
        dataIndex: "number",
        render: (_, row) => <Link to={`/customers/${customerId}/cases/${row.id}`}>{row.id}</Link>,
      },
      {
        title: t("kkm"),
        dataIndex: "deviceSerialNumber",
        render: (_, row) => row.data?.deviceSerialNumber || "-",
      },
      {
        title: t("type"),
        dataIndex: "caseType",
        render: (_, row) => `${row.caseType.name} ${row.data?.reason?.name || ""}`,
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) => (
          <StatusCustom status={row.status.code} getStatusColor={getCaseStatusColor}>
            {row.status.name}
          </StatusCustom>
        ),
      },
      {
        title: t("creationDate"),
        dataIndex: "createdDate",
        render: (_, row) => formatDate(row.createdDate),
      },
      {
        title: t("balance"),
        dataIndex: "balance",
        width: 170,
        render: () => <CasesBalance tin={tin} />,
      },
      {
        title: "",
        width: 40,
        dataIndex: "actions",
        render: (_, row) => (
          <PopoverCustom
            placement="bottomRight"
            trigger="click"
            content={
              <>
                <PopoverCustom.Item>
                  <ButtonCustom
                    onClick={() => {
                      addEditCaseBranchModalControl.openModal({
                        customerId,
                        caseId: row.id,
                      });
                    }}
                  >
                    {t("edit", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </PopoverCustom.Item>
              </>
            }
          />
        ),
      },
    ];

    return columns;
  }, [customerBranchesListData.size, customerBranchesListData.number, customerId]);

  return (
    <div className="customer-details__entity">
      <div className="customer-details__entity-head">
        <div className="customer-details__table-title">{t("cases")}</div>
        <div className="customer-details__entity-head__actions">
          <CustomerCasesFilter
            queryParams={queryParams}
            additionalParams={{}}
            onFilterChange={onFilterChange}
            clearFilter={clearFilter}
          />

          <ButtonCustom
            size="small"
            onClick={() => {
              addEditCaseBranchModalControl.openModal({
                customerId,
              });
            }}
          >
            {t("addCase", { ns: namespaces.buttons })}
          </ButtonCustom>
        </div>
      </div>
      <TableCustom
        rowKey="id"
        loading={customerCasesListState.loading}
        dataSource={customerBranchesListData.content}
        columns={tableColumns}
        pagination={{
          total: customerBranchesListData.totalElements,
          pageSize: customerBranchesListData.size,
          current: queryParams.page ? queryParams.page + 1 : 1,
          onChange: onPaginationChange,
        }}
      />

      <DrawerCustom open={addEditCaseBranchModalControl.visible} onCancel={addEditCaseBranchModalControl.closeModal}>
        <AddEditCaseModal modalControl={addEditCaseBranchModalControl} callback={getList} />
      </DrawerCustom>
    </div>
  );
};
