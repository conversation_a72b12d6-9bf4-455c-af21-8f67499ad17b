import React, { FC, useEffect, useMemo, useState } from "react";

import { IChangesLogsListItemModel, TChangesLogsListParams } from "#businessLogic/models/changesLogs";
import { DASH_DATE_FORMAT_WITH_TIME } from "#constants/index";
import { $caseChangesLogs, $caseDetails } from "#stores/cases";
import { TableCustom } from "#ui/tableCustom";
import { formatDate } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const CaseChangeLogs: FC = () => {
  const { t } = useTranslation();

  const caseDetailsState = useStore($caseDetails.store);
  const caseChangesLogsState = useStore($caseChangesLogs.store);

  const [params, setParams] = useState<TChangesLogsListParams>({
    entityId: caseDetailsState.data!.id,
    entityType: "CASES",
  });

  const caseChangesLogsData = caseChangesLogsState.data;

  const getList = () => {
    $caseChangesLogs.effect(params);
  };

  useEffect(() => {
    return () => {
      $caseChangesLogs.reset();
    };
  }, []);

  useEffect(() => {
    getList();
  }, [params]);

  const onPaginationChange = (page: number, size: number) => {
    setParams({ ...params, page: page - 1, size });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<IChangesLogsListItemModel> = [
      {
        title: "№",
        dataIndex: "number",
        width: 30,
        render: (_, row, index) => (
          <div className="w-s-n">{caseChangesLogsData.size * caseChangesLogsData.number + index + 1}</div>
        ),
      },
      {
        title: t("field"),
        dataIndex: "field",
        render: (_, row) => row.field,
      },
      {
        title: t("oldValue"),
        dataIndex: "oldValue",
        render: (_, row) => row.oldValue,
      },
      {
        title: t("newValue"),
        dataIndex: "newValue",
        render: (_, row) => row.newValue,
      },
      {
        title: t("date"),
        dataIndex: "updatedDate",
        render: (_, row) => formatDate(row.updatedDate, DASH_DATE_FORMAT_WITH_TIME),
      },
      {
        title: t("lastModifierFullName"),
        dataIndex: "updater",
        render: (_, row) => row.updater.name,
      },
    ];

    return columns;
  }, [caseChangesLogsData.size, caseChangesLogsData.number]);

  return (
    <TableCustom
      rowKey="id"
      loading={caseChangesLogsState.loading}
      dataSource={caseChangesLogsData.content}
      columns={tableColumns}
      pagination={{
        total: caseChangesLogsData.totalElements,
        pageSize: caseChangesLogsData.size,
        current: params.page ? params.page + 1 : 1,
        onChange: onPaginationChange,
      }}
    />
  );
};
