import React, { FC, useEffect, useMemo } from "react";

import { ICaseStatusHistoriesListItemModel } from "#businessLogic/models/cases";
import { DASH_DATE_FORMAT_WITH_TIME } from "#constants/index";
import { $caseDetails, $caseStatusHistories } from "#stores/cases";
import { StatusCustom } from "#ui/status";
import { TableCustom } from "#ui/tableCustom";
import { formatDate } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export const CaseStatusHistories: FC = () => {
  const { t } = useTranslation();

  const caseDetailsState = useStore($caseDetails.store);
  const caseStatusHistoriesState = useStore($caseStatusHistories.store);

  useEffect(() => {
    return () => {
      $caseStatusHistories.reset();
    };
  }, []);

  useEffect(() => {
    $caseStatusHistories.effect(caseDetailsState.data!.id);
  }, []);

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<ICaseStatusHistoriesListItemModel> = [
      {
        title: "№",
        dataIndex: "number",
        width: 30,
        render: (_, row, index) => <div className="w-s-n">{index + 1}</div>,
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) =>
          row.status ? <StatusCustom status={row.status.code}>{row.status.name}</StatusCustom> : "-",
      },
      {
        title: t("duration"),
        dataIndex: "duration",
        render: (_, row) => (row.duration !== null ? `${row.duration} ${t("minutes")}` : "-"),
      },
      {
        title: t("creationDate"),
        dataIndex: "createdDate",
        render: (_, row) => formatDate(row.createdDate, DASH_DATE_FORMAT_WITH_TIME),
      },
      {
        title: t("author"),
        dataIndex: "creator",
        render: (_, row) => row.creator.name,
      },
    ];

    return columns;
  }, []);

  return (
    <TableCustom
      rowKey="id"
      loading={caseStatusHistoriesState.loading}
      dataSource={caseStatusHistoriesState.data}
      columns={tableColumns}
      pagination={false}
    />
  );
};
