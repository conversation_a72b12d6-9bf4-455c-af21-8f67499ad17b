import React, { FC, useEffect, useMemo } from "react";

import { ICaseStatusHistoriesListItemModel } from "#businessLogic/models/cases";
import { DASH_DATE_FORMAT_WITH_TIME } from "#constants/index";
import { $caseDetails, $caseStatusHistories } from "#stores/cases";
import { StatusCustom } from "#ui/status";
import { TableCustom } from "#ui/tableCustom";
import { formatDate } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";

export const CaseStatusHistories: FC = () => {
  const caseDetailsState = useStore($caseDetails.store);
  const caseStatusHistoriesState = useStore($caseStatusHistories.store);

  useEffect(() => {
    return () => {
      $caseStatusHistories.reset();
    };
  }, []);

  useEffect(() => {
    $caseStatusHistories.effect(caseDetailsState.data!.id);
  }, []);

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<ICaseStatusHistoriesListItemModel> = [
      {
        title: "№",
        dataIndex: "number",
        width: 30,
        render: (_, row, index) => <div className="w-s-n">{index + 1}</div>,
      },
      {
        title: "Статус",
        dataIndex: "status",
        render: (_, row) =>
          row.status ? <StatusCustom status={row.status.code}>{row.status.name}</StatusCustom> : "-",
      },
      {
        title: "Продолжительность",
        dataIndex: "duration",
        render: (_, row) => (row.duration !== null ? `${row.duration} мин` : "-"),
      },
      {
        title: "Дата создания",
        dataIndex: "createdDate",
        render: (_, row) => formatDate(row.createdDate, DASH_DATE_FORMAT_WITH_TIME),
      },
      {
        title: "Автор",
        dataIndex: "creator",
        render: (_, row) => row.creator.name,
      },
    ];

    return columns;
  }, []);

  return (
    <TableCustom
      rowKey="id"
      loading={caseStatusHistoriesState.loading}
      dataSource={caseStatusHistoriesState.data}
      columns={tableColumns}
      pagination={false}
    />
  );
};
