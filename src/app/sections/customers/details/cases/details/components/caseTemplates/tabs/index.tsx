import React, { useMemo } from "react";

import { PrinterIcon } from "#assets/icons";
import { ButtonCustom } from "#ui/buttonCustom";
import { printIframe } from "#utils/helpers";
import { Tabs } from "antd";
import { useTranslation } from "react-i18next";

import classes from "./styles.module.scss";

type PropsTypes = {
  templateData: Array<string> | null;
  caseId: number;
};

export const CaseTemplateTabs: React.FC<PropsTypes> = (props) => {
  const { templateData, caseId } = props;

  const { t } = useTranslation();

  const tabsItems = useMemo(() => {
    return (templateData && templateData.length ? templateData : []).map((v, i) => ({
      label: `${t("template")} ` + (i + 1),
      children: (
        <div className={classes.documentWrapper}>
          {v ? (
            <>
              <div className={classes.documentButtons}>
                <ButtonCustom type="primary" icon={<PrinterIcon />} onClick={() => printIframe(String(caseId))} />
              </div>
              <iframe id={String(caseId)} width="100%" height="1000px" srcDoc={v} />
            </>
          ) : null}
        </div>
      ),
      key: `${i}`,
    }));
  }, [templateData]);

  return <Tabs type="card" items={tabsItems} />;
};
