import React, { FC, useEffect } from "react";

import { $documentsByCase } from "#stores/cases";
import { Spin } from "antd";
import { useStore } from "effector-react";

import { CaseTemplateTabs } from "./tabs";

type PropsTypes = {
  caseId: number;
};

export const CaseTemplates: FC<PropsTypes> = ({ caseId }) => {
  const { data, loading } = useStore($documentsByCase.store);

  useEffect(() => {
    $documentsByCase.effect(caseId);
  }, [caseId]);

  if (loading) {
    return (
      <div className="details-card__loader">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <div className="case-details__templates">
        <CaseTemplateTabs templateData={data} caseId={caseId} />
      </div>
    </div>
  );
};
