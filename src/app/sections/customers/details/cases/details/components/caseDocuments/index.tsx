import React, { FC, useEffect, useMemo } from "react";

import { ICaseFileByCaseListItemModel } from "#businessLogic/models/caseFile";
import { UploadFileModal } from "#components/uploadFileModal";
import { DASH_DATE_FORMAT_WITH_TIME } from "#constants/index";
import { useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $caseFiles, $uploadCaseDocument } from "#stores/caseFile";
import { $caseDetails, $changeCaseStatus } from "#stores/cases";
import { ButtonCustom } from "#ui/buttonCustom";
import { DrawerCustom } from "#ui/drawerCustom";
import { PopoverCustom } from "#ui/popoverCustom";
import { TableCustom } from "#ui/tableCustom";
import { formatDate, openNotification } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { isEmpty } from "lodash";
import { useTranslation } from "react-i18next";

import { CASE_STATUSES, CASE_TYPES } from "../../../constants";

export const CaseDocuments: FC = () => {
  const { t } = useTranslation();

  const caseDetailsState = useStore($caseDetails.store);
  const caseFilesState = useStore($caseFiles.store);
  const changeCaseStatusState = useStore($changeCaseStatus.store);

  const uploadFileModalControl = useModalControl();

  const caseDetailsData = caseDetailsState.data;
  const caseId = caseDetailsState.data!.id;

  const getList = () => {
    $caseFiles.effect(caseId);
  };

  useEffect(() => {
    getList();

    return () => {
      $caseFiles.reset();
    };
  }, []);

  useEffect(() => {
    if (changeCaseStatusState.success) {
      $caseDetails.effect(caseId);
      getList();
      openNotification("success", t("statusUpdated", { ns: namespaces.notifications }));
      $changeCaseStatus.reset();
    }
  }, [changeCaseStatusState.success]);

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<ICaseFileByCaseListItemModel> = [
      {
        title: "№",
        dataIndex: "number",
        width: 30,
        render: (_, row, index) => index + 1,
      },
      {
        title: "Тип документа",
        dataIndex: "documentType",
        render: (_, row) => (
          <a href={row.fileUrl} target="_blank" rel="noreferrer">
            {row.documentType.name}
          </a>
        ),
      },
      {
        title: "Дата создания",
        dataIndex: "createdDate",
        render: (_, row) => formatDate(row.createdDate, DASH_DATE_FORMAT_WITH_TIME),
      },
      {
        title: "Кем изменено",
        dataIndex: "lastModifierFullName",
        render: (_, row) => row.lastModifierFullName,
      },
      {
        title: "",
        dataIndex: "actions",
        width: 40,
        render: (_, row) => (
          <PopoverCustom
            placement="bottomRight"
            trigger="click"
            content={
              <>
                <PopoverCustom.Item>
                  <ButtonCustom href={row.fileUrl} target="_blank">
                    {t("download", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </PopoverCustom.Item>
              </>
            }
          />
        ),
      },
    ];

    return columns;
  }, []);

  const isDisabled = useMemo(() => {
    const allowedCases = [
      CASE_TYPES.ACT_OF_RECONCILIATION,
      CASE_TYPES.TEMPORARY_REPLACEMENT_REVERT,
      CASE_TYPES.APP_TYPE_CHANGE,
      CASE_TYPES.DEACTIVATE_DEVICE,
      CASE_TYPES.ADD_CONSIGNOR,
      CASE_TYPES.DELETE_DEVICE_COMBINATION,
    ];

    if (allowedCases.includes(caseDetailsData?.caseType?.code || "")) {
      return false;
    }

    const isEmptyContent = isEmpty(caseFilesState.data?.content);

    if (
      caseDetailsData &&
      !isEmptyContent &&
      [CASE_STATUSES.DRAFT, CASE_STATUSES.DEFECTIVE].includes(caseDetailsData.status.code)
    ) {
      return false;
    }

    return true;
  }, [caseDetailsData, caseFilesState]);

  return (
    <div className="case-documents">
      <div className="case-documents__actions">
        <ButtonCustom
          disabled={changeCaseStatusState.loading || isDisabled}
          type="primary"
          size="small"
          onClick={() =>
            $changeCaseStatus.effect({
              status: "NEW",
              id: caseId,
            })
          }
        >
          {t("send", { ns: namespaces.buttons })}
        </ButtonCustom>
        <ButtonCustom
          size="small"
          type="primary"
          onClick={() => {
            uploadFileModalControl.openModal();
          }}
        >
          {t("add", { ns: namespaces.buttons })}
        </ButtonCustom>
      </div>
      <TableCustom
        rowKey={(row) => String(row.id)}
        loading={caseFilesState.loading}
        dataSource={caseFilesState.data.content}
        columns={tableColumns}
        pagination={false}
      />
      <DrawerCustom open={uploadFileModalControl.visible} onCancel={uploadFileModalControl.closeModal}>
        <UploadFileModal
          modalControl={uploadFileModalControl}
          uploadAction={(data) => {
            $uploadCaseDocument.effect({
              caseId,
              documentType: "APPLICATION",
              file: data.file,
            });
          }}
          callback={getList}
          uploadFileStoreController={$uploadCaseDocument}
        />
      </DrawerCustom>
    </div>
  );
};
