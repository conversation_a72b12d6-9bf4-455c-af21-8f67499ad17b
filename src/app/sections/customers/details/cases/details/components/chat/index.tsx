import React, { FC, useEffect, useMemo, useState } from "react";

import { ICaseNoteByCaseListItemModel } from "#businessLogic/models/caseNote";
import { ChatComponent } from "#components/chat";
import { $caseNotes, $createCaseNote, $uploadCaseNoteFile } from "#stores/caseNote";
import { $caseDetails } from "#stores/cases";
import { useStore } from "effector-react";

export const CaseDetailsChat: FC = () => {
  const caseDetailsState = useStore($caseDetails.store);
  const createCaseNoteState = useStore($createCaseNote.store);
  const uploadCaseNoteFileState = useStore($uploadCaseNoteFile.store);
  const caseNotesState = useStore($caseNotes.store);

  const [messages, setMessages] = useState<Array<ICaseNoteByCaseListItemModel & { isNew?: boolean }> | null>(null);

  const params = useMemo(() => {
    return {
      caseId: caseDetailsState.data!.id,
      size: 40,
      page: 0,
    };
  }, [caseDetailsState.data!.id]);

  const currentCaseNotesState = caseNotesState[params.caseId];

  const getMessages = () => {
    $caseNotes.effect(params);
  };

  useEffect(() => {
    return () => {
      $caseNotes.reset();
    };
  }, []);

  useEffect(() => {
    getMessages();
  }, [params]);

  useEffect(() => {
    if (currentCaseNotesState && currentCaseNotesState.fulfilled) {
      setMessages((prevState) => [...(prevState || []), ...currentCaseNotesState.data]);
    }
  }, [currentCaseNotesState]);

  useEffect(() => {
    if (createCaseNoteState.data) {
      addNewItem(createCaseNoteState.data);
      $createCaseNote.reset();
    }
  }, [createCaseNoteState.data]);

  useEffect(() => {
    if (uploadCaseNoteFileState.data) {
      addNewItem(uploadCaseNoteFileState.data);
      $uploadCaseNoteFile.reset();
    }
  }, [uploadCaseNoteFileState.data]);

  const addNewItem = (item: any) => {
    setMessages((prevState) => {
      return [{ ...item, isNew: true }, ...(prevState || [])];
    });
  };

  const onSendMessage = (data: { message: string; taggedEmployeeIds: Array<number> }) => {
    if (data.message && !createCaseNoteState.loading) {
      const formData = {
        caseId: params.caseId,
        message: data.message,
        taggedEmployeeIds: data.taggedEmployeeIds,
      };

      $createCaseNote.effect(formData);
    }
  };

  const onSendFileMessage = (data: any) => {
    $uploadCaseNoteFile.effect({ caseId: params.caseId, file: data.file });
  };

  return (
    <div className="case-details__chat">
      <ChatComponent
        getMessages={getMessages}
        messages={messages}
        loading={currentCaseNotesState?.loading || false}
        onSendMessage={onSendMessage}
        onSendFileMessage={onSendFileMessage}
        createState={createCaseNoteState}
        uploadFileState={uploadCaseNoteFileState}
      />
    </div>
  );
};
