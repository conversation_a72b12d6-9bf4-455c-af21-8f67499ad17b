export const caseDataFieldLabels: Record<string, string> = {
  activityTypeId: "Вид деятельности",
  address: "Адрес",
  apartment: "Квартира",
  branches: "Филиал",
  businessType: "Сфера деятельности",
  cadastreNumber: "Номер кадастра",
  companyName: "Название компании",
  currentDate: "Дата создания",
  currentPhoneNumber: "Текущий номер телефона",
  customerId: "Клиент Ид",
  description: "Описание",
  deviceId: "ККМ Ид",
  districtId: "Район",
  fileUrl: "Адрес файла",
  firstName: "Имя",
  fiscalModuleSerial: "Серийный номер Фискального Модуля",
  house: "Дом",
  lastName: "Фамилия",
  latitude: "Широта",
  longitude: "Долгота",
  lossFiscalModule: "Утеря Фискального Модуля",
  name: "Новое название",
  newDeviceSerialNumber: "Новый ККМ",
  newSerialNumber: "Новый серийный номер",
  patronymic: "Отчество",
  phone: "Телефон",
  phoneNumber: "Новый номер телефона",
  reason: "Причина",
  regionId: "Регион",
  street: "Улица",
  templateType: "Тип шаблона",
  vatId: "НДС",
  deviceSerialNumber: "Серийный номер ККМ",
  deviceSerialNumbers: "Серийный номер ККМ",
  accountNumber: "Расчетный счет",
  oked: "ОКЭД",
  fmSerialNumber: "Серийный номер ФМ",
  newFiscalModuleSerial: "Новый Серийный номер ФМ",
  mfo: "МФО",
  bankName: "Название банка",
  tariffName: "Тарифный план",
  newTariffName: "Новый тарифный план",
  activityTypeName: "Новый вид деятельности",
  customerBranchDevicesTariff: "Тариф",
  bankType: "Тип банка",
  paymentType: "Тип платежной системы",
  fullName: "ФИО",
  oldManagerName: "Старый менеджер",
  newManagerName: "Новый менеджер",
  rentNumber: "Номер аренды",
  rentStartDate: "Начало аренды",
  rentEndDate: "Конец аренды",
  secretKey: "Кодовое слово",
  deviceType: "Тип девайса",
  changeCtsType: "Смена ЦТО",
  agreementNumber: "Номер заказа",
  reasonName: "Основание",
  typeName: "Тип обращения",
  appType: "Тип приложения",
  fromTime: "Дата начала",
  toTime: "Дата окончания",
  scannerName: "Сканер",
  customerTin: "ИНН/ПИНФЛ клиента",
  deviceModelName: "Модель ККМ",
  purchaseDate: "Дата покупки ККМ",
  ofdStatus: "Статус регистрации ККМ в ГНИ",
  fiscalModuleSerialNumber: "Серийный номер ФМ",
  merchantId: "Мерчант ИД",
  terminalId: "Терминал ИД",
};
