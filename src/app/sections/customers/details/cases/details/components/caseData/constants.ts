import { i18n } from "#localization/i18n";

export const caseDataFieldLabels: Record<string, string> = {
  activityTypeId: i18n.t("activityTypeId"),
  address: i18n.t("address"),
  apartment: i18n.t("apartment"),
  branches: i18n.t("branches"),
  businessType: i18n.t("activityType"),
  cadastreNumber: i18n.t("cadastreNum"),
  companyName: i18n.t("companyName"),
  currentDate: i18n.t("currentDate"),
  currentPhoneNumber: i18n.t("currentPhoneNumber"),
  customerId: i18n.t("customerId"),
  description: i18n.t("description"),
  deviceId: i18n.t("deviceId"),
  districtId: i18n.t("districtId"),
  fileUrl: i18n.t("fileUrl"),
  firstName: i18n.t("firstName"),
  fiscalModuleSerial: i18n.t("fiscalModuleSerial"),
  house: i18n.t("house"),
  lastName: i18n.t("lastName"),
  latitude: i18n.t("latitude"),
  longitude: i18n.t("longitude"),
  lossFiscalModule: i18n.t("lossFiscalModule"),
  name: i18n.t("newCompanyName"),
  newDeviceSerialNumber: i18n.t("newDeviceSerialNumber"),
  newSerialNumber: i18n.t("newSerialNumber"),
  patronymic: i18n.t("patronymic"),
  phone: i18n.t("phone"),
  phoneNumber: i18n.t("newPhoneNumber"),
  reason: i18n.t("reason"),
  regionId: i18n.t("regionId"),
  street: i18n.t("street"),
  templateType: i18n.t("templateType"),
  vatId: i18n.t("vatId"),
  deviceSerialNumber: i18n.t("deviceSerialNumber"),
  deviceSerialNumbers: i18n.t("deviceSerialNumber"),
  accountNumber: i18n.t("accountNumber"),
  oked: i18n.t("oked"),
  fmSerialNumber: i18n.t("fmSerialNumber"),
  newFiscalModuleSerial: i18n.t("newFiscalModuleSerial"),
  mfo: i18n.t("mfo"),
  bankName: i18n.t("bankName"),
  tariffName: i18n.t("tariffName"),
  newTariffName: i18n.t("newTariffName"),
  activityTypeName: i18n.t("activityTypeName"),
  customerBranchDevicesTariff: i18n.t("customerBranchDevicesTariff"),
  bankType: i18n.t("bankType"),
  paymentType: i18n.t("paymentType2"),
  fullName: i18n.t("fullName"),
  oldManagerName: i18n.t("oldManagerName"),
  newManagerName: i18n.t("newManagerName"),
  rentNumber: i18n.t("rentNumber"),
  rentStartDate: i18n.t("rentStartDate"),
  rentEndDate: i18n.t("rentEndDate"),
  secretKey: i18n.t("secretWord"),
  deviceType: i18n.t("deviceType"),
  changeCtsType: i18n.t("changeCtsType"),
  agreementNumber: i18n.t("agreementNumber"),
  reasonName: i18n.t("reasonName"),
  typeName: i18n.t("typeName"),
  appType: i18n.t("appType"),
  fromTime: i18n.t("fromTime"),
  toTime: i18n.t("toTime"),
  scannerName: i18n.t("scannerName"),
  customerTin: i18n.t("customerTin2"),
  deviceModelName: i18n.t("deviceModelName"),
  purchaseDate: i18n.t("purchaseDate"),
  ofdStatus: i18n.t("ofdStatus2"),
  fiscalModuleSerialNumber: i18n.t("fiscalModuleSerialNumber"),
  merchantId: i18n.t("merchantId"),
  terminalId: i18n.t("terminalId"),
};
