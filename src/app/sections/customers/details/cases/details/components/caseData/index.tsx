import React, { FC, useMemo } from "react";

import { ICaseDetailsModel } from "#businessLogic/models/cases";
import { Link } from "react-router-dom";

import { caseDataFieldLabels } from "./constants";
import classes from "./styles.module.scss";

const changeLang = (v: any) => {
  return caseDataFieldLabels[v];
};

const lv = (label: string, value?: string, link?: string) => ({ label, value, link });

type PropsTypes = {
  caseDetails: ICaseDetailsModel;
};

export const CaseData: FC<PropsTypes> = (props) => {
  const { caseDetails } = props;
  const caseData = caseDetails.data;

  const isNeedShowLossFiscalModule = caseDetails.caseType?.code === "DEREGISTER_KKM";

  const fields = useMemo(() => {
    if (!caseData) {
      return [];
    }

    const fields: Array<{ label: string; value?: string; link: string | undefined }> = [];
    Object.entries(caseData).forEach(([key, value]) => {
      const link: string | undefined = undefined;

      if (key === "agreementNumber") {
        //link = `/customers/${caseData.customerId}/connected-services/${caseData.agreementId}`;
      }

      if (typeof value === "string") {
        fields.push(lv(key, value, link));
      } else if (value === null) {
      } else {
        if (key === "reason") {
          fields.push(lv("reason", caseData.reason?.name, link));
        }

        if (key === "deviceType") {
          fields.push(lv("deviceType", caseData.deviceType!.name, link));
        }

        if (key === "changeCtsType") {
          fields.push(lv("changeCtsType", caseData.changeCtsType?.name, link));
        }

        if (key === "deviceSerialNumbers") {
          fields.push(lv("deviceSerialNumbers", caseData.deviceSerialNumbers!.join(" , "), link));
        }
      }
    });

    return fields;
  }, [caseData]);

  const addressFields = useMemo(() => {
    const link: string | undefined = undefined;

    if (!caseData || !caseData.address) {
      return [];
    }

    const result: Array<{ label: string; value?: string; link?: string }> = [];

    Object.entries(caseData.address).forEach(([key, value]) => {
      if (typeof value === "string") {
        result.push(lv(key, value, link));
      }
    });

    return result;
  }, [caseData?.address]);

  const rentFields = useMemo(() => {
    const link: string | undefined = undefined;

    if (!caseData?.rent) {
      return [];
    }

    const fields = [];
    fields.push(lv("rentNumber", caseData.rent.rentNumber, link));
    fields.push(lv("rentStartDate", caseData.rent.startDate, link));
    fields.push(lv("rentEndDate", caseData.rent.endDate, link));

    return fields;
  }, [caseData?.rent]);

  return (
    <div className={classes.row}>
      {fields?.map((v) => (
        <div key={v.label} className={classes.rowItem}>
          <span>{changeLang(v.label)}</span>
          <p>{v.link ? <Link to={v.link}>{v.value}</Link> : v.value}</p>
        </div>
      ))}
      {addressFields?.map((v) => (
        <div key={v.label} className={classes.rowItem}>
          <span>{changeLang(v.label)} :</span>
          <p>{v.value}</p>
        </div>
      ))}
      {isNeedShowLossFiscalModule && (
        <div className={classes.rowItem}>
          <span>{changeLang("lossFiscalModule")}</span>
          <p>{caseData?.lossFiscalModule ? "да" : "нет"}</p>
        </div>
      )}
      {caseData.bankType && (
        <div className={classes.rowItem}>
          <span>{changeLang("bankType")}</span>
          <p>{caseData.bankType.name}</p>
        </div>
      )}
      {caseData.paymentType && (
        <div className={classes.rowItem}>
          <span>{changeLang("paymentType")}</span>
          <p>{caseData.paymentType.name}</p>
        </div>
      )}
      {rentFields.map((v) => (
        <div key={v.label} className={classes.rowItem}>
          <span>{changeLang(v.label)}</span>
          <p>{v.value}</p>
        </div>
      ))}
    </div>
  );
};
