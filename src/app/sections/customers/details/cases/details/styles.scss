@import "src/styles/variables.scss";

.case-details {
  padding: 0;

  &__row {
    display: flex;
    overflow: hidden;
    flex-grow: 1;
  }

  &__left-col {
    padding: 0 5px 0 10px;
    margin: 15px 0;
    overflow: auto;
    flex-grow: 1;
  }

  .case-details__collapse {
    &-title {
      font-size: 16px;
      font-weight: 500;
    }

    &-icon {
      border: 1px solid #DDE6FA;
      width: 30px;
      height: 30px;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $primary;
      box-shadow: 0px 4px 4px 0px rgba(44, 100, 227, 0.04);

      svg {
        width: 16px;
        height: 16px;
      }
    }

    .ant-collapse-item:not(:last-child) {
      margin-bottom: 20px;
    }

    &.ant-collapse > .ant-collapse-item.ant-collapse-no-arrow > .ant-collapse-header {
      padding: 0;
    }

    .ant-collapse-content-box {
      padding: 0;
    }

    &.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
      padding-bottom: 0;
    }
  }

  &__chat {
    background-color: #fff;
    padding: 15px 10px 10px;
    margin-left: 5px;
    min-width: 440px;
    flex-basis: 30%;
    flex-shrink: 0;
    border-left: 2px solid $primary;
  }
}

.case-documents {
  position: relative;

  &__actions {
    position: absolute;
    top: -42px;
    right: 42px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
}