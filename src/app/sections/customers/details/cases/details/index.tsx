import React, { FC, ReactNode, useEffect } from "react";

import { ArrowDownIcon } from "#assets/icons";
import { $caseDetails } from "#stores/cases";
import { ContentCustom } from "#ui/contentCustom";
import { ContentHeader } from "#ui/contentHeader";
import { StatusCustom } from "#ui/status";
import { Collapse, Row } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import { RouteComponentProps } from "react-router-dom";

import { getCaseStatusColor } from "../../helpers";

import { CaseChangeLogs } from "./components/caseChangeLogs";
import { CaseData } from "./components/caseData";
import { CaseDocuments } from "./components/caseDocuments";
import { CaseStatusHistories } from "./components/caseStatusHistories";
import { CaseTemplates } from "./components/caseTemplates";
import { CaseDetailsChat } from "./components/chat";
import "./styles.scss";

type PropsTypes = {} & RouteComponentProps<{
  id: string;
}>;

export const CaseDetails: FC<PropsTypes> = (props) => {
  const { match } = props;

  const caseId = Number(match.params.id);

  const { t } = useTranslation();

  const { data: caseDetails, loading: caseDetailsLoading, error: caseDetailsError } = useStore($caseDetails.store);

  useEffect(() => {
    return () => {
      $caseDetails.reset();
    };
  }, []);

  useEffect(() => {
    $caseDetails.effect(caseId);
  }, [caseId]);

  const header = (title: ReactNode) => {
    return (
      <Row justify="space-between" align="middle">
        <div className="case-details__collapse-title">{title}</div>
        <div className="case-details__collapse-icon">
          <ArrowDownIcon />
        </div>
      </Row>
    );
  };

  return (
    <ContentCustom className="case-details" loading={caseDetailsLoading} error={caseDetailsError}>
      {caseDetails && (
        <div className="case-details__row">
          <div className="case-details__left-col u-fancy-scrollbar">
            <ContentHeader>
              <ContentHeader.Title title={`${t("case")}: ${caseId}`} goBack={true} />
              <StatusCustom status={caseDetails.status.code} getStatusColor={getCaseStatusColor}>
                {caseDetails.status.name}
              </StatusCustom>
            </ContentHeader>
            <Collapse ghost={true} className="case-details__collapse" expandIcon={ArrowDownIcon} defaultActiveKey="1">
              <Collapse.Panel showArrow={false} key="1" header={header(t("caseDataChanges"))}>
                <CaseData caseDetails={caseDetails} />
              </Collapse.Panel>
              <Collapse.Panel showArrow={false} key="2" header={header(t("caseTemplates"))}>
                <CaseTemplates caseId={caseDetails.id} />
              </Collapse.Panel>
              <Collapse.Panel showArrow={false} key="3" header={header(t("caseDocuments"))}>
                <CaseDocuments />
              </Collapse.Panel>
              <Collapse.Panel showArrow={false} key="4" header={header(t("caseHistory"))}>
                <CaseChangeLogs />
              </Collapse.Panel>
              <Collapse.Panel showArrow={false} key="5" header={header(t("caseActivities"))}>
                <CaseStatusHistories />
              </Collapse.Panel>
            </Collapse>
          </div>
          <CaseDetailsChat />
        </div>
      )}
    </ContentCustom>
  );
};
