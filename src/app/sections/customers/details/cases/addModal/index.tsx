import React, { FC, useEffect, useState } from "react";

import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $caseDetails, $createCase, $updateCase } from "#stores/cases";
import { ButtonCustom } from "#ui/buttonCustom";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification } from "#utils/helpers";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { CASE_TYPES } from "../constants";

import { HumoConnectionChangeForm } from "./humoConnectionChangeForm";

export type AddEditCaseModalType = {
  caseId?: number;
  customerId: number;
};

type PropsTypes = {
  modalControl: ModalControlType<AddEditCaseModalType>;
  callback: () => void;
};

export const AddEditCaseModal: FC<PropsTypes> = (props) => {
  const { modalControl, callback } = props;

  const { caseId, customerId, closeModal } = modalControl;

  const { t } = useTranslation();

  const caseDetailsState = useStore($caseDetails.store);
  const createCaseState = useStore($createCase.store);
  const updateCaseState = useStore($updateCase.store);

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [caseType, setCaseType] = useState("");

  useEffect(() => {
    if (caseId) {
      $caseDetails.effect(caseId);
    } else {
      setCaseType(CASE_TYPES.HUMO_CONNECTION_CHANGE);
    }

    return () => {
      $caseDetails.reset();
      $createCase.reset();
      $updateCase.reset();
    };
  }, []);

  useEffect(() => {
    if (caseDetailsState.data) {
      setCaseType(caseDetailsState.data.caseType.code);
    }
  }, [caseDetailsState.data]);

  useEffect(() => {
    if (createCaseState.data || updateCaseState.data) {
      const successMessage = createCaseState.data
        ? t("caseAdded", { ns: namespaces.notifications })
        : t("caseUpdated", { ns: namespaces.notifications });

      openNotification("success", successMessage);
      closeModal();
      callback();
    }
  }, [createCaseState.data, updateCaseState.data]);

  return (
    <>
      <ModalCustom.Loading show={createCaseState.loading || updateCaseState.loading || caseDetailsState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>
          {caseId ? t("editCase", { ns: namespaces.buttons }) : t("addCase", { ns: namespaces.buttons })}
        </ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        {caseType === CASE_TYPES.HUMO_CONNECTION_CHANGE && (
          <HumoConnectionChangeForm
            isSubmitted={isSubmitted}
            setIsSubmitted={setIsSubmitted}
            caseId={caseId}
            customerId={customerId}
          />
        )}
      </ModalCustom.Middle>
      <ModalCustom.Footer>
        <ButtonCustom
          type="primary"
          size="small"
          onClick={() => {
            setIsSubmitted(true);
          }}
        >
          {t("add", { ns: namespaces.buttons })}
        </ButtonCustom>
      </ModalCustom.Footer>
    </>
  );
};
