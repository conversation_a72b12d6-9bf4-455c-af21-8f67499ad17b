import React, { FC, useEffect } from "react";

import { TIdCodeNameModel } from "#businessLogic/models/common";
import { ICustomerDeviceLookupItemModel } from "#businessLogic/models/customerDevices";
import { requiredRules } from "#constants/index";
import { BankCardTypesSelect } from "#pickers/bankCardTypeSelect";
import { BankSelect } from "#pickers/bankSelect";
import { BankTypeSelect } from "#pickers/bankTypeSelect";
import { CustomerDeviceSelect } from "#pickers/customerDeviceSelect";
import { $caseDetails, $createCase, $updateCase } from "#stores/cases";
import { SelectOptionType } from "#types/common";
import { FormCustom } from "#ui/formCustom";
import { InputCustom } from "#ui/inputCustom";
import { getDigitsNums } from "#utils/helpers";
import { Col, Form, Row } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { CASE_TYPES } from "../../constants";

type PropsTypes = {
  isSubmitted: boolean;
  setIsSubmitted: React.Dispatch<React.SetStateAction<boolean>>;
  caseId?: number;
  customerId: number;
};

type TFormFields = {
  deviceId: number;
  deviceSerialNumber: string;
  customerBranchName: string;
  customerBranchId: number;
  customerBranchDeviceId: number;
  bankId: number;
  bankName: string;
  mfo: string;
  bankType: string;
  accountNumber: string;
  oked: string;
  phone: string;
  paymentType: string;
  secretKey: string;
  deviceType: string;
  merchantId: string;
  terminalId: string;
};

export const HumoConnectionChangeForm: FC<PropsTypes> = (props) => {
  const { isSubmitted, setIsSubmitted, caseId, customerId } = props;

  const { t } = useTranslation();

  const caseDetailsState = useStore($caseDetails.store);

  const [form] = Form.useForm<TFormFields>();

  useEffect(() => {
    if (caseDetailsState.data) {
      const caseDataFields = caseDetailsState.data.data;

      form.setFieldsValue({
        deviceId: caseDataFields.deviceId,
        deviceSerialNumber: caseDataFields.deviceSerialNumber,
        customerBranchName: caseDataFields.branchName,
        customerBranchId: caseDataFields.customerBranchId,
        customerBranchDeviceId: caseDataFields.customerBranchDeviceId,
        bankId: caseDataFields.bankId,
        bankName: caseDataFields.bankName,
        mfo: caseDataFields.mfo,
        bankType: caseDataFields.bankType?.code,
        accountNumber: caseDataFields.accountNumber,
        oked: caseDataFields.oked,
        phone: caseDataFields.phone,
        paymentType: caseDataFields.paymentType?.code,
        deviceType: caseDataFields.deviceType?.code,
        merchantId: caseDataFields.merchantId,
        terminalId: caseDataFields.terminalId,
      });
    }
  }, [caseDetailsState.data]);

  useEffect(() => {
    if (isSubmitted) {
      form.submit();
      setIsSubmitted(false);
    }
  }, [isSubmitted]);

  const onDeviceChange = (deviceId: number, option: SelectOptionType<ICustomerDeviceLookupItemModel>) => {
    const deviceItem = option ? option["data-option"] : undefined;

    form.setFieldsValue({
      deviceId,
      deviceSerialNumber: deviceItem ? deviceItem.device.serialNumber : undefined,
      customerBranchId: deviceItem ? deviceItem.customerBranch?.id : undefined,
      customerBranchName: deviceItem ? deviceItem.customerBranch?.name : undefined,
      customerBranchDeviceId: deviceItem ? deviceItem.customerBranchDeviceId : undefined,
      deviceType: deviceItem ? deviceItem.deviceType?.code : undefined,
    });
  };

  const onBankChange = (bankId: number | undefined, option: SelectOptionType<TIdCodeNameModel>) => {
    const bankName = option ? option["data-option"]?.name : undefined;
    const mfo = option ? option["data-option"]?.code : undefined;

    form.setFieldsValue({
      bankId,
      bankName,
      mfo,
    });
  };

  const onFinish = () => {
    const formFields: TFormFields = form.getFieldsValue(true);
    const checkAccount = formFields.accountNumber.length <= 20;

    const data = {
      customerId,
      type: CASE_TYPES.HUMO_CONNECTION_CHANGE,
      data: {
        customerId,
        checkAccount,
        deviceId: formFields.deviceId,
        deviceSerialNumber: formFields.deviceSerialNumber,
        branchName: formFields.customerBranchName,
        customerBranchId: formFields.customerBranchId,
        customerBranchDeviceId: formFields.customerBranchDeviceId,
        bankId: formFields.bankId,
        bankName: formFields.bankName,
        mfo: formFields.mfo,
        bankType: formFields.bankType,
        accountNumber: formFields.accountNumber,
        oked: formFields.oked,
        phone: getDigitsNums(formFields.phone),
        paymentType: formFields.paymentType,
        deviceType: formFields.deviceType,
        merchantId: formFields.merchantId,
        terminalId: formFields.terminalId,
      },
    };

    if (caseId) {
      $updateCase.effect({
        id: caseId,
        ...data,
      });
    } else {
      $createCase.effect(data);
    }
  };

  return (
    <FormCustom
      form={form}
      onFinish={onFinish}
      initialValues={{
        accountNumber: "",
        oked: "",
        phone: "",
      }}
      phantomSubmit
    >
      <Form.Item label={t("placeholders.selectBank")} name="bankType" initialValue="INFINBANK" rules={requiredRules}>
        <BankTypeSelect disabled={!caseId} />
      </Form.Item>
      <Form.Item label={t("placeholders.selectPaymentSystem")} name="paymentType" rules={requiredRules}>
        <BankCardTypesSelect />
      </Form.Item>
      <Form.Item label={t("kkm")} name="deviceId" rules={requiredRules}>
        <CustomerDeviceSelect
          onChange={onDeviceChange}
          dependencies={[customerId]}
          requestParams={{ type: "PHYSICAL", customerId }}
          optionDetails={true}
        />
      </Form.Item>
      <Form.Item label={t("tt")} name="customerBranchName" rules={requiredRules}>
        <InputCustom disabled={true} />
      </Form.Item>
      <Form.Item label={t("placeholders.selectClientBank")} name="bankId" rules={requiredRules}>
        <BankSelect optionDetails={true} onChange={onBankChange} placeholder={t("placeholders.selectClientBank")} />
      </Form.Item>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t("accountNumber")}
            name="accountNumber"
            rules={[
              {
                validateTrigger: "onSubmit",
                required: true,
                min: 20,
              },
            ]}
          >
            <InputCustom.Mask
              placeholder="XXXXX XXX X XXXXXXXX XXX"
              mask="99999999999999999999999999999999999"
              maskChar=""
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={t("oked")} name="oked">
            <InputCustom.Mask placeholder="XXXXX" mask="99999" maskChar="" />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item label={t("phoneNum")} name="phone" rules={requiredRules}>
        <InputCustom.Mask mask="+\9\98 (99) 999 99 99" maskChar=" " placeholder={t("placeholders.inputPhoneNum")} />
      </Form.Item>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="TID" name="terminalId" rules={requiredRules}>
            <InputCustom />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="MID" name="merchantId" rules={requiredRules}>
            <InputCustom />
          </Form.Item>
        </Col>
      </Row>
    </FormCustom>
  );
};
