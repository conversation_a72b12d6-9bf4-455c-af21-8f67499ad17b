import React, { FC, useMemo } from "react";

import apay from "#assets/images/apay.png";
import click from "#assets/images/click.png";
import humo from "#assets/images/humo-logo.svg";
import payme from "#assets/images/payme.png";
import uzcard from "#assets/images/uzcard.png";
import { ICustomerDevicesListItemModel } from "#businessLogic/models/customerDevices";
import { StatusCustom } from "#ui/status";
import { TableCustom } from "#ui/tableCustom";
import { Row } from "antd";
import { ColumnsType } from "antd/lib/table/interface";

import classes from "./styles.module.scss";

type PropsTypes = {
  row: ICustomerDevicesListItemModel;
};

const getImgType = (val: string) => {
  if (val.includes("UZ_CARD")) return uzcard;
  else if (val.includes("HUMO")) return humo;
  else if (val === "CLICK") return click;
  else if (val === "PAY_ME") return payme;
  else return apay;
};

export const ExpandCustomerDeviceDetails: FC<PropsTypes> = (props) => {
  const { row } = props;

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<ICustomerDevicesListItemModel> = [
      {
        title: "Документы",
        dataIndex: "ofdFileUrl",
        render: (_, row) => (
          <div>
            {row.ofdFile && (
              <div className="application-file-wrap">
                <a href={row.ofdFile.url} target="_blank" rel="noreferrer">
                  Документ НИЦ
                </a>
              </div>
            )}
            {row.applicationFile && (
              <div className="application-file-wrap">
                <a href={row.applicationFile.url} target="_blank" download rel="noreferrer">
                  Заявление (Ариза)
                </a>
              </div>
            )}
          </div>
        ),
      },
      // {
      //   title: "Данные по продаже",
      //   dataIndex: "paymentData",
      //   render: (_, row) => (
      //     <>
      //       <div>
      //         Чек A-pay:&nbsp;
      //         {bill ? (
      //           <a href={bill.fiscalCheckUrl} target="_blank" rel="noreferrer">
      //             <FontAwesomeIcon icon={faFileCircleCheck} />
      //           </a>
      //         ) : (
      //           "-"
      //         )}
      //       </div>
      //     </>
      //   ),
      // },
      {
        title: "Данные OFD",
        dataIndex: "ofdApplication",
        render: (_, row) => (
          <>
            <div>Тип заявления: {row.ofdApplication ? row.ofdApplication.type.name : "-"}</div>
            <Row align="middle">
              Статус заявления:&nbsp;
              {row.ofdApplication ? (
                <StatusCustom status={row.ofdApplication.status.code}>{row.ofdApplication.status.name}</StatusCustom>
              ) : (
                "-"
              )}
            </Row>
          </>
        ),
      },
    ];

    return columns;
  }, []);

  return (
    <div className={classes.expandDetails}>
      <>
        {row.paymentStatuses.length > 0 && (
          <div className={classes.paymentIcons}>
            {row.paymentStatuses
              .filter((item) => item.code !== "NOT_CONNECTED")
              .map((item) => (
                <img key={item.code} src={getImgType(item.code)} alt={item.code} />
              ))}
          </div>
        )}
      </>
      <TableCustom rowKey="id" dataSource={[row]} columns={tableColumns} pagination={false} />
    </div>
  );
};
