.expandDetails {
  padding: 16px 14px;

  .ant-table {
    background: transparent;
  }

  .ant-table-wrapper {
    .ant-table-thead > tr > th {
      padding: 16px;
    }

    .ant-table-tbody > tr > td {
      padding: 11px 10px;
    }
  }

  .ant-table-thead > tr > th {
    background: #fff;
  }
}

.paymentIcons {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  img {
    margin: 0 10px 10px 0;
    width: 49px;
    height: 25px;
    object-fit: contain;
  }
}
