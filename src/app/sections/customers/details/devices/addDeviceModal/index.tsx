import React, { FC, useEffect } from "react";

import { requiredRules } from "#constants/index";
import { ModalControlType } from "#hooks/useModalControl";
import { i18n } from "#localization/i18n";
import { namespaces } from "#localization/i18n.constants";
import { BranchDeviceSelect } from "#pickers/branchDeviceSelect";
import { CustomerBranchSelect } from "#pickers/customerBranchSelect";
import { DeviceModelSelect } from "#pickers/modelSelect";
import { $customerBranchDevices } from "#stores/branchDevices";
import { $createCustomerDevice } from "#stores/customerDevices";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { SelectCustom } from "#ui/selectCustom";
import { SpinCustom } from "#ui/spinCustom";
import { openNotification } from "#utils/helpers";
import { Alert, Form } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export type AddCustomerDeviceModalType = {
  customerId: number;
};

type PropsTypes = {
  modalControl: ModalControlType<AddCustomerDeviceModalType>;
  callback: () => void;
};

type TFormFields = {
  deviceType: string;
  customerBranchId: number;
  deviceId: number;
};

const deviceTypes = [
  { code: "TERMINAL", name: i18n.t("deviceTypeTerminal") },
  { code: "FISCAL", name: i18n.t("deviceTypeFiscal") },
];

export const AddCustomerDeviceModal: FC<PropsTypes> = (props) => {
  const { modalControl, callback } = props;

  const { customerId, closeModal } = modalControl;

  const { t } = useTranslation();

  const createCustomerDeviceState = useStore($createCustomerDevice.store);
  const customerBranchDevicesState = useStore($customerBranchDevices.store);

  const [form] = Form.useForm<TFormFields>();

  useEffect(() => {
    return () => {
      $createCustomerDevice.reset();
      $customerBranchDevices.reset();
    };
  }, []);

  useEffect(() => {
    if (createCustomerDeviceState.success) {
      openNotification("success", t("kkmAdded", { ns: namespaces.notifications }));
      closeModal();
      callback();
    }
  }, [createCustomerDeviceState.success]);

  const onModelChange = (modelId: number) => {
    const customerBranchId = form.getFieldValue("customerBranchId");

    $customerBranchDevices.effect({
      customerId,
      customerBranchId,
      modelId,
    });

    form.setFieldValue("serialNumber", undefined);
  };

  const onFinish = () => {
    const formFields: TFormFields = form.getFieldsValue(true);

    const data = {
      customerId,
      appType: "SMARTPOS",
      devicePaymentType: "REREGISTRATION",
      deviceType: formFields.deviceType,
      customerBranchId: formFields.customerBranchId,
      devices: [formFields.deviceId],
    };

    $createCustomerDevice.effect(data);
  };

  return (
    <>
      <ModalCustom.Loading show={createCustomerDeviceState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>{t("registerKkm", { ns: namespaces.customers })}</ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        <FormCustom form={form} onFinish={onFinish} phantomSubmit>
          <Form.Item label={t("deviceType")} name="deviceType" rules={requiredRules}>
            <SelectCustom>
              {deviceTypes.map((item) => (
                <SelectCustom.Option value={item.code}>{item.name}</SelectCustom.Option>
              ))}
            </SelectCustom>
          </Form.Item>
          <Form.Item noStyle shouldUpdate>
            {() => {
              const deviceType = form.getFieldValue("deviceType");

              return (
                <Form.Item label={t("tt")} name="customerBranchId" rules={requiredRules}>
                  <CustomerBranchSelect
                    placeholder=""
                    dependencies={[customerId, deviceType]}
                    requestParams={{ customerId, type: deviceType }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          {customerBranchDevicesState.loading ? (
            <div className="m-b-10">
              <SpinCustom size="small" />
            </div>
          ) : (
            customerBranchDevicesState.data.length > 0 && (
              <div className="m-b-10">
                <Alert
                  message={customerBranchDevicesState.data.map((item) => item.serialNumber).join(", ")}
                  type="warning"
                />
              </div>
            )
          )}
          <Form.Item label={t("terminalModel")} name="modelId" rules={requiredRules}>
            <DeviceModelSelect placeholder="" onChange={onModelChange} />
          </Form.Item>
          <Form.Item noStyle shouldUpdate>
            {() => {
              const modelId = form.getFieldValue("modelId");

              return (
                <Form.Item label={t("serialNumber")} name="deviceId" rules={requiredRules}>
                  <BranchDeviceSelect
                    placeholder=""
                    optionValue="id"
                    requestParams={{
                      modelIds: [modelId],
                    }}
                    dependencies={[modelId]}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        </FormCustom>
      </ModalCustom.Middle>
      <ModalCustom.Footer>
        <ButtonCustom type="primary" size="small" onClick={() => form.submit()}>
          {t("add", { ns: namespaces.buttons })}
        </ButtonCustom>
      </ModalCustom.Footer>
    </>
  );
};
