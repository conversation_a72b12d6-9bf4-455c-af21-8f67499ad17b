import React, { FC, useEffect } from "react";

import { Price } from "#components/price";
import { requiredRules } from "#constants/index";
import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { CustomerBranchSelect } from "#pickers/customerBranchSelect";
import { FiscalModuleSelect } from "#pickers/fiscalModuleSelect";
import { $createCustomerFiscalModule, $customerBalance } from "#stores/customerFiscalModules";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification } from "#utils/helpers";
import { Form } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import "./styles.scss";

export type AddFiscalModuleModalType = {
  customerId: number;
  tin: string;
};

type PropsTypes = {
  modalControl: ModalControlType<AddFiscalModuleModalType>;
  callback: () => void;
};

type TFormFields = {
  customerBranchId: number;
  serialNumber: string;
};

export const AddFiscalModuleModal: FC<PropsTypes> = (props) => {
  const { modalControl } = props;

  const { customerId, tin, closeModal } = modalControl;

  const { t } = useTranslation();

  const createCustomerFiscalModuleState = useStore($createCustomerFiscalModule.store);
  const customerBalanceState = useStore($customerBalance.store);

  const [form] = Form.useForm<TFormFields>();

  useEffect(() => {
    $customerBalance.effect(tin);

    return () => {
      $createCustomerFiscalModule.reset();
    };
  }, []);

  useEffect(() => {
    if (createCustomerFiscalModuleState.success) {
      openNotification("success", t("fmRegistered", { ns: namespaces.customers }));
      closeModal();
    }
  }, [createCustomerFiscalModuleState.success]);

  const onFinish = () => {
    const formFields: TFormFields = form.getFieldsValue(true);

    const data = {
      customerId,
      customerBranchId: formFields.customerBranchId,
      serialNumber: formFields.serialNumber,
      fromRegistry: false,
    };

    $createCustomerFiscalModule.effect(data);
  };

  return (
    <>
      <ModalCustom.Loading show={createCustomerFiscalModuleState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>{t("newFm")}</ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        {customerBalanceState.data && (
          <div className="fiscal-module-form-balance">
            <div className="fiscal-module-form-balance__row">
              <div className="fiscal-module-form-balance__label">{t("balance")}:</div>
              <strong>
                <Price value={customerBalanceState.data.data.overpaymentSum} />
              </strong>
            </div>
            <div className="fiscal-module-form-balance__row">
              <div className="fiscal-module-form-balance__label">
                {t("terminalCost", { ns: namespaces.customers })}:
              </div>
              <Price value={customerBalanceState.data.data.terminalCost} />
            </div>
          </div>
        )}

        <FormCustom form={form} onFinish={onFinish} phantomSubmit>
          <Form.Item label={t("tt")} name="customerBranchId" rules={requiredRules}>
            <CustomerBranchSelect placeholder="" requestParams={{ customerId }} dependencies={[customerId]} />
          </Form.Item>

          <Form.Item label={t("fm")} name="serialNumber" rules={requiredRules}>
            <FiscalModuleSelect
              placeholder=""
              optionValue="serialNumber"
              requestParams={{
                customerId,
                fromRegistry: false,
              }}
            />
          </Form.Item>
        </FormCustom>
      </ModalCustom.Middle>
      <ModalCustom.Footer>
        <ButtonCustom type="primary" size="small" onClick={() => form.submit()}>
          {t("add", { ns: namespaces.buttons })}
        </ButtonCustom>
      </ModalCustom.Footer>
    </>
  );
};
