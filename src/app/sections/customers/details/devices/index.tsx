import React, { FC, useEffect, useMemo, useState } from "react";

import { ICustomerDevicesListItemModel, TCustomerDevicesListParams } from "#businessLogic/models/customerDevices";
import { TUploadFileModalControlType, UploadFileModal } from "#components/uploadFileModal";
import { DOCUMENT_TYPES, ENTITY_TYPES } from "#constants/index";
import { useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $customerBranchDevicesSyncSmartpos, $downloadKKMApplicationFile } from "#stores/customerBranchDevice";
import { $customerDevicesList } from "#stores/customerDevices";
import { $uploadEntityDocument } from "#stores/fileUpload";
import { $confirmCustomer } from "#stores/ofd";
import { $syncOfdApplications } from "#stores/ofdApplications";
import { ButtonCustom } from "#ui/buttonCustom";
import { DrawerCustom } from "#ui/drawerCustom";
import { PopoverCustom } from "#ui/popoverCustom";
import { StatusCustom } from "#ui/status";
import { TableCustom, TableNumber } from "#ui/tableCustom";
import { formatDate, openNotification } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { AddCustomerDeviceModal, AddCustomerDeviceModalType } from "./addDeviceModal";
import { AddFiscalModuleModal, AddFiscalModuleModalType } from "./addFiscalModule";
import { ExpandCustomerDeviceDetails } from "./expandDetails";
import { FiscalizationModal, FiscalizationModalType } from "./fiscalizationModal";

type PropsTypes = {
  customerId: number;
  tin: string;
};

type TUploadFileModalControl = TUploadFileModalControlType<{
  entityId: number;
  entityType: string;
  documentType: string;
}>;

export const CustomerDevices: FC<PropsTypes> = (props) => {
  const { customerId, tin } = props;

  const { t } = useTranslation();

  const customerDevicesListState = useStore($customerDevicesList.store);
  const downloadKKMApplicationFileState = useStore($downloadKKMApplicationFile.store);
  const customerBranchDevicesSyncSmartposState = useStore($customerBranchDevicesSyncSmartpos.store);
  const confirmCustomerState = useStore($confirmCustomer.store);
  const syncOfdApplicationsState = useStore($syncOfdApplications.store);

  const [queryParams, setQueryParams] = useState<TCustomerDevicesListParams>({
    customerId,
  });

  const createCustomerDeviceModalControl = useModalControl<AddCustomerDeviceModalType>();
  const createFiscalModuleModalControl = useModalControl<AddFiscalModuleModalType>();
  const fiscalizationModalControl = useModalControl<FiscalizationModalType>();
  const uploadFileModalControl = useModalControl<TUploadFileModalControl>();

  const customerDevicesListData = customerDevicesListState.data;

  const getList = () => {
    $customerDevicesList.effect(queryParams);
  };

  useEffect(() => {
    return () => {
      $customerDevicesList.reset();
    };
  }, []);

  useEffect(() => {
    getList();
  }, [queryParams]);

  useEffect(() => {
    if (customerBranchDevicesSyncSmartposState.success) {
      openNotification("success", t("successRegister", { ns: namespaces.customers }));
      $customerBranchDevicesSyncSmartpos.reset();
    }
  }, [customerBranchDevicesSyncSmartposState.success]);

  useEffect(() => {
    if (confirmCustomerState.success) {
      openNotification("success", t("confirmed", { ns: namespaces.customers }));
      $confirmCustomer.reset();
    }
  }, [confirmCustomerState.success]);

  useEffect(() => {
    if (syncOfdApplicationsState.success) {
      getList();
      $syncOfdApplications.reset();
    }
  }, [syncOfdApplicationsState.success]);

  const onFilterChange = (params: TCustomerDevicesListParams) => {
    setQueryParams(params);
  };

  const onPaginationChange = (page: number) => {
    onFilterChange({ page: page - 1 });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<ICustomerDevicesListItemModel> = [
      {
        title: "#",
        dataIndex: "num",
        width: 30,
        render: (_, row, index) => (
          <TableNumber value={customerDevicesListData.size * customerDevicesListData.number + index + 1} />
        ),
      },
      {
        title: t("kkm"),
        dataIndex: "device",
        render: (_, row) => row.device?.serialNumber || "-",
      },
      {
        title: t("fm"),
        dataIndex: "fiscalModule",
        render: (_, row) => row.fiscalModule?.serialNumber || "-",
      },
      {
        title: t("tt"),
        dataIndex: "customerBranch",
        render: (_, row) => row.customerBranch?.name || "-",
      },
      {
        title: t("paymentType"),
        dataIndex: "paymentType",
        render: (_, row) => row.paymentType?.name || "-",
      },
      {
        title: t("deviceType"),
        dataIndex: "deviceType",
        render: (_, row) => row.deviceType?.name || "-",
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) =>
          row.deviceStatus ? <StatusCustom status={row.deviceStatus.code}>{row.deviceStatus.name}</StatusCustom> : "-",
      },
      {
        title: t("date"),
        dataIndex: "createdDate",
        render: (_, row) => <div className="w-s-n">{formatDate(row.createdDate)}</div>,
      },
      {
        title: "",
        dataIndex: "actions",
        width: 40,
        render: (_, row) => (
          <PopoverCustom
            placement="bottomRight"
            trigger="click"
            content={
              <>
                <PopoverCustom.Item>
                  <ButtonCustom
                    type="default"
                    loading={downloadKKMApplicationFileState.loading}
                    disabled={!row.customerBranch || !row.fiscalModule}
                    onClick={() =>
                      $downloadKKMApplicationFile.effect({
                        customerBranchId: row.customerBranch?.id,
                        deviceSerial: row.device.serialNumber,
                        fiscalModuleSerial: row.fiscalModule?.serialNumber,
                      })
                    }
                  >
                    {t("downloadApplication", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </PopoverCustom.Item>
                {row.fiscalizationId && (
                  <PopoverCustom.Item>
                    <ButtonCustom
                      type="default"
                      onClick={() =>
                        uploadFileModalControl.openModal({
                          params: {
                            entityId: row.fiscalizationId!,
                            entityType: ENTITY_TYPES.FISCALIZATION,
                            documentType: DOCUMENT_TYPES.APPLICATION,
                          },
                        })
                      }
                    >
                      {t("uploadApplication", { ns: namespaces.buttons })}
                    </ButtonCustom>
                  </PopoverCustom.Item>
                )}
                <PopoverCustom.Item>
                  <ButtonCustom
                    type="default"
                    // disabled={!row.sync}
                    disabled={!row.customerBranch || !row.fiscalModule}
                    loading={confirmCustomerState.loading}
                    onClick={() => {
                      if (row.customerBranch && row.customerBranch.id) {
                        const data = {
                          customerId,
                          customerBranchId: row.customerBranch.id,
                          deviceSerial: row.device.serialNumber,
                          fiscalModuleSerial: row.fiscalModule?.serialNumber,
                        };

                        $confirmCustomer.effect(data);
                      }
                    }}
                  >
                    {t("confirm", { ns: namespaces.customers })}
                  </ButtonCustom>
                </PopoverCustom.Item>
                <PopoverCustom.Item>
                  <ButtonCustom
                    type="default"
                    loading={customerBranchDevicesSyncSmartposState.loading}
                    onClick={() => $customerBranchDevicesSyncSmartpos.effect(row.customerBranchDeviceId)}
                  >
                    {t("registerSmartpos", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </PopoverCustom.Item>
              </>
            }
          />
        ),
      },
    ];

    return columns;
  }, [
    customerDevicesListData.size,
    customerDevicesListData.number,
    downloadKKMApplicationFileState.loading,
    customerBranchDevicesSyncSmartposState.loading,
    confirmCustomerState.loading,
  ]);

  return (
    <div className="customer-details__entity">
      <div className="customer-details__entity-head">
        <div className="customer-details__table-title">{t("kkm")}</div>
        <div className="customer-details__entity-head__actions">
          <ButtonCustom
            size="small"
            onClick={() => $syncOfdApplications.effect(customerId)}
            loading={syncOfdApplicationsState.loading}
          >
            {t("ofdSync", { ns: namespaces.buttons })}
          </ButtonCustom>
          <ButtonCustom
            size="small"
            onClick={() => {
              fiscalizationModalControl.openModal({
                customerId,
              });
            }}
          >
            {t("fiscalization")}
          </ButtonCustom>
          <ButtonCustom
            size="small"
            onClick={() => {
              createFiscalModuleModalControl.openModal({
                customerId,
                tin,
              });
            }}
          >
            {t("addFm", { ns: namespaces.buttons })}
          </ButtonCustom>
          <ButtonCustom
            size="small"
            onClick={() => {
              createCustomerDeviceModalControl.openModal({
                customerId,
              });
            }}
          >
            {t("addKkm", { ns: namespaces.buttons })}
          </ButtonCustom>
        </div>
      </div>
      <TableCustom
        rowKey="id"
        loading={customerDevicesListState.loading}
        dataSource={customerDevicesListData.content}
        columns={tableColumns}
        pagination={{
          total: customerDevicesListData.totalElements,
          pageSize: customerDevicesListData.size,
          current: queryParams.page ? queryParams.page + 1 : 1,
          onChange: onPaginationChange,
        }}
        expandable={{
          expandedRowRender: (row) => <ExpandCustomerDeviceDetails row={row} />,
        }}
      />
      <DrawerCustom
        open={createCustomerDeviceModalControl.visible}
        onCancel={createCustomerDeviceModalControl.closeModal}
        width={350}
      >
        <AddCustomerDeviceModal modalControl={createCustomerDeviceModalControl} callback={getList} />
      </DrawerCustom>
      <DrawerCustom
        open={createFiscalModuleModalControl.visible}
        onCancel={createFiscalModuleModalControl.closeModal}
        width={350}
      >
        <AddFiscalModuleModal modalControl={createFiscalModuleModalControl} callback={getList} />
      </DrawerCustom>
      <DrawerCustom
        open={fiscalizationModalControl.visible}
        onCancel={fiscalizationModalControl.closeModal}
        width={350}
      >
        <FiscalizationModal modalControl={fiscalizationModalControl} />
      </DrawerCustom>
      <DrawerCustom open={uploadFileModalControl.visible} onCancel={uploadFileModalControl.closeModal}>
        <UploadFileModal
          modalControl={uploadFileModalControl}
          uploadAction={(data) => {
            $uploadEntityDocument.effect({
              entityId: data.params.entityId,
              entityType: data.params.entityType,
              documentType: data.params.documentType,
              file: data.file,
            });
          }}
          callback={getList}
          uploadFileStoreController={$uploadEntityDocument}
          type="pdf"
        />
      </DrawerCustom>
    </div>
  );
};
