import React, { FC, useEffect } from "react";

import { requiredRules } from "#constants/index";
import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { CustomerBranchDeviceSelect } from "#pickers/customerBranchDeviceItems";
import { CustomerBranchSelect } from "#pickers/customerBranchSelect";
import { CustomerFiscalModuleSelect } from "#pickers/customerFiscalModuleSelect";
import { DeviceModelSelect } from "#pickers/modelSelect";
import { $fiscalization } from "#stores/customerFiscalModules";
import { ButtonCustom } from "#ui/buttonCustom";
import { FormCustom } from "#ui/formCustom";
import { ModalCustom } from "#ui/modalCustom";
import { openNotification } from "#utils/helpers";
import { Form } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

export type FiscalizationModalType = {
  customerId: number;
};

type PropsTypes = {
  modalControl: ModalControlType<FiscalizationModalType>;
};

type TFormFields = {
  customerBranchId: number;
  deviceModelId: number;
  deviceSerial: string;
  fiscalModuleSerial: string;
};

export const FiscalizationModal: FC<PropsTypes> = (props) => {
  const { modalControl } = props;

  const { customerId, closeModal } = modalControl;

  const { t } = useTranslation();

  const fiscalizationState = useStore($fiscalization.store);

  const [form] = Form.useForm<TFormFields>();

  const modelId = Form.useWatch("deviceModelId", form);
  const customerBranchId = Form.useWatch("customerBranchId", form);

  useEffect(() => {
    return () => {
      $fiscalization.reset();
    };
  }, []);

  useEffect(() => {
    if (fiscalizationState.success) {
      openNotification("success", t("successFiscalization", { ns: namespaces.customers }));
      closeModal();
    }
  }, [fiscalizationState.success]);

  const onCustomerBranchChange = () => {
    form.setFieldsValue({
      deviceSerial: undefined,
      fiscalModuleSerial: undefined,
    });
  };

  const onModelChange = () => {
    form.setFieldValue("deviceSerial", undefined);
  };

  const onFinish = () => {
    const formFields: TFormFields = form.getFieldsValue(true);

    const data = {
      customerId,
      customerBranchId: formFields.customerBranchId,
      deviceModelId: formFields.deviceModelId,
      deviceSerial: formFields.deviceSerial,
      fiscalModuleSerial: formFields.fiscalModuleSerial,
    };

    $fiscalization.effect(data);
  };

  return (
    <>
      <ModalCustom.Loading show={fiscalizationState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>{t("fiscalization")}</ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        <FormCustom form={form} onFinish={onFinish} phantomSubmit>
          <Form.Item label={t("tt")} name="customerBranchId" rules={requiredRules}>
            <CustomerBranchSelect
              placeholder=""
              requestParams={{ customerId }}
              dependencies={[customerId]}
              onChange={onCustomerBranchChange}
            />
          </Form.Item>
          <Form.Item label={t("model")} name="deviceModelId" rules={requiredRules}>
            <DeviceModelSelect placeholder="" onChange={onModelChange} />
          </Form.Item>

          <Form.Item label={t("kkm")} name="deviceSerial" rules={requiredRules}>
            <CustomerBranchDeviceSelect
              optionValue="serialNumber"
              dependencies={[modelId, customerBranchId]}
              requestParams={{
                customerId,
                customerBranchId,
                modelId,
              }}
            />
          </Form.Item>

          <Form.Item label={t("fm")} name="fiscalModuleSerial" rules={requiredRules}>
            <CustomerFiscalModuleSelect
              optionValue="serialNumber"
              dependencies={[customerBranchId]}
              requestParams={{
                customerId,
                customerBranchId,
              }}
            />
          </Form.Item>
        </FormCustom>
      </ModalCustom.Middle>
      <ModalCustom.Footer>
        <ButtonCustom type="primary" size="small" onClick={() => form.submit()}>
          {t("save", { ns: namespaces.buttons })}
        </ButtonCustom>
      </ModalCustom.Footer>
    </>
  );
};
