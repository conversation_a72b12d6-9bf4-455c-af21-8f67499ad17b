import React, { FC, useEffect, useRef, useState } from "react";

import { EnumReferenceTypes } from "#businessLogic/models/public/referencesTypes";
import { CoordsMap, CoordsMapModalTypes, TMapAddress, TMapData } from "#components/coordsMapModal";
import { requiredRules } from "#constants/index";
import { ModalControlType, useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { ActivityTypeChildSelect, ActivityTypeParentSelect } from "#pickers/activityTypeSelect";
import { CustomerBranchTypeSelect } from "#pickers/customerBranchTypeSelect";
import { DistrictSelect } from "#pickers/districtSelect";
import { NeighborhoodSelect } from "#pickers/neighborhoodSelect";
import { RegionSelect } from "#pickers/regionSelect";
import { $checkedCustomerBranchAddress, $createCustomerBranch } from "#stores/customerBranches";
import { $referenceItemsTypes } from "#stores/public";
import { SelectOptionType } from "#types/common";
import { ButtonCustom } from "#ui/buttonCustom";
import { DatePickerCustom } from "#ui/datePickerCustom";
import { FormCustom } from "#ui/formCustom";
import { InputCustom } from "#ui/inputCustom";
import { ModalCustom } from "#ui/modalCustom";
import { getDigitsNums, getPhoneTypeIds, openNotification, withDebounce } from "#utils/helpers";
import { Col, Form, Input, InputNumber, Radio, Row } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import InputMask from "react-input-mask";

export type AddCustomerBranchModalType = {
  customerId: number;
  tin: string;
};

type PropsTypes = {
  modalControl: ModalControlType<AddCustomerBranchModalType>;
  callback: () => void;
};

type TCheckAddressRentFields = { rentNumber: string; rentPeriod: Array<string> };
type TCheckAddressOwnFields = { cadastreNumber: string; cadastreNumberAdditional: string | undefined };

export const AddCustomerBranchModal: FC<PropsTypes> = (props) => {
  const { modalControl, callback } = props;

  const { customerId, tin, closeModal } = modalControl;

  const { t } = useTranslation();

  const createCustomerBranchState = useStore($createCustomerBranch.store);
  const referenceTypesState = useStore($referenceItemsTypes.store);
  const checkedCustomerBranchAddressState = useStore($checkedCustomerBranchAddress.store);

  const phoneTypes = referenceTypesState[EnumReferenceTypes.PHONE_TYPE];

  const [mapData, setMapData] = useState<TMapData>(null);
  const [branchAddress, setBranchAddress] = useState<TMapAddress>({});

  const [form] = Form.useForm();
  const propertyType = Form.useWatch("propertyType", form);

  const inputCadastreNumberAdditional = useRef<any>(null);

  const [showFiscalTypeFields, setShowFiscalTypeFields] = useState<boolean>(false);

  const coordsMapModalControl = useModalControl<CoordsMapModalTypes>();

  useEffect(() => {
    if (!phoneTypes) {
      $referenceItemsTypes.effect({
        type: EnumReferenceTypes.PHONE_TYPE,
      });
    }

    return () => {
      $createCustomerBranch.reset();
      $checkedCustomerBranchAddress.reset();
    };
  }, []);

  useEffect(() => {
    if (createCustomerBranchState.success) {
      openNotification("success", t("ttAdded", { ns: namespaces.notifications }));
      closeModal();
      callback();
    }
  }, [createCustomerBranchState.success]);

  useEffect(() => {
    if (checkedCustomerBranchAddressState.data) {
      const address = checkedCustomerBranchAddressState.data;

      const formData = {
        latitude: address.latitude,
        longitude: address.longitude,
        apartment: address.apartment,
        regionId: address.region?.id,
        districtId: address.district?.id,
        house: address.house,
        street: address.street,
      };

      if (address.latitude && address.longitude) {
        setMapData({
          ...mapData,
          pmCoords: [address.latitude, address.longitude],
          centerCoords: [address.latitude, address.longitude],
        });
      }

      form.setFieldsValue(formData);

      openNotification("success", t("dataChanged", { ns: namespaces.notifications }));
      $checkedCustomerBranchAddress.reset();
    }
  }, [checkedCustomerBranchAddressState.data]);

  const onActivityTypeParentChange = () => {
    form.setFieldValue("activityTypeId", undefined);
  };

  const onCadastreNumberAdditionalChange = (e: any) => {
    form.setFieldValue("cadastreNumberAdditional", e.target.value.replace(/[^:\/\\\d]/g, ""));
  };

  const onRegionChange = (regionId: number, option: SelectOptionType<undefined>) => {
    setBranchAddress({
      ...branchAddress,
      regionName: option ? `${option.children}` : undefined,
      districtName: undefined,
    });

    form.setFieldsValue({
      districtId: undefined,
      neighborhoodId: undefined,
      latitude: undefined,
      longitude: undefined,
    });
  };

  const onDistrictChange = (districtId: number, option: SelectOptionType<undefined>) => {
    setBranchAddress({
      ...branchAddress,
      districtName: option ? `${option.children}` : undefined,
    });

    form.setFieldsValue({ neighborhoodId: undefined, latitude: undefined, longitude: undefined });
  };

  const onStreetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const street = e.target.value;

    withDebounce(() => {
      setBranchAddress({ ...branchAddress, street });
    });
  };

  const onHouseChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const house = e.target.value;

    withDebounce(() => {
      setBranchAddress({ ...branchAddress, house });
    });
  };

  const onMapDataIncome = (data: TMapData) => {
    setMapData(data);

    if (data && data.pmCoords) {
      form.setFields([
        {
          name: "latitude",
          value: data.pmCoords[0].toString(),
        },
        {
          name: "longitude",
          value: data.pmCoords[1].toString(),
        },
      ]);
    }
  };

  const onCadastreNumberChange = (e: any) => {
    const val = e.target.value.replaceAll("_", "");

    if (val.length === 19) {
      inputCadastreNumberAdditional.current?.focus();
    }
  };

  const onRentPeriodChange = ({ from, to }: { from?: string; to?: string }) => {
    form.setFieldValue("rentPeriod", from ? [from, to] : undefined);
  };

  const onDeviceTypesChange = (types: Array<string>) => {
    let hasFiscal = false;

    types.forEach((item) => {
      if (item === "FISCAL") {
        hasFiscal = true;
      }
    });

    setShowFiscalTypeFields(hasFiscal);
  };

  const onCheckAddress = (fields: TCheckAddressRentFields | TCheckAddressOwnFields) => {
    if (propertyType === "RENT") {
      const fieldsNew = fields as TCheckAddressRentFields;

      $checkedCustomerBranchAddress.effect({
        tin,
        rentCadastre: false,
        rentNumber: fieldsNew.rentNumber,
        beginDate: fieldsNew.rentPeriod[0],
        endDate: fieldsNew.rentPeriod[1],
      });
    } else {
      const fieldsNew = fields as TCheckAddressOwnFields;

      $checkedCustomerBranchAddress.effect({
        tin,
        rentCadastre: true,
        cadastreNumber: `${fieldsNew.cadastreNumber}${fieldsNew.cadastreNumberAdditional || ""}`,
      });
    }
  };

  const onFinish = () => {
    const formFields = form.getFieldsValue(true);

    const phones: Array<{ name: string; typeId: number; primary: boolean }> = [];

    const { workTypeId, otherTypeId } = getPhoneTypeIds(phoneTypes.data);

    if (formFields.mainPhone) {
      phones.push({
        name: getDigitsNums(formFields.mainPhone),
        typeId: workTypeId,
        primary: true,
      });
    }

    if (formFields.additionalPhone) {
      phones.push({
        name: getDigitsNums(formFields.additionalPhone),
        typeId: otherTypeId,
        primary: false,
      });
    }

    const data = {
      customerId,
      name: formFields.name,
      activityTypeId: formFields.activityTypeId,
      cadastreNumber:
        propertyType === "OWN" ? `${formFields.cadastreNumber}${formFields.cadastreNumberAdditional || ""}` : undefined,
      rent:
        propertyType === "RENT"
          ? {
              rentNumber: formFields.rentNumber,
              startDate: formFields.rentPeriod[0],
              endDate: formFields.rentPeriod[1],
            }
          : null,
      address: {
        regionId: formFields.regionId,
        districtId: formFields.districtId,
        neighborhoodId: formFields.neighborhoodId,
        house: formFields.house,
        street: formFields.street,
        apartment: formFields.apartment,
        latitude: formFields.latitude,
        longitude: formFields.longitude,
      },
      phones,
      types: formFields.types,
    };

    $createCustomerBranch.effect(data);
  };

  return (
    <>
      <ModalCustom.Loading show={createCustomerBranchState.loading || checkedCustomerBranchAddressState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>{t("addTT", { ns: namespaces.customers })}</ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        <FormCustom
          form={form}
          onFinish={onFinish}
          initialValues={{
            mainPhone: "",
            additionalPhone: "",
            cadastreNumber: "",
            propertyType: "OWN",
          }}
          phantomSubmit
        >
          <Form.Item
            label={t("title")}
            name="name"
            rules={[
              {
                required: true,
                validator(_, value) {
                  const isCyrillic = value.search(/[а-яА-ЯёЁ]/) !== -1;

                  if (isCyrillic) {
                    return Promise.reject(new Error(t("validations.onlyLat")));
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <InputCustom />
          </Form.Item>
          <Form.Item label={t("deviceType")} name="types" rules={requiredRules}>
            <CustomerBranchTypeSelect placeholder="" mode="multiple" onChange={onDeviceTypesChange} />
          </Form.Item>
          <Form.Item label={t("activityTypeCategory")} name="activityTypeParentId" rules={requiredRules}>
            <ActivityTypeParentSelect placeholder="" onChange={onActivityTypeParentChange} />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              prevValues["activityTypeParentId"] !== curValues["activityTypeParentId"]
            }
          >
            {() => {
              const parentId = form.getFieldValue("activityTypeParentId");

              return (
                <Form.Item label={t("activityType")} name="activityTypeId" rules={requiredRules}>
                  <ActivityTypeChildSelect placeholder="" parentId={parentId} />
                </Form.Item>
              );
            }}
          </Form.Item>
          {showFiscalTypeFields && (
            <>
              <Row gutter={[16, 0]} align="middle">
                <Col span={12}>
                  <Form.Item name="propertyType">
                    <Radio.Group>
                      <Radio value="OWN">{t("ownProperty")}</Radio>
                      <Radio value="RENT">{t("rent")}</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item shouldUpdate>
                    {() => {
                      const {
                        regionId,
                        districtId,
                        street,
                        house,
                        apartment,
                        cadastreNumber,
                        cadastreNumberAdditional,
                        rentNumber,
                        rentPeriod,
                      } = form.getFieldsValue(true);

                      const hasAddress = !!(regionId || districtId || street || house || apartment);
                      const hasOwnProperty =
                        propertyType === "OWN" && cadastreNumber?.replaceAll("_", "").length === 19;
                      const hasRentProperty = propertyType === "RENT" && !!(rentNumber && rentPeriod);

                      return (
                        <ButtonCustom
                          size="small"
                          type="primary"
                          onClick={() =>
                            onCheckAddress(
                              propertyType === "RENT"
                                ? { rentNumber, rentPeriod }
                                : { cadastreNumber, cadastreNumberAdditional },
                            )
                          }
                          disabled={(hasOwnProperty || hasRentProperty) && !hasAddress ? false : true}
                        >
                          {t("check")}
                        </ButtonCustom>
                      );
                    }}
                  </Form.Item>
                </Col>
              </Row>
              {propertyType === "OWN" ? (
                <Row gutter={[16, 0]}>
                  <Col span={12}>
                    <Form.Item
                      label={t("cadastreNum")}
                      name="cadastreNumber"
                      rules={[
                        {
                          required: true,
                          validateTrigger: "onSubmit",
                          validator(_, value) {
                            const cadastreVal = value.replaceAll("_", "");

                            if (cadastreVal.length !== 19) {
                              return Promise.reject(new Error(t("validations.wrongFormat")));
                            }

                            return Promise.resolve();
                          },
                        },
                      ]}
                      preserve={false}
                    >
                      <InputMask
                        mask="99:99:99:99:99:9999"
                        maskChar="_"
                        placeholder={"__:__:__:__:__:____"}
                        className="ant-input custom-input"
                        onChange={onCadastreNumberChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t("additional")} name="cadastreNumberAdditional" preserve={false}>
                      <Input onChange={onCadastreNumberAdditionalChange} ref={inputCadastreNumberAdditional} />
                    </Form.Item>
                  </Col>
                </Row>
              ) : (
                <>
                  <Row gutter={[16, 0]}>
                    <Col span={12}>
                      <Form.Item
                        label={t("contractNum")}
                        name="rentNumber"
                        rules={[{ required: true }]}
                        preserve={false}
                      >
                        <InputNumber className="full-width" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item shouldUpdate noStyle>
                        {() => {
                          const [from, to] = form.getFieldValue("rentPeriod") || [];

                          return (
                            <Form.Item
                              label={t("rentPeriod")}
                              name="rentPeriod"
                              rules={[{ required: true }]}
                              preserve={false}
                            >
                              <DatePickerCustom.Range
                                onChange={onRentPeriodChange}
                                from={from}
                                to={to}
                                className="full-width"
                              />
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}
            </>
          )}
          <Row gutter={20}>
            <Col span={8}>
              <Form.Item label={t("region")} name="regionId" rules={requiredRules}>
                <RegionSelect placeholder="" onChange={onRegionChange} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.regionId !== curValues.regionId}>
                {() => {
                  const regionId = form.getFieldValue("regionId");

                  return (
                    <Form.Item label={t("district")} name="districtId" rules={requiredRules}>
                      <DistrictSelect placeholder="" onChange={onDistrictChange} regionId={regionId} />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item noStyle shouldUpdate>
                {() => {
                  const districtId = form.getFieldValue("districtId");
                  return (
                    <Form.Item label={t("neighborhood")} name="neighborhoodId" rules={requiredRules}>
                      <NeighborhoodSelect
                        placeholder=""
                        dependencies={[districtId]}
                        requestParams={{
                          districtId,
                        }}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={16}>
              <Form.Item label={t("street")} name="street" rules={requiredRules}>
                <InputCustom onChange={onStreetChange} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label={t("house")} name="house" rules={requiredRules}>
                <InputCustom onChange={onHouseChange} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label={t("apartment")} name="apartment">
                <InputCustom />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Item
                label={t("phoneNum")}
                name="mainPhone"
                rules={[
                  {
                    required: true,
                    validateTrigger: "onSubmit",
                    validator(_, value) {
                      const name = getDigitsNums(value);

                      if (!name) {
                        return Promise.reject(new Error(t("requiredField")));
                      }

                      if (name.length !== 12) {
                        return Promise.reject(new Error(t("validations.wrongFormat")));
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputCustom.Mask mask="+\9\98 (99) 999 99 99" maskChar=" " />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={t("additionalPhoneNum")}
                name="additionalPhone"
                rules={[
                  {
                    validateTrigger: "onSubmit",
                    validator(_, value) {
                      const name = getDigitsNums(value);

                      if (value) {
                        if (name.length !== 12) {
                          return Promise.reject(new Error(t("validations.wrongFormat")));
                        }
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputCustom.Mask mask="+\9\98 (99) 999 99 99" maskChar=" " />
              </Form.Item>
            </Col>
          </Row>
          <div className="t-a-r m-b-10">
            <Form.Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.districtId !== curValues.districtId}>
              {() => {
                const regionId = form.getFieldValue("regionId");
                const districtId = form.getFieldValue("districtId");

                return (
                  <ButtonCustom
                    size="small"
                    type="primary"
                    disabled={!regionId || !districtId}
                    onClick={() => {
                      coordsMapModalControl.openModal({
                        address: branchAddress,
                        mapData,
                        onMapDataIncome,
                      });
                    }}
                  >
                    {t("showOnMap")}
                  </ButtonCustom>
                );
              }}
            </Form.Item>
          </div>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Item label={t("latitude")} name="latitude" rules={requiredRules} preserve={false}>
                <Input disabled={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label={t("longitude")} name="longitude" rules={requiredRules} preserve={false}>
                <Input disabled={true} />
              </Form.Item>
            </Col>
          </Row>
        </FormCustom>
      </ModalCustom.Middle>
      <ModalCustom.Footer>
        <ButtonCustom type="primary" size="small" onClick={() => form.submit()}>
          {t("add", { ns: namespaces.buttons })}
        </ButtonCustom>
      </ModalCustom.Footer>

      <ModalCustom open={coordsMapModalControl.visible} onCancel={coordsMapModalControl.closeModal} width={800}>
        <CoordsMap modalControlType={coordsMapModalControl} />
      </ModalCustom>
    </>
  );
};
