import React, { FC, useEffect, useMemo, useState } from "react";

import {
  ICustomerBranchesListItemModel,
  TCustomerBranchesListParams,
  TUploadCustomerBranchFileParams,
} from "#businessLogic/models/customerBranches";
import { TUploadFileModalControlType, UploadFileModal } from "#components/uploadFileModal";
import { DOCUMENT_TYPES } from "#constants/index";
import { useModalControl } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $confirmCustomerBranch, $customerBranchesList, $uploadCustomerBranchFile } from "#stores/customerBranches";
import { $customerBranchDocumentTypes } from "#stores/enums";
import { ButtonCustom } from "#ui/buttonCustom";
import { DrawerCustom } from "#ui/drawerCustom";
import { ModalCustom } from "#ui/modalCustom";
import { PopoverCustom } from "#ui/popoverCustom";
import { StatusCustom } from "#ui/status";
import { TableCustom, TableNumber } from "#ui/tableCustom";
import { openNotification } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { AddCustomerBranchModal, AddCustomerBranchModalType } from "./addModal";

type PropsTypes = {
  customerId: number;
  tin: string;
};

const isFilesUploaded = (files: ICustomerBranchesListItemModel["files"]) => {
  let lease = false;
  let cadastre = false;

  files.forEach((file: any) => {
    if (file.documentType.code === DOCUMENT_TYPES.LEASE_CONTRACT) lease = true;
    if (file.documentType.code === DOCUMENT_TYPES.CADASTRE) cadastre = true;
  });

  return lease && cadastre;
};

type TUploadCustomerBranchFileModalTypes = TUploadFileModalControlType<{
  customerBranchId: TUploadCustomerBranchFileParams["customerBranchId"];
}>;

export const CustomerBranches: FC<PropsTypes> = (props) => {
  const { customerId, tin } = props;

  const { t } = useTranslation();

  const customerBranchesListState = useStore($customerBranchesList.store);
  const confirmCustomerBranchState = useStore($confirmCustomerBranch.store);

  const [queryParams, setQueryParams] = useState<TCustomerBranchesListParams>({
    customerId,
  });

  const createCustomerBranchModalControl = useModalControl<AddCustomerBranchModalType>();
  const uploadFileModalControl = useModalControl<TUploadCustomerBranchFileModalTypes>();

  const customerBranchesListData = customerBranchesListState.data;

  const getList = () => {
    $customerBranchesList.effect(queryParams);
  };

  useEffect(() => {
    return () => {
      $customerBranchesList.reset();
    };
  }, []);

  useEffect(() => {
    getList();
  }, [queryParams]);

  useEffect(() => {
    if (confirmCustomerBranchState.success) {
      openNotification("success", t("ttConfirmed", { ns: namespaces.notifications }));
      $confirmCustomerBranch.reset();
    }
  }, [confirmCustomerBranchState.success]);

  const onFilterChange = (params: TCustomerBranchesListParams) => {
    setQueryParams(params);
  };

  const onPaginationChange = (page: number, size: number) => {
    onFilterChange({ page: page - 1 });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<ICustomerBranchesListItemModel> = [
      {
        title: "#",
        dataIndex: "num",
        width: 30,
        render: (_, row, index) => (
          <TableNumber value={customerBranchesListData.size * customerBranchesListData.number + index + 1} />
        ),
      },
      {
        title: t("title"),
        dataIndex: "name",
        render: (_, row) => row.name,
      },
      {
        title: t("activityType"),
        dataIndex: "activityType",
        render: (_, row) => row.activityType.name,
      },
      {
        title: t("address"),
        dataIndex: "address",
        render: (_, row) =>
          row.address ? (
            <div>
              {row.address.region.name}, {row.address.district?.name}
            </div>
          ) : (
            "-"
          ),
      },
      {
        title: t("deviceType"),
        dataIndex: "types",
        render: (_, row) =>
          row.types?.map((item, index) => (
            <span key={item.code}>
              {item.name}
              {index !== row.types!.length - 1 ? ", " : ""}
            </span>
          )) || "-",
      },
      {
        title: t("documents"),
        dataIndex: "files",
        render: (_, row) => (
          <>
            {!row.files.length && t("no")}
            {row.files.map((item) => (
              <div key={item.id} className="application-file-wrap">
                <div>
                  <a href={item.fileDownloadUri} target="_blank" download rel="noreferrer">
                    {item.name}
                  </a>
                </div>
              </div>
            ))}
          </>
        ),
      },
      {
        title: t("kkm"),
        dataIndex: "totalDeviceCount",
        render: (_, row) => row.totalDeviceCount,
      },
      {
        title: t("fm"),
        dataIndex: "totalFiscalCount",
        render: (_, row) => row.totalFiscalCount,
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) => <StatusCustom status={row.status.code}>{row.status.name}</StatusCustom>,
      },
      {
        title: t("ofdStatus"),
        dataIndex: "ofdStatus",
        render: (_, row) =>
          row.ofdStatus ? <StatusCustom status={row.ofdStatus.code}>{row.ofdStatus.name}</StatusCustom> : "-",
      },
      {
        title: "",
        width: 40,
        dataIndex: "actions",
        render: (_, row) => (
          <PopoverCustom
            placement="bottomRight"
            trigger="click"
            content={
              <>
                <PopoverCustom.Item>
                  <ButtonCustom
                    onClick={() => {
                      $confirmCustomerBranch.effect({ customerBranchId: row.id, customerId });
                    }}
                    loading={confirmCustomerBranchState.loading}
                    disabled={!row.files.length}
                  >
                    {t("confirm", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </PopoverCustom.Item>
                <PopoverCustom.Item>
                  <ButtonCustom
                    onClick={() => {
                      uploadFileModalControl.openModal({
                        params: {
                          customerBranchId: row.id,
                        },
                      });
                    }}
                    disabled={isFilesUploaded(row.files)}
                  >
                    {t("uploadFile", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </PopoverCustom.Item>
              </>
            }
          />
        ),
      },
    ];

    return columns;
  }, [customerBranchesListData.size, customerBranchesListData.number, confirmCustomerBranchState.loading]);

  return (
    <div className="customer-details__entity">
      <div className="customer-details__entity-head">
        <div className="customer-details__table-title">{t("tts")}</div>
        <div className="customer-details__entity-head__actions">
          <ButtonCustom
            size="small"
            onClick={() => {
              createCustomerBranchModalControl.openModal({
                customerId,
                tin,
              });
            }}
          >
            {t("addTT", { ns: namespaces.customers })}
          </ButtonCustom>
        </div>
      </div>
      <TableCustom
        rowKey="id"
        loading={customerBranchesListState.loading}
        dataSource={customerBranchesListData.content}
        columns={tableColumns}
        pagination={{
          total: customerBranchesListData.totalElements,
          pageSize: customerBranchesListData.size,
          current: queryParams.page ? queryParams.page + 1 : 1,
          onChange: onPaginationChange,
        }}
      />

      <DrawerCustom
        open={createCustomerBranchModalControl.visible}
        onCancel={createCustomerBranchModalControl.closeModal}
        width={800}
      >
        <AddCustomerBranchModal modalControl={createCustomerBranchModalControl} callback={getList} />
      </DrawerCustom>

      <ModalCustom open={uploadFileModalControl.visible} onCancel={uploadFileModalControl.closeModal}>
        <UploadFileModal
          modalControl={uploadFileModalControl}
          uploadAction={(data) => {
            $uploadCustomerBranchFile.effect({
              file: data.file,
              documentType: data.documentType!,
              customerBranchId: data.params.customerBranchId,
            });
          }}
          callback={getList}
          uploadFileStoreController={$uploadCustomerBranchFile}
          documentTypesStoreController={$customerBranchDocumentTypes}
        />
      </ModalCustom>
    </div>
  );
};
