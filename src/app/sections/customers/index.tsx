import React, { <PERSON> } from "react";

import { Route, RouteComponentProps, Switch } from "react-router-dom";

import { CustomerDetails } from "./details";
import { CaseDetails } from "./details/cases/details";
import { ConnectedServicesDetails } from "./details/connectedServices/details";
import { CustomersList } from "./list";

export const Customers: FC<RouteComponentProps> = (props) => {
  const { match } = props;

  return (
    <Switch>
      <Route exact path={match.path} component={CustomersList} />
      <Route path={`${match.path}/:customerId/cases/:id`} component={CaseDetails} />
      <Route exact path={`${match.path}/:id`} component={CustomerDetails} />
      <Route path={`${match.path}/:customerId/connected-services/:agreementId`} component={ConnectedServicesDetails} />
    </Switch>
  );
};
