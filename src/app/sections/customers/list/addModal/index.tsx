import React, { FC, useEffect, useState } from "react";

import { EnumReferenceTypes } from "#businessLogic/models/public/referencesTypes";
import { ModalControlType } from "#hooks/useModalControl";
import { namespaces } from "#localization/i18n.constants";
import { $createCustomer } from "#stores/customers";
import { $checkCompanyByTin } from "#stores/ofd";
import { $referenceItemsTypes } from "#stores/public";
import { ButtonCustom } from "#ui/buttonCustom";
import { ModalCustom } from "#ui/modalCustom";
import { getDigitsNums, getPhoneTypeIds, openNotification } from "#utils/helpers";
import { Col, Form, Row } from "antd";
import cn from "classnames";
import { useStore } from "effector-react";
import { FormFinishInfo } from "rc-field-form/lib/FormContext";
import { useTranslation } from "react-i18next";

import { AddCustomerFormStep1 } from "./step1";
import { AddCustomerFormStep2 } from "./step2";
import "./styles.scss";

type PropsTypes = {
  modalControl: ModalControlType;
  callback: () => void;
};

export type TCustomerCreateFormStep1Fields = {
  branchIds: Array<number>;
  branchNames: Array<string>;
  tin: string;
  businessType: string;
  activityTypeParentId: number;
  activityTypeId: number;
  oked: string;
  vatId: number;
  companyName: string;
  name: string;
};

export type TCustomerCreateFormStep2Fields = {
  directorLastName: string;
  directorFirstName: string;
  directorPatronymic: string;
  regionId: number;
  districtId: number;
  neighborhoodId: number;
  street: string;
  house: string;
  apartment: string;
  mainPhone: string;
  additionalPhone: string;
  appType: string;
  sourceId: number;
  readTermsAndConditions: boolean;
  readServiceContract: boolean;
};

export const AddCustomerModal: FC<PropsTypes> = (props) => {
  const { modalControl, callback } = props;

  const { t } = useTranslation();

  const createCustomerState = useStore($createCustomer.store);

  const referenceTypesState = useStore($referenceItemsTypes.store);

  const phoneTypes = referenceTypesState[EnumReferenceTypes.PHONE_TYPE];

  const [step, setStep] = useState<number>(1);

  const [form1] = Form.useForm<TCustomerCreateFormStep1Fields>();
  const [form2] = Form.useForm<TCustomerCreateFormStep2Fields>();

  useEffect(() => {
    if (!phoneTypes) {
      $referenceItemsTypes.effect({
        type: EnumReferenceTypes.PHONE_TYPE,
      });
    }

    return () => {
      $createCustomer.reset();
      $checkCompanyByTin.reset();
    };
  }, []);

  useEffect(() => {
    if (createCustomerState.success) {
      openNotification("success", t("customerAdded", { ns: namespaces.notifications }));
      modalControl.closeModal();
      callback();
    }
  }, [createCustomerState.success]);

  const handleFormFinish = (name: string, { values, forms }: FormFinishInfo) => {
    switch (name) {
      case "step1": {
        const checkCompanyByTinState = $checkCompanyByTin.store.getState();

        if (!checkCompanyByTinState.error) {
          setStep(step + 1);
        }

        break;
      }
      case "step2": {
        const fieldsStep1: TCustomerCreateFormStep1Fields = form1.getFieldsValue(true);
        const fieldsStep2: TCustomerCreateFormStep2Fields = form2.getFieldsValue(true);

        const phones: Array<{ name: string; typeId: number; primary: boolean }> = [];

        const { workTypeId, otherTypeId } = getPhoneTypeIds(phoneTypes.data);

        if (fieldsStep2.mainPhone) {
          phones.push({
            name: getDigitsNums(fieldsStep2.mainPhone),
            typeId: workTypeId,
            primary: true,
          });
        }

        if (fieldsStep2.additionalPhone) {
          phones.push({
            name: getDigitsNums(fieldsStep2.additionalPhone),
            typeId: otherTypeId,
            primary: false,
          });
        }

        const data = {
          branches: fieldsStep1.branchIds,
          name: fieldsStep1.name,
          companyName: fieldsStep1.companyName,
          tin: fieldsStep1.tin,
          oked: fieldsStep1.oked,
          activityTypeId: fieldsStep1.activityTypeId,
          businessType: fieldsStep1.businessType,
          vatId: fieldsStep1.vatId,
          director: {
            lastName: fieldsStep2.directorLastName,
            firstName: fieldsStep2.directorFirstName,
            patronymic: fieldsStep2.directorPatronymic,
          },
          address: {
            regionId: fieldsStep2.regionId,
            districtId: fieldsStep2.districtId,
            neighborhoodId: fieldsStep2.neighborhoodId,
            street: fieldsStep2.street,
            house: fieldsStep2.house,
            apartment: fieldsStep2.apartment,
          },
          phones,
          sourceId: fieldsStep2.sourceId,
        };

        $createCustomer.effect(data);

        break;
      }
      default: {
      }
    }
  };

  return (
    <>
      <ModalCustom.Loading show={createCustomerState.loading} />
      <ModalCustom.Header>
        <ModalCustom.Title>Добавить клиента</ModalCustom.Title>
      </ModalCustom.Header>
      <ModalCustom.Middle>
        <Form.Provider onFormFinish={handleFormFinish}>
          <div className="customer-form-wrap">
            <div className="customer-form" style={{ transform: `translate(-${(step - 1) * 100}%)` }}>
              <div className="customer-form__item">
                <div className={cn("customer-form__item__in", { active: step === 1 })}>
                  <AddCustomerFormStep1 form={form1} />
                </div>
              </div>
              <div className="customer-form__item">
                <div className={cn("customer-form__item__in", { active: step === 2 })}>
                  <AddCustomerFormStep2 form={form2} />
                </div>
              </div>
            </div>
          </div>
        </Form.Provider>
      </ModalCustom.Middle>
      <ModalCustom.Footer>
        <Row gutter={20}>
          <Col className="t-a-l" span={8}>
            {step > 1 && (
              <ButtonCustom
                size="small"
                onClick={() => {
                  setStep(step - 1);
                }}
              >
                {t("back", { ns: namespaces.buttons })}
              </ButtonCustom>
            )}
          </Col>
          <Col span={8}>
            <div className="customer-form__dots">
              {[1, 2].map((item, index) => (
                <div
                  key={item}
                  className={cn("customer-form__dots__item", { active: step === item })}
                  onClick={() => {
                    if (item < step) {
                      setStep(item);
                    } else {
                      form1.submit();
                    }
                  }}
                />
              ))}
            </div>
          </Col>
          <Col span={8}>
            <div>
              <ButtonCustom
                type="primary"
                size="small"
                onClick={() => {
                  if (step === 1) {
                    form1.submit();
                  } else {
                    form2.submit();
                  }
                }}
              >
                {step === 1 ? t("continue", { ns: namespaces.buttons }) : t("add", { ns: namespaces.buttons })}
              </ButtonCustom>
            </div>
          </Col>
        </Row>
      </ModalCustom.Footer>
    </>
  );
};
