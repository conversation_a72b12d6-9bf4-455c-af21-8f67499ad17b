import React, { FC, useEffect } from "react";

import { Price } from "#components/price";
import { requiredRules } from "#constants/index";
import { namespaces } from "#localization/i18n.constants";
import { ActivityTypeChildSelect, ActivityTypeParentSelect } from "#pickers/activityTypeSelect";
import { BranchSelect } from "#pickers/branchSelect";
import { BusinessTypeSelect } from "#pickers/businessTypeSelect";
import { VatSelect } from "#pickers/vatSelect";
import { $currentUser } from "#stores/account";
import { $checkCompanyByTin } from "#stores/ofd";
import { FormCustom } from "#ui/formCustom";
import { InputCustom } from "#ui/inputCustom";
import { SpinCustom } from "#ui/spinCustom";
import {
  regexForUppercaseLatinAndSpecialChars,
  validatePinFl,
  validateTin,
  validateUppercaseLatinWithSpecialChars,
} from "#utils/helpers";
import { Col, Form, FormInstance, Row } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { TCustomerCreateFormStep1Fields } from "../index";

type PropsTypes = {
  form: FormInstance<TCustomerCreateFormStep1Fields>;
};

export const AddCustomerFormStep1: FC<PropsTypes> = (props) => {
  const { form } = props;

  const { t } = useTranslation();

  const currentUserState = useStore($currentUser.store);
  const checkCompanyByTinState = useStore($checkCompanyByTin.store);

  const validateCompanyName = validateUppercaseLatinWithSpecialChars(regexForUppercaseLatinAndSpecialChars);

  useEffect(() => {
    if (currentUserState.data?.branch) {
      form.setFieldsValue({
        branchIds: [currentUserState.data.branch.id],
        branchNames: [currentUserState.data.branch.name],
      });
    }
  }, []);

  const onActivityTypeParentChange = () => {
    form.setFieldValue("activityTypeId", undefined);
  };

  const onTinOrPinflChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    if ((validateTin(value) || validatePinFl(value)) && checkCompanyByTinState.data?.tin !== value) {
      if (!checkCompanyByTinState.error) {
        $checkCompanyByTin.effect(value);
      }
    } else {
      if (checkCompanyByTinState.error) {
        $checkCompanyByTin.reset();
      }
    }
  };

  return (
    <FormCustom
      name="step1"
      form={form}
      initialValues={{
        oked: "",
        tin: "",
      }}
    >
      <Form.Item shouldUpdate noStyle>
        {() => {
          const branchNames = form.getFieldValue("branchNames");

          return (
            <Form.Item label={t("branchesAccess", { ns: namespaces.customers })} name="branchIds">
              <BranchSelect.Multiple
                placeholder={t("selectBranchesAccess", { ns: namespaces.customers })}
                valueNames={branchNames}
              />
            </Form.Item>
          );
        }}
      </Form.Item>
      <Row gutter={20}>
        <Col span={12}>
          <Form.Item
            label={t("tinOrPinfl", { ns: namespaces.customers })}
            name="tin"
            rules={[
              {
                validateTrigger: "onSubmit",
                pattern: /^(?:\d*)$/,
                message: t("onlyNum", { ns: namespaces.customers }),
              },
              {
                validateTrigger: "onSubmit",
                pattern: /^[\d]{9,14}$/,
                message: t("tinOrPinflValidation", { ns: namespaces.customers }),
              },
              { required: true },
            ]}
            help={
              checkCompanyByTinState.error ? (
                checkCompanyByTinState.error.detail
              ) : checkCompanyByTinState.data ? (
                <div>
                  {t("balance")}: <Price value={checkCompanyByTinState.data.balance} />
                </div>
              ) : undefined
            }
            {...(checkCompanyByTinState.error
              ? {
                  validateStatus: "error",
                }
              : {})}
          >
            <InputCustom
              placeholder={t("inputTinOrPinfl", { ns: namespaces.customers })}
              onChange={onTinOrPinflChange}
              suffix={checkCompanyByTinState.loading ? <SpinCustom size="small" /> : undefined}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Вид компании" name="businessType" rules={requiredRules}>
            <BusinessTypeSelect />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item label={t("activityTypeCategory")} name="activityTypeParentId" rules={requiredRules}>
        <ActivityTypeParentSelect onChange={onActivityTypeParentChange} />
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues["activityTypeParentId"] !== curValues["activityTypeParentId"]
        }
      >
        {() => {
          const parentId = form.getFieldValue("activityTypeParentId");

          return (
            <Form.Item label={t("activityType")} name="activityTypeId" rules={requiredRules}>
              <ActivityTypeChildSelect parentId={parentId} />
            </Form.Item>
          );
        }}
      </Form.Item>
      <Row gutter={20}>
        <Col span={12}>
          <Form.Item label={t("oked")} name="oked" rules={requiredRules}>
            <InputCustom.Mask placeholder="XXXXX" mask="99999" maskChar="" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={t("vat")} name="vatId" rules={requiredRules}>
            <VatSelect />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={20}>
        <Col span={12}>
          <Form.Item
            label={t("companyName")}
            name="companyName"
            rules={[{ required: true, pattern: validateCompanyName.regex }]}
            help={checkCompanyByTinState.data?.name}
          >
            <InputCustom
              placeholder={t("placeholders.inputTitle")}
              onChange={validateCompanyName.changeToUpperCase(form, "companyName")}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={t("companyBrand")} name="name" rules={requiredRules}>
            <InputCustom placeholder={t("placeholders.inputCompanyBrand")} />
          </Form.Item>
        </Col>
      </Row>
    </FormCustom>
  );
};
