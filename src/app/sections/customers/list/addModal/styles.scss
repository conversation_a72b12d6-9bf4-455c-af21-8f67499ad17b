@import "src/styles/variables.scss";

.customer-form-wrap {
  overflow: hidden;
  padding: 0 10px;
  margin: 0 -10px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.customer-form {
  display: flex;
  flex-grow: 1;
  transition: all 0.3s;

  &__item {
    display: flex;
    flex-direction: column;
    min-width: 100%;

    &__in {
      flex-grow: 1;

      &:not(.active) {
        display: none;
      }

      .ant-form {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }

  &__dots {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    &__item {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: $gray01;
      transition: background-color .3s ease-in-out;
      margin: 0 3px;
      cursor: pointer;

      &.active {
        background-color: #B6BCC5;
      }
    }
  }

  &__terms {
    padding: 20px;
    background-color: $gray04;
    border-radius: 8px;
    margin-top: auto;

    .ant-form-item-control-input {
      min-height: 0;
    }

    .ant-form-item {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}