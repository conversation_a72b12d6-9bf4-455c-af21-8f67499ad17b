import React, { FC, useEffect } from "react";

import { requiredRules } from "#constants/index";
import { LocalizationWithTag } from "#localization/components";
import { namespaces } from "#localization/i18n.constants";
import { DistrictSelect } from "#pickers/districtSelect";
import { NeighborhoodSelect } from "#pickers/neighborhoodSelect";
import { ReferenceTypeSelect } from "#pickers/referenceTypeSelect";
import { RegionSelect } from "#pickers/regionSelect";
import { $checkCompanyByTin } from "#stores/ofd";
import { FormCustom } from "#ui/formCustom";
import { InputCustom } from "#ui/inputCustom";
import {
  getDigitsNums,
  uppercaseLettersWithSymbolsRegex,
  validateUppercaseLatinWithSpecialChars,
} from "#utils/helpers";
import { Checkbox, Col, Form, FormInstance, Row } from "antd";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { TCustomerCreateFormStep2Fields } from "../index";

type PropsTypes = {
  form: FormInstance<TCustomerCreateFormStep2Fields>;
};

export const AddCustomerFormStep2: FC<PropsTypes> = (props) => {
  const { form } = props;

  const { t } = useTranslation();

  const checkCompanyByTinState = useStore($checkCompanyByTin.store);

  const validateHumanName = validateUppercaseLatinWithSpecialChars(uppercaseLettersWithSymbolsRegex);

  useEffect(() => {
    if (checkCompanyByTinState.data) {
      const { regionId, districtId } = checkCompanyByTinState.data;
      form.setFieldsValue({ regionId, districtId });
    }
  }, [checkCompanyByTinState.data]);

  const onRegionChange = () => {
    form.setFieldsValue({ districtId: undefined, neighborhoodId: undefined });
  };

  const onDistrictChange = () => {
    form.setFieldsValue({ neighborhoodId: undefined });
  };

  return (
    <FormCustom
      name="step2"
      form={form}
      initialValues={{
        mainPhone: "",
        additionalPhone: "",
      }}
    >
      <Row gutter={20}>
        <Col span={8}>
          <Form.Item
            label={t("directorLastName")}
            name="directorLastName"
            rules={[{ required: true, pattern: validateHumanName.regex }]}
          >
            <InputCustom onChange={validateHumanName.changeToUpperCase(form, "directorLastName")} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t("directorName")}
            name="directorFirstName"
            rules={[{ required: true, pattern: validateHumanName.regex }]}
          >
            <InputCustom onChange={validateHumanName.changeToUpperCase(form, "directorFirstName")} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t("directorPatronymic")}
            name="directorPatronymic"
            rules={[{ required: true, pattern: validateHumanName.regex }]}
          >
            <InputCustom onChange={validateHumanName.changeToUpperCase(form, "directorPatronymic")} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={20}>
        <Col span={8}>
          <Form.Item label={t("region")} name="regionId" rules={requiredRules}>
            <RegionSelect placeholder="" onChange={onRegionChange} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.regionId !== curValues.regionId}>
            {() => {
              const regionId = form.getFieldValue("regionId");

              return (
                <Form.Item label={t("district")} name="districtId" rules={requiredRules}>
                  <DistrictSelect placeholder="" onChange={onDistrictChange} regionId={regionId} />
                </Form.Item>
              );
            }}
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item noStyle shouldUpdate>
            {() => {
              const districtId = form.getFieldValue("districtId");
              return (
                <Form.Item label={t("neighborhood")} name="neighborhoodId" rules={requiredRules}>
                  <NeighborhoodSelect
                    placeholder=""
                    dependencies={[districtId]}
                    requestParams={{
                      districtId,
                    }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        </Col>
      </Row>
      <Form.Item label={t("street")} name="street" rules={requiredRules}>
        <InputCustom />
      </Form.Item>
      <Row gutter={20}>
        <Col span={12}>
          <Form.Item label={t("house")} name="house" rules={requiredRules}>
            <InputCustom />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={t("apartment")} name="apartment">
            <InputCustom />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={20}>
        <Col span={12}>
          <Form.Item
            label={t("phoneNum")}
            name="mainPhone"
            rules={[
              {
                required: true,
                validateTrigger: "onSubmit",
                validator(_, value) {
                  const name = getDigitsNums(value);

                  if (!name) {
                    return Promise.reject(new Error(t("requiredField")));
                  }

                  if (name.length !== 12) {
                    return Promise.reject(new Error(t("validations.wrongFormat")));
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <InputCustom.Mask mask="+\9\98 (99) 999 99 99" maskChar=" " />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t("additionalPhoneNum")}
            name="additionalPhone"
            rules={[
              {
                validateTrigger: "onSubmit",
                validator(_, value) {
                  const name = getDigitsNums(value);

                  if (value) {
                    if (name.length !== 12) {
                      return Promise.reject(new Error(t("validations.wrongFormat")));
                    }
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <InputCustom.Mask mask="+\9\98 (99) 999 99 99" maskChar=" " />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label={t("customerSource", { ns: namespaces.customers })} name="sourceId" rules={requiredRules}>
        <ReferenceTypeSelect
          requestParams={{
            type: "CUSTOMER_SOURCE",
          }}
        />
      </Form.Item>

      <div className="customer-form__terms">
        <Form.Item name="readTermsAndConditions" valuePropName="checked" rules={requiredRules}>
          <Checkbox>
            <LocalizationWithTag
              text={t("privacyPolicyTerm", { ns: namespaces.customers })}
              tags={{
                1: (text) => (
                  <a href="https://storage.smartpos.uz/terms_and_conditions.rtf" target="_blank" rel="noreferrer">
                    {text}
                  </a>
                ),
              }}
            />
          </Checkbox>
        </Form.Item>
        <Form.Item name="readServiceContract" valuePropName="checked" rules={requiredRules}>
          <Checkbox>
            <div>
              <LocalizationWithTag
                text={t("publicOfferTermRu", { ns: namespaces.customers })}
                tags={{
                  1: (text) => (
                    <a href="https://storage2.smartpos.uz/offerrus.pdf" target="_blank" rel="noreferrer">
                      {text}
                    </a>
                  ),
                }}
              />
            </div>
            <div>
              <LocalizationWithTag
                text={t("publicOfferTermUz", { ns: namespaces.customers })}
                tags={{
                  1: (text) => (
                    <a href="https://storage2.smartpos.uz/offeruzb.pdf" target="_blank" rel="noreferrer">
                      {text}
                    </a>
                  ),
                }}
              />
            </div>
          </Checkbox>
        </Form.Item>
      </div>
    </FormCustom>
  );
};
