import React, { FC, useEffect, useMemo } from "react";

import { ICustomersListItemModel } from "#businessLogic/models/customers";
import { useModalControl } from "#hooks/useModalControl";
import { useQueryParams } from "#hooks/useQueryParams";
import { namespaces } from "#localization/i18n.constants";
import { $customersList } from "#stores/customers";
import { ButtonCustom } from "#ui/buttonCustom";
import { ContentCustom } from "#ui/contentCustom";
import { ContentHeader } from "#ui/contentHeader";
import { DrawerCustom } from "#ui/drawerCustom";
import { StatusCustom } from "#ui/status";
import { TableCustom, TableNumber } from "#ui/tableCustom";
import { formatDate, formatPhoneNumber } from "#utils/helpers";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";
import { Link, RouteComponentProps } from "react-router-dom";

import { $customersFilterProps } from "../models";

import { AddCustomerModal } from "./addModal";
import { CustomersFilter } from "./filter";

export const CustomersList: FC<RouteComponentProps> = (props) => {
  const { match } = props;

  const { t } = useTranslation();

  const customersListState = useStore($customersList.store);

  const { queryParams, additionalParams, clearFilter, onFilterChange } = useQueryParams($customersFilterProps);

  const customersListData = customersListState.data;

  const createCustomerModalControl = useModalControl();

  const getList = () => {
    $customersList.effect(queryParams);
  };

  useEffect(() => {
    getList();
  }, [queryParams]);

  const onPaginationChange = (page: number) => {
    onFilterChange({ page: page - 1 });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<ICustomersListItemModel> = [
      {
        title: "#",
        dataIndex: "num",
        width: 30,
        render: (_, row, index) => (
          <TableNumber value={customersListData.size * customersListData.number + index + 1} />
        ),
      },
      {
        title: t("company"),
        dataIndex: "customer",
        render: (_, row) => (
          <>
            <div>
              <Link to={`${match.path}/${row.id}`}>{row.name}</Link>
            </div>
            <div>
              {row.businessType ? row.businessType.name : ""}&nbsp; {row.tin || row.pinfl}
            </div>
          </>
        ),
      },
      {
        title: t("branch"),
        dataIndex: "branch",
        render: (_, row) => row.branch.name,
      },
      {
        title: t("phone"),
        dataIndex: "phone",
        render: (_, row) => formatPhoneNumber(row.phone),
      },
      {
        title: t("activityType"),
        dataIndex: "activityType",
        render: (_, row) => (row.activityType ? row.activityType.name : "-"),
      },
      {
        title: t("registrationDate"),
        dataIndex: "createdDate",
        render: (_, row) => <div className="w-s-n">{formatDate(row.createdDate)}</div>,
      },
      {
        title: t("customerSource", { ns: namespaces.customers }),
        dataIndex: "source",
        render: (_, row) => (row.source ? row.source.name : "-"),
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) => <StatusCustom status={row.status.code}>{row.status.name}</StatusCustom>,
      },
    ];

    return columns;
  }, [customersListData.size, customersListData.number]);

  return (
    <ContentCustom>
      <ContentHeader>
        <ContentHeader.Left>
          {/*<div>*/}
          {/*  Total*/}
          {/*</div>*/}
          <CustomersFilter
            queryParams={queryParams}
            additionalParams={additionalParams}
            clearFilter={clearFilter}
            onFilterChange={onFilterChange}
          />
        </ContentHeader.Left>
        <ContentHeader.Right>
          <ButtonCustom size="small" type="primary" onClick={() => createCustomerModalControl.openModal()}>
            {t("add", { ns: namespaces.buttons })}
          </ButtonCustom>
        </ContentHeader.Right>
      </ContentHeader>
      <TableCustom
        fixedHead={true}
        rowKey="id"
        loading={customersListState.loading}
        dataSource={customersListData.content}
        columns={tableColumns}
        pagination={{
          total: customersListData.totalElements,
          pageSize: customersListData.size,
          current: queryParams.page ? queryParams.page + 1 : 1,
          onChange: onPaginationChange,
        }}
      />
      <DrawerCustom open={createCustomerModalControl.visible} onCancel={createCustomerModalControl.closeModal}>
        <AddCustomerModal modalControl={createCustomerModalControl} callback={getList} />
      </DrawerCustom>
    </ContentCustom>
  );
};
