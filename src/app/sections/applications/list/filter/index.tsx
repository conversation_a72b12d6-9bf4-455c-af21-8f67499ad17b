import React, { FC } from "react";
import { FilterCustom } from "#ui/filterCustom";
import { InputCustom } from "#ui/inputCustom";
import { FilterOnChangeType } from "#types/common";
import { DatePickerCustom } from "#ui/datePickerCustom";
import {
  TApplicationsListAdditionalParams,
  TApplicationsListParams
} from "#businessLogic/models/applications";
import { useTranslation } from "react-i18next";

type PropsTypes = {
  queryParams: TApplicationsListParams;
  additionalParams: TApplicationsListAdditionalParams;
  clearFilter: () => void;
  onFilterChange: FilterOnChangeType<TApplicationsListParams, TApplicationsListAdditionalParams>;
};

export const ApplicationsFilter: FC<PropsTypes> = (props) => {
  const { queryParams, additionalParams, onFilterChange, clearFilter } = props;

  const { t } = useTranslation();

  return (
    <FilterCustom
      queryParams={queryParams}
      additionalParams={additionalParams}
      onFilterChange={onFilterChange}
      clearQueryParams={clearFilter}
      numberOfFilterItemsToRender={5}
      items={[
        {
          key: "search",
          label: t("search"),
          fieldType: "search",
          render: ({ params, onChange }) => (
            <InputCustom.Search value={params.search} onChange={(search) => onChange({ search })} />
          ),
        },
        {
          key: ["from", "to"],
          label: t("dates"),
          render: ({ params, onChange }) => (
            <DatePickerCustom.Range
              size="small"
              from={params.from}
              to={params.to}
              onChange={({ from, to }) =>
                onChange({
                  from,
                  to,
                })
              }
            />
          )
        }
      ]}
    />
  )
};