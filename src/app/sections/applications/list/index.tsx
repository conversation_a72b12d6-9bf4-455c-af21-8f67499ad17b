import React, { FC, useEffect, useMemo } from "react";
import { TableCustom } from "#ui/tableCustom";
import { ContentCustom } from "#ui/contentCustom";
import { ApplicationsFilter } from "./filter";
import { $applicationsFilterProps } from "../models";
import { useQueryParams } from "#hooks/useQueryParams";
import { $applicationsList } from "#stores/applications";
import { useStore } from "effector-react";
import { ColumnsType } from "antd/lib/table/interface";
import { IApplicationsListItemModel } from "#businessLogic/models/applications";
import { StatusCustom } from "#ui/status";
import { ContentHeader } from "#ui/contentHeader";
import { useTranslation } from "react-i18next";

export const ApplicationsList: FC = () => {

  const { t } = useTranslation();

  const applicationsListState = useStore($applicationsList.store);

  const { queryParams, additionalParams, clearFilter, onFilterChange } = useQueryParams($applicationsFilterProps);

  const applicationsListData = applicationsListState.data;

  const getList = () => {
    $applicationsList.effect(queryParams);
  };

  useEffect(() => {
    getList();
  }, [queryParams]);

  const onPaginationChange = (page: number, size: number) => {
    onFilterChange({ page: page - 1 });
  };

  const tableColumns = useMemo(() => {
    const columns: ColumnsType<IApplicationsListItemModel> = [
      {
        title: "#",
        dataIndex: "num",
        width: 30,
        render: (_, row, index) => (
          <div className="w-s-n">{applicationsListData.size * applicationsListData.number + index + 1}</div>
        ),
      },
      {
        title: t("tin"),
        dataIndex: "tin",
        render: (_, row) => row.tin,
      },
      {
        title: t("kkm"),
        dataIndex: "device",
        render: (_, row) => row.device.serialNumber,
      },
      {
        title: t("status"),
        dataIndex: "status",
        render: (_, row) => (
          <StatusCustom status={row.status.code}>{row.status.name}</StatusCustom>
        )
      },
    ];

    return columns;
  }, [applicationsListData.size, applicationsListData.number]);

  return (
    <ContentCustom>
      <ContentHeader>
        <ContentHeader.Left>
          <ApplicationsFilter
            queryParams={queryParams}
            additionalParams={additionalParams}
            clearFilter={clearFilter}
            onFilterChange={onFilterChange}
          />
        </ContentHeader.Left>
      </ContentHeader>
      <TableCustom
        fixedHead={true}
        rowKey="id"
        loading={applicationsListState.loading}
        dataSource={applicationsListData.content}
        columns={tableColumns}
        pagination={{
          total: applicationsListData.totalElements,
          pageSize: applicationsListData.size,
          current: queryParams.page ? queryParams.page + 1 : 1,
          onChange: onPaginationChange,
        }}
      />
    </ContentCustom>
  )
};