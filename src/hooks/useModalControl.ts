import { useState } from "react";

export type ModalControlType<T = unknown> = {
  visible: boolean;
  openModal: (props?: T) => void;
  closeModal: () => void;
  updateProps: (props: T) => void;
  resetModal: () => void;
} & T;

export const useModalControl = <T>(initialModalProps?: T): ModalControlType<T> => {
  const [modalProps, setModalProps] = useState({ visible: false, ...initialModalProps });

  const openModal = (props?: T) => {
    setModalProps({ visible: true, ...props });
  };

  const closeModal = () => {
    setModalProps({ ...modalProps, visible: false, ...initialModalProps });
  };

  const updateProps = (props: T) => {
    setModalProps({ ...modalProps, ...props });
  };

  const resetModal = () => {
    setModalProps({ visible: false, ...initialModalProps });
  };

  return {
    ...modalProps,
    openModal,
    closeModal,
    updateProps,
    resetModal,
  } as ModalControlType<T>;
};
