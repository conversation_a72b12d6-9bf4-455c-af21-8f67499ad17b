import { useEffect, useState } from "react";

import { AdvancedFilterStore } from "#core/stateManager/types/store";
import { Event, Store } from "effector";
import { useStore } from "effector-react";
import { History, Location } from "history";
import queryString from "query-string";
import { equals, isNil, mergeRight, pipe, reject } from "ramda";
import { useHistory, useLocation } from "react-router";

type AnyParamsType = { [key: string]: any };

const SEPARATE = "&separate=1&";

const wrap = (num: number) => {
  return `(${num})`;
};

const unwrap = (wrapped: unknown): string | number | unknown => {
  if (typeof wrapped !== "string") {
    return wrapped;
  }

  if (!wrapped.startsWith("(") || !wrapped.endsWith(")")) {
    return wrapped;
  }
  const inner = wrapped.slice(1, -1);
  const num = Number(inner);
  if (Number.isNaN(num)) {
    return wrapped;
  }
  return num;
};

const wrapNumber = (params: AnyParamsType) => {
  const result: AnyParamsType = {};

  Object.entries(params).forEach(([key, value]) => {
    if (typeof value === "number") {
      result[key] = wrap(value);
      return;
    }

    if (Array.isArray(value)) {
      value.map((v) => {
        if (!result[key]) {
          result[key] = [];
        }
        if (typeof v === "number") {
          result[key] = [...result[key], wrap(v)];
        } else {
          result[key] = [...result[key], v];
        }
      });
      return;
    }

    result[key] = value;
  });

  return result;
};

const unwrapNumber = (params: AnyParamsType) => {
  const result: AnyParamsType = {};

  Object.entries(params).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((v) => {
        if (!result[key]) {
          result[key] = [];
        }
        result[key] = [...result[key], unwrap(v)];
      });
    } else {
      result[key] = unwrap(value);
    }
  });

  return result;
};

export const parseParams = (value: string, isAdditionalParams: boolean) => {
  const indx = isAdditionalParams ? 1 : 0;
  const data = value.split(SEPARATE)[indx];

  const params = queryString.parse(data, { arrayFormat: "bracket-separator" });

  return unwrapNumber(params);
};

export const updateQueryParams = pipe(
  (search: string, params: any) => mergeRight(queryString.parse(search), params),
  reject(isNil),
  (p) => queryString.stringify(wrapNumber(p), { arrayFormat: "bracket-separator" }),
);

export const updatePathNew = (
  history: History<unknown>,
  location: Location,
  qp: AnyParamsType,
  add: { [key: string]: any },
) => {
  const requestParams = updateQueryParams("", qp);
  const additionalParams = updateQueryParams("", add);

  history.push(
    `${location.pathname}${requestParams ? "?" : ""}${requestParams}${
      additionalParams ? SEPARATE : ""
    }${additionalParams}`,
  );
};

type useQueryParamsNewReturnType = <T extends object, A extends object>(storeController: {
  update: Event<AdvancedFilterStore<T, A>>;
  store: Store<AdvancedFilterStore<T, A>>;
  reset: Event<void>;
}) => {
  queryParams: T;
  additionalParams: A;
  onFilterChange: (params: T, additional?: A) => void;
  clearFilter: () => void;
};

export const useQueryParams: useQueryParamsNewReturnType = (storeController) => {
  const storeState = useStore(storeController.store);

  const history = useHistory();
  const location = useLocation();

  const [queryParams, setQueryParams] = useState({
    ...storeState.queryParams,
    ...parseParams(location.search, false),
  } as typeof storeState.queryParams);

  const [additionalParams, setAdditionalQueryParams] = useState({
    ...storeState.additionalParams,
    ...parseParams(location.search, true),
  } as typeof storeState.additionalParams);

  useEffect(() => {
    if (Object.keys(queryParams).length) {
      storeController.update({
        queryParams,
        additionalParams,
      });
    }
  }, []);

  useEffect(() => {
    const locationParams = parseParams(location.search, false) as typeof queryParams;

    if (!equals(queryParams, locationParams)) {
      updatePathNew(history, location, { ...queryParams }, { ...additionalParams });
    }
  }, [queryParams]);

  const onFilterChange = (newParams: typeof queryParams, newAdditionalParams?: typeof additionalParams) => {
    const localParams = { page: undefined, ...newParams };

    setQueryParams((params: typeof queryParams) => ({ ...params, ...localParams }));
    setAdditionalQueryParams(
      (params: typeof additionalParams) => ({ ...params, ...newAdditionalParams } as typeof additionalParams),
    );

    storeController.update({
      queryParams: localParams,
      additionalParams: newAdditionalParams ? newAdditionalParams : ({} as typeof additionalParams),
    });
  };

  const clearFilter = () => {
    storeController.reset();

    const currentQueryParams = storeController.store.getState();

    setQueryParams(currentQueryParams.queryParams);
    if (currentQueryParams.additionalParams) {
      setAdditionalQueryParams(currentQueryParams.additionalParams);
    }
  };

  return {
    queryParams,
    additionalParams: additionalParams!,
    onFilterChange,
    clearFilter,
  };
};
