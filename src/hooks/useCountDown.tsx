import React, { useEffect, useState } from "react";

type PropsTypes = {
  start: boolean;
  onTimerFinish: () => void;
  seconds: number;
};

export function useCountDown({ start, onTimerFinish, seconds = 60 }: PropsTypes) {
  const [count, setCount] = useState(seconds);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (start) {
      interval = setInterval(() => {
        if (count <= 0) {
          onTimerFinish && onTimerFinish();
          clearInterval(interval);
        } else {
          setCount((value) => value - 1);
        }
      }, 1000);
    }

    return () => {
      clearInterval(interval);
    };
  }, [start, count]);

  useEffect(() => {
    if (start && count === 0) {
      setCount(seconds);
    }
  }, [start]);

  return count;
}
