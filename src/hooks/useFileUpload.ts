import { useState } from "react";

import { i18n } from "#localization/i18n";
import { namespaces } from "#localization/i18n.constants";
import { message } from "antd";
import { RcFile } from "antd/lib/upload/interface";

const maxFileSize = 10000000;

const sizeLimitError = {
  nameRu: i18n.t("validations.bigSize", { ns: namespaces.fields, size: "10" }),
};

export const useFileUpload = (type: string) => {
  const [uploadedDocuments, setUploadedDocuments] = useState<RcFile[]>([]);

  const uploadProps = {
    onRemove: () => {
      setUploadedDocuments([]);
    },
    beforeUpload: (file: RcFile) => {
      if (type) {
        const description = i18n.t("validations.onlySpecFileTypes", { ns: namespaces.fields, type });

        if (file.size > maxFileSize) {
          const errorText = sizeLimitError.nameRu;

          message.error(errorText);
          return false;
        }
        if (type === "pdf") {
          const isPdf = file.type === "application/pdf";

          if (!isPdf) {
            message.error(description);
            return false;
          }
        }
        if (type === "excel") {
          const isExcel = file.name.match(/xls/);

          if (!isExcel) {
            message.error(description);
            return false;
          }
        }
      }

      setUploadedDocuments([file]);

      return false;
    },
    fileList: uploadedDocuments,
  };

  return {
    uploadedDocuments,
    setUploadedDocuments,
    uploadProps,
  };
};
