import React, { KeyboardEvent, useRef, useState } from "react";

import { withDebounce } from "#utils/helpers";
import { Form } from "antd";
import { TextAreaRef } from "antd/es/input/TextArea";

type TChatFormFields = {
  message: string;
};

type TChatEmployees = {
  id: number;
  name: string;
  login: string;
};

export const useChatInput = <Employees extends TChatEmployees>() => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageValRef = useRef<string>("");
  const messageCaretPositionRef = useRef<number>(0);
  const userSearchValRef = useRef<string>("");
  const messageTextareaRef = useRef<TextAreaRef>(null);
  const taggedEmployeeIdsRef = useRef<Record<string, number>>({});

  const [uploadedFile, setUploadedFile] = useState<{ type: "doc" | "image"; file: File } | null>(null);
  const [openUserSelectPopover, setOpenUserSelectPopover] = useState<boolean>(false);
  const [userSearchVal, setUserSearchVal] = useState<string>("");

  const [form] = Form.useForm<TChatFormFields>();

  const handlePaste = (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const clipboardData = event.clipboardData || event.nativeEvent.clipboardData;
    if (!clipboardData) return;

    const items = clipboardData.items;
    if (!items.length) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (item.type.indexOf("image") === 0) {
        const file = item.getAsFile();
        if (file) {
          setUploadedFile({
            type: "image",
            file,
          });
        }
      } else if (item.kind === "file") {
        const file = item.getAsFile();

        if (file) {
          setUploadedFile({
            type: "doc",
            file,
          });
        }
      } else {
        // text
      }
    }
  };

  const onSelectMessage = (e: React.SyntheticEvent<HTMLTextAreaElement>) => {
    const textarea = e.currentTarget;

    const currentMessage = messageValRef.current;
    const val = textarea.value;
    const caretPosition = textarea.selectionStart;
    const currentSymbol = val[caretPosition - 1];
    const prevCaretSymbol = val[caretPosition - 2];
    const nextCaretSymbol = val[caretPosition];
    const isUserMovedCaret = val === currentMessage;

    if (val.length > currentMessage.length) {
      if (
        currentSymbol === "@" &&
        (prevCaretSymbol === undefined || prevCaretSymbol === " " || prevCaretSymbol === "\n") &&
        (nextCaretSymbol === undefined || nextCaretSymbol === " ")
      ) {
        setOpenUserSelectPopover(true);
      }

      if (openUserSelectPopover) {
        userSearchValRef.current = `${userSearchValRef.current}${currentSymbol}`;

        if (currentSymbol === " ") {
          setOpenUserSelectPopover(false);
          userSearchValRef.current = "";
        }
      }
    } else {
      // if caret moved or delete message
      let i = caretPosition - 1;
      let userName = "";
      userSearchValRef.current = "";
      let open = false;

      while (!(val[i] === " " || val[i] === undefined)) {
        if (val[i] === "@") {
          if (val[i - 1] === " " || val[i - 1] === "\n" || val[i - 1] === undefined) {
            userSearchValRef.current = userName;
            open = true;
          }
        } else {
          userName = `${val[i]}${userName}`;
        }
        i--;
      }

      setOpenUserSelectPopover(open);
    }

    if (isUserMovedCaret) {
      setUserSearchVal(userSearchValRef.current);
    } else {
      withDebounce(() => {
        setUserSearchVal(userSearchValRef.current);
      });
    }

    messageValRef.current = val;
    messageCaretPositionRef.current = caretPosition;
  };

  const onEmployeeSelect = (employeeItem: Employees) => {
    const messageVal: string = form.getFieldValue("message");

    const login = employeeItem.login.split("@")[0];

    let i = messageCaretPositionRef.current - 1;
    let userName = "";

    while (messageVal[i] !== "@") {
      userName = `${messageVal[i]}${userName}`;
      i--;
    }

    const firstPart = `${messageVal.substring(0, messageCaretPositionRef.current - userName.length)}${login}`;
    const secondPart = messageVal.substring(messageCaretPositionRef.current);

    form.setFieldValue("message", `${firstPart}${secondPart[0] === " " ? secondPart : ` ${secondPart}`}`);

    userSearchValRef.current = "";
    setUserSearchVal("");
    setOpenUserSelectPopover(false);
    taggedEmployeeIdsRef.current = {
      ...taggedEmployeeIdsRef.current,
      [`@${login}`]: employeeItem.id,
    };

    messageTextareaRef.current?.focus();
  };

  const onPressEnter = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    const message = form.getFieldValue("message");
    let newMessage = message;

    if (!e.shiftKey) {
      newMessage = message.trim();
      form.submit();
    }

    form.setFieldValue("message", newMessage);
  };

  const getTaggedEmployeeIds = () => {
    const formFields = form.getFieldsValue(true);

    if (!formFields.message) {
      return [];
    }

    const mentions: Array<string> = formFields.message.match(/@[a-zA-Z0-9._-]+/g) || [];
    return mentions.reduce<number[]>((prevIds, userName) => {
      const res = [...prevIds];

      if (taggedEmployeeIdsRef.current[userName]) {
        res.push(taggedEmployeeIdsRef.current[userName]);
      }

      return res;
    }, []);
  };

  return {
    form,
    getTaggedEmployeeIds,
    onEmployeeSelect,
    onPressEnter,
    onSelectMessage,
    handlePaste,
    uploadedFile,
    setUploadedFile,
    openUserSelectPopover,
    userSearchVal,
    messageTextareaRef,
    messagesEndRef,
  };
};

export type TChatInput = ReturnType<typeof useChatInput>;
