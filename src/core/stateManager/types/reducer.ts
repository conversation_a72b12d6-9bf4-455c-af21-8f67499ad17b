import { ResponseType } from "#types/api";

import { AdvancedFilterStore } from "./store";

export interface ReducersCollectionType<P, R, S> {
  zeroReducer?: ReducerType<P, S>;
  doneReducer?: DoneReducerType<P, R, S>;
  failReducer?: FailReducerType<P, R, S>;
}

export type ReducerType<P, S> = (state: S, params: P) => S;
export type DoneReducerType<P, R, S = any> = (state: S, response: ResponseType<P, R>) => S;
export type FailReducerType<P, R, S = any> = (state: S, error: { params: P; error: any }, initialState: S) => S;
export type GetReducerType = <P, R, S>(state: S) => DoneReducerType<P, R, S> | ReducerType<S, P>;

export type AdvancedFilterHandlerType<P, A> = (
  prevStore: AdvancedFilterStore<P, A>,
  props: AdvancedFilterStore<P, A>,
) => AdvancedFilterStore<P, A>;
