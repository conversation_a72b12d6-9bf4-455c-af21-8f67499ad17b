import { Event, Store } from "effector";

import { HandlerType } from "./handler";
import { ReducersCollectionType } from "./reducer";

export interface CreateStoreReturnType<E, S> {
  effect: E;
  store: Store<S>;
  reset: Event<void>;
}

export type CreateXHRStoreType = <S = any, P = any, R = any>(
  handler: HandlerType<P, R>,
  initialState: S,
  reducers?: ReducersCollectionType<P, R, S>,
  resets?: any,
) => CreateStoreReturnType<typeof handler, S>;

export interface AdvancedFilterStore<P, A> {
  queryParams: P;
  additionalParams: A;
}

export type CreateAdvancedFilterStorePropTypes = <P, A = undefined>(
  initialState: AdvancedFilterStore<P, A>,
  resets?: Array<any>,
) => {
  update: Event<AdvancedFilterStore<P, A>>;
  store: Store<AdvancedFilterStore<P, A>>;
  reset: Event<void>;
};

export type SingleField<T> = {
  [K in keyof T]: Pick<T, K>;
}[keyof T];

export type TCreateGlobalStore = <StateType>(
  initialState: StateType,
  withoutSpread?: boolean,
  currentHandler?: (prevStore: StateType) => StateType,
) => {
  update: Event<StateType | SingleField<StateType>>;
  store: Store<StateType>;
  reset: Event<void>;
};

export type ResetType = Event<void>;
