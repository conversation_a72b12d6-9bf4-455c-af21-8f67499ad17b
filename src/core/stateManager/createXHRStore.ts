import { XHRState } from "#constructors/index";
import { Event, Store, combine, createEffect, createEvent, createStore } from "effector";

import { GetReducerType } from "./types/reducer";
import { AdvancedFilterStore, CreateXHRStoreType, ResetType, SingleField, TCreateGlobalStore } from "./types/store";

const zeroReducerDefault = (state: any) => ({ ...state, loading: true });
const doneReducerDefault = (state: any, response: any) => {
  return {
    ...state,
    fulfilled: true,
    loading: false,
    data: response.result.data,
    error: null,
  };
};

const doneSuccessReducerDefault = (state: any) => ({
  ...state,
  fulfilled: true,
  loading: false,
  success: true,
  error: null,
});

const failReducerDefault = (state: any, { error }: any, initialState: any) => ({
  ...state,
  fulfilled: true,
  loading: false,
  error: error.response && error.response.data,
  data: initialState.data,
});

const getDoneReducer: GetReducerType = (initialState) => {
  return "data" in (initialState as object) ? doneReducerDefault : doneSuccessReducerDefault;
};

export const globalReset = createEvent<void>();

export const createXHRStore: CreateXHRStoreType = (handler, initialState, reducers = {}, resets) => {
  const zeroReducer = reducers.zeroReducer !== undefined ? reducers.zeroReducer : zeroReducerDefault;
  type State = typeof initialState;
  type Params = Parameters<typeof handler>[0];
  type Response = ReturnType<typeof handler>;
  const doneReducer =
    reducers.doneReducer !== undefined ? reducers.doneReducer : getDoneReducer<Params, Response, State>(initialState);
  const failReducer = reducers.failReducer !== undefined ? reducers.failReducer : failReducerDefault;

  const effect = createEffect<typeof handler>({ handler });
  const reset = createEvent<void>();
  const store: Store<typeof initialState> = createStore(initialState).reset(reset).reset(globalReset);

  if (zeroReducer) {
    store.on(effect, zeroReducer);
  }

  if (doneReducer) {
    // @ts-ignore
    store.on(effect.done, doneReducer);
  }

  if (failReducer) {
    store.on(effect.fail, (prevState, response) => failReducer(prevState, response, initialState));
  }

  if (resets) {
    if (Array.isArray(resets)) {
      resets.forEach((func) => {
        store.reset(func);
      });
    } else {
      store.reset(resets);
    }
  }

  return {
    effect,
    store,
    reset,
  };
};

type CreateAdvancedFilterStorePropTypes = <P, A>(
  initialState: AdvancedFilterStore<P, A>,
  config?: {
    currentHandler?: (_: AdvancedFilterStore<P, A>, props: AdvancedFilterStore<P, A>) => AdvancedFilterStore<P, A>;
    resets?: ResetType | ResetType[];
    initStore?: (initStat: AdvancedFilterStore<P, A>) => Store<AdvancedFilterStore<P, A>>;
  },
) => {
  update: Event<AdvancedFilterStore<P, A>>;
  store: Store<AdvancedFilterStore<P, A>>;
  reset: Event<void>;
};

export const createFilterStore: CreateAdvancedFilterStorePropTypes = (initialState, config) => {
  const handler =
    config && config.currentHandler
      ? config.currentHandler
      : (prevStore: typeof initialState, props: typeof initialState) => ({
          queryParams: {
            ...prevStore.queryParams,
            ...props.queryParams,
          },
          additionalParams: {
            ...prevStore.additionalParams,
            ...props.additionalParams,
          },
        });

  const update = createEvent<typeof initialState>();
  const reset = createEvent<void>();
  const store: Store<typeof initialState> =
    config && config.initStore ? config.initStore(initialState) : createStore(initialState);

  store.on(update, handler).reset(reset);

  if (config && config.resets) {
    if (Array.isArray(config.resets)) {
      config.resets.forEach((func) => {
        store.reset(func);
      });
    } else {
      store.reset(config.resets);
    }
  }

  return {
    update,
    store,
    reset,
  };
};

export function initStore<Q, A, S>(
  initialState: AdvancedFilterStore<Q, A>,
  store: Store<XHRState<S>>,
  callback: (state: NonNullable<S>) => AdvancedFilterStore<Q, A>,
) {
  return combine(store, (state) => {
    if (!state.data) {
      return initialState;
    } else {
      const additionalInitialState = callback(state.data);

      return {
        queryParams: {
          ...additionalInitialState.queryParams,
          ...initialState.queryParams,
        },
        additionalParams: {
          ...additionalInitialState.additionalParams,
          ...initialState.additionalParams,
        },
      };
    }
  });
}

export const createGlobalStore: TCreateGlobalStore = (initialState, withoutSpread, currentHandler) => {
  const handler = currentHandler
    ? currentHandler
    : (prevStore: typeof initialState, props: typeof initialState) => {
        return withoutSpread ? props : { ...prevStore, ...props };
      };

  type State = typeof initialState;

  const update = createEvent<State | SingleField<State>>();
  const reset = createEvent<void>();
  const store: Store<typeof initialState> = createStore(initialState).on(update, handler).reset(reset);

  return {
    update,
    store,
    reset,
  };
};
