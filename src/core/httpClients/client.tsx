import React from "react";

import { notification } from "antd";
import axios, { AxiosRequestConfig } from "axios";
import Cookies from "js-cookie";
import queryString from "query-string";

type RequestHeaders = {
  Authorization?: string;
  Language: string;
};

export const httpClient = axios.create({
  withCredentials: true,
});

httpClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const status = (error.response && error.response.status) || 0;

    if (status === 401 && window.location.pathname !== "/sign-in") {
      if (!error.response.config.headers || !error.response.config.headers.stopRedirect) {
        window.location.href = "/sign-in";
      }
    } else if (status === 403) {
      notification["error"]({
        message: "Ошибка",
        description: "У вас нет необходимого разрешения. Пожалуйста, свяжитесь с вашим администратором",
      });
    } else if (
      status >= 500 ||
      (status === 404 && !error.response.config.ignoreNotFoundNotification) ||
      status === 400 ||
      status === 409
    ) {
      // если ошибка типа Blob (это случается когда указан responseType: 'blob' в запросе)
      if (error.response?.data instanceof Blob) {
        try {
          const text = await error.response.data.text();
          error.response.data = JSON.parse(text);
        } catch {}
      }
      if (error.response && !error.response.config.withoutNotification) {
        const description = (
          <div>
            {error.response.data.title || "Что-то пошло не так..."}
            {error.response.data.title !== error.response.data.detail && (
              <>
                <br />
                Детали: {error.response.data.detail}
              </>
            )}
          </div>
        );

        notification["error"]({
          message: "Ошибка",
          description: description,
        });
      }
    }
    return Promise.reject(error);
  },
);

httpClient.interceptors.request.use((config) => {
  const token = Cookies.get("access-token");
  const currentLanguage = localStorage.getItem("i18nextLng");
  const headers: RequestHeaders = {
    Language: currentLanguage === "uz" ? "uz-Latn-UZ" : "ru",
  };

  if (token) {
    config.paramsSerializer = (params) => {
      return queryString.stringify(params, { arrayFormat: "comma" });
    };
    headers.Authorization = "Bearer " + token;
  }

  config.headers = Object.assign(config.headers, headers);
  return config;
});

type requestConfigType = AxiosRequestConfig & {
  ignoreNotFoundNotification?: boolean;
  withoutNotification?: boolean;
};

export const httpGet = (url: string, config?: requestConfigType) =>
  httpClient({
    method: "get",
    url,
    ...(config ? config : {}),
  });

export const httpPost = (url: string, config?: requestConfigType) =>
  httpClient({
    method: "post",
    url,
    ...(config ? config : {}),
  });

export const httpPut = (url: string, config?: requestConfigType) =>
  httpClient({
    method: "put",
    url,
    ...(config ? config : {}),
  });

export const httpPatch = (url: string, config?: requestConfigType) =>
  httpClient({
    method: "patch",
    url,
    ...(config ? config : {}),
  });

export const httpDelete = (url: string, config?: requestConfigType) =>
  httpClient({
    method: "delete",
    url,
    ...(config ? config : {}),
  });
