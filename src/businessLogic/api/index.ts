import * as account from "./resources/account";
import * as agreement from "./resources/agreement";
import * as applications from "./resources/applications";
import * as auth from "./resources/auth";
import * as banks from "./resources/banks";
import * as billingServices from "./resources/billingServices";
import * as branchDevices from "./resources/branchDevices";
import * as branches from "./resources/branches";
import * as caseFile from "./resources/caseFile";
import * as caseNote from "./resources/caseNote";
import * as cases from "./resources/cases";
import * as changesLogs from "./resources/changesLogs";
import * as customerBank from "./resources/customerBank";
import * as customerBranchDevice from "./resources/customerBranchDevice";
import * as customerBranches from "./resources/customerBranches";
import * as customerDevices from "./resources/customerDevices";
import * as customerFiscalModule from "./resources/customerFiscalModules";
import * as customers from "./resources/customers";
import * as employee from "./resources/employee";
import * as enums from "./resources/enums";
import * as fileUpload from "./resources/fileUpload";
import * as ledger from "./resources/ledger";
import * as merchantAcquiring from "./resources/merchantAcquiring";
import * as ofd from "./resources/ofd";
import * as ofdApplications from "./resources/ofdApplications";
import * as publicApi from "./resources/public";

export const api = {
  auth,
  account,
  merchantAcquiring,
  customers,
  customerBranches,
  customerDevices,
  customerFiscalModule,
  applications,
  enums,
  public: publicApi,
  branches,
  ofd,
  ofdApplications,
  branchDevices,
  billingServices,
  cases,
  ledger,
  banks,
  caseFile,
  changesLogs,
  employee,
  caseNote,
  customerBranchDevice,
  fileUpload,
  customerBank,
  agreement,
};
