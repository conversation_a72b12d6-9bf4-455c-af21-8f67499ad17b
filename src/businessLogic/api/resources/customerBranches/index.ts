import { PaginationListModel, TIdNameModel } from "#businessLogic/models/common";
import {
  ICheckedCustomerBranchAddressDetailsModel,
  ICustomerBranchesListItemModel,
  TCheckCustomerBranchAddressParams,
  TConfirmCustomerBranchParams,
  TCreateCustomerBranchParams,
  TCustomerBranchesListParams,
  TCustomerBranchesLookupParams,
  TUploadCustomerBranchFileParams,
} from "#businessLogic/models/customerBranches";
import { httpGet, httpPost, httpPut } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getCustomerBranchesList: HandlerType<
  TCustomerBranchesListParams,
  PaginationListModel<ICustomerBranchesListItemModel>
> = (params) => {
  return httpGet("/api/partner/v1/customer-branches", { params });
};

export const createCustomerBranch: HandlerType<TCreateCustomerBranchParams, number> = (data) => {
  return httpPost("/api/partner/v1/customer-branches", { data });
};

export const confirmCustomerBranch: HandlerType<TConfirmCustomerBranchParams, void> = (data) => {
  return httpPost("/api/partner/v1/ofd-customer-branches/confirm", { data });
};

export const uploadCustomerBranchFile: HandlerType<TUploadCustomerBranchFileParams, void> = ({
  customerBranchId,
  documentType,
  file,
}) => {
  return httpPut(`/api/partner/v1/customer-branches/${customerBranchId}/${documentType}`, { data: file });
};

export const getCustomerBranchesLookup: HandlerType<TCustomerBranchesLookupParams, Array<TIdNameModel>> = (params) => {
  return httpGet("/api/partner/v1/customer-branches/lookup", { params });
};

export const checkCustomerBranchAddress: HandlerType<
  TCheckCustomerBranchAddressParams,
  ICheckedCustomerBranchAddressDetailsModel
> = ({ tin, ...params }) => {
  return httpGet(`/api/partner/v1/ofd-customer-branches/rent-cadastre/${tin}`, { params });
};
