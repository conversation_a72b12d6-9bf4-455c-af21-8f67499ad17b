import {
  ICaseDetailsModel,
  ICaseStatusHistoriesListItemModel,
  ICasesListItemModel,
  TCasesCreateParams,
  TCasesListParams,
  TCasesUpdateParams,
} from "#businessLogic/models/cases";
import { PaginationListModel, TStatusChangeParams } from "#businessLogic/models/common";
import { httpGet, httpPatch, httpPost, httpPut } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getCasesList: HandlerType<TCasesListParams, PaginationListModel<ICasesListItemModel>> = (params) => {
  return httpGet("/api/partner/v1/cases", { params });
};

export const getCaseDetails: HandlerType<number, PaginationListModel<ICaseDetailsModel>> = (id) => {
  return httpGet(`/api/partner/v1/cases/${id}`);
};

export const createCase: HandlerType<TCasesCreateParams, number> = (data) => {
  return httpPost("/api/partner/v1/cases", { data });
};

export const updateCase: HandlerType<TCasesUpdateParams, number> = ({ id, ...data }) => {
  return httpPut(`/api/partner/v1/cases/${id}`, { data });
};

export const getCaseStatusHistories: HandlerType<number, Array<ICaseStatusHistoriesListItemModel>> = (caseId) => {
  return httpGet("/api/partner/v1/case-status-histories/items", { params: { caseId } });
};

export const getCaseTemplate: HandlerType<number, Array<string>> = (id) => {
  return httpGet(`/api/partner/v1/cases/${id}/generate-template`);
};

export const changeCaseStatus: HandlerType<TStatusChangeParams, void> = ({ id, ...params }) => {
  return httpPatch(`/api/partner/v1/cases/${id}/status`, { params });
};
