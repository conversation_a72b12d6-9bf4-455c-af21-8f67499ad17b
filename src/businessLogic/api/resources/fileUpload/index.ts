import { TUploadEntityDocumentParams } from "#businessLogic/models/fileUpload";
import { httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const uploadEntityDocument: HandlerType<TUploadEntityDocumentParams, void> = ({
  entityId,
  entityType,
  documentType,
  file,
}) => {
  return httpPost(`/api/files/upload/${entityId}/${entityType}/${documentType}`, { data: file });
};
