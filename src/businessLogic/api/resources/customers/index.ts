import { PaginationListModel } from "#businessLogic/models/common";
import {
  ICustomerDetailsModel,
  ICustomersListItemModel,
  TCreateCustomerParams,
  TCustomersListParams,
  TCustomersPublicOfferSendParams,
  TCustomersPublicOfferVerifyParams,
  TCustomersVerifyParams,
} from "#businessLogic/models/customers";
import { httpGet, httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getCustomersList: HandlerType<TCustomersListParams, PaginationListModel<ICustomersListItemModel>> = (
  params,
) => {
  return httpGet("/api/partner/v1/customers", { params });
};

export const getCustomerDetails: HandlerType<number, ICustomerDetailsModel> = (id) => {
  return httpGet(`/api/partner/v1/customers/${id}`, { withoutNotification: true });
};

export const createCustomer: HandlerType<TCreateCustomerParams, ICustomerDetailsModel> = (data) => {
  return httpPost("/api/partner/v1/customers", { data });
};

export const sendCustomersPublicOffer: HandlerType<TCustomersPublicOfferSendParams, any> = (data) => {
  return httpPost("/api/partner/v1/customers/public-offer/send", {
    data,
  });
};

export const verifyCustomersPublicOffer: HandlerType<TCustomersPublicOfferVerifyParams, any> = (data) => {
  return httpPost("/api/partner/v1/customers/public-offer/verify", {
    data,
  });
};

export const verifyCustomer: HandlerType<TCustomersVerifyParams, void> = (data) => {
  return httpPost("/api/partner/v1/customers/activate", { data });
};

export const getCustomerActivationCode: HandlerType<number, void> = (id) => {
  return httpGet(`/api/partner/v1/customers/${id}/send-activation-code`);
};
