import { IChangesLogsListItemModel, TChangesLogsListParams } from "#businessLogic/models/changesLogs";
import { PaginationListModel } from "#businessLogic/models/common";
import { httpGet } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getChangesLogs: HandlerType<TChangesLogsListParams, PaginationListModel<IChangesLogsListItemModel>> = (
  params,
) => {
  return httpGet("/api/partner/v1/changes-logs", { params });
};
