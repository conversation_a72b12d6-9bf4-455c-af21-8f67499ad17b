import { httpGet } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";
import { ICurrentUserModel } from "#businessLogic/models/account";
import { TApplicationsListParams } from "#businessLogic/models/applications";

export const getApplicationsList: HandlerType<TApplicationsListParams, ICurrentUserModel> = (params) => {
  return httpGet("/api/partner/v1/applications", { params });
};
