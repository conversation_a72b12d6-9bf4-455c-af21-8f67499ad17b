import { TCodeNameModel } from "#businessLogic/models/common";
import { httpGet } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getBusinessTypes: HandlerType<void, Array<TCodeNameModel>> = () => {
  return httpGet("/api/enums/businessTypes");
};

export const getCustomerAppTypes: HandlerType<void, Array<TCodeNameModel>> = () => {
  return httpGet("/api/enums/customer-app-types");
};

export const getCustomerBranchDocumentTypes: HandlerType<void, Array<TCodeNameModel>> = () => {
  return httpGet("/api/enums/customer/branch/documentTypes");
};
