import { ICurrentUserModel } from "#businessLogic/models/account";
import {
  TMerchantAcquiringListParams,
  TMerchantAcquiringTurnoverParams,
} from "#businessLogic/models/merchantAcquiring";
import { httpGet } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getMerchantAcquiringList: HandlerType<TMerchantAcquiringListParams, ICurrentUserModel> = (params) => {
  return httpGet("/api/partner/v1/merchant-acquiring", { params });
};

export const merchantAcquiringTurnover: HandlerType<TMerchantAcquiringTurnoverParams, number> = (params) => {
  return httpGet(`/api/partner/v1/merchant-acquiring/turnover`, { params });
};
