import {
  IAgreementInvoicesModel,
  IAgreementModel,
  IAgreementQuoteListItemModel,
  IGetAgreementInvoicesModel,
  IQuoteDetailsModel,
  IUploadQuoteFileModel,
  TAgreementQuoteCreateParams,
} from "#businessLogic/models/agreement";
import { PaginationListModel } from "#businessLogic/models/common";
import { httpGet, httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const generatedInvoices: HandlerType<string, string> = (invoiceId) => {
  return httpGet(`/api/billing-invoice/${invoiceId}/template`);
};

export const getAgreementDetails: HandlerType<string, IAgreementModel> = (id) => {
  return httpGet(`/api/billing-services/agreements/${id}`);
};

export const getQuoteDetails: HandlerType<string, IQuoteDetailsModel> = (quoteId) => {
  return httpGet(`/api/quote/${quoteId}`);
};

export const getGeneratedQuote: HandlerType<string, string> = (quoteId) => {
  return httpGet(`/api/quote/${quoteId}/template`);
};

export const uploadQuoteFile: HandlerType<IUploadQuoteFileModel, void> = (data) => {
  return httpPost(`/api/quote/${data.id}/upload`, {
    data: data.file,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const getAgreementQuoteList: HandlerType<any, PaginationListModel<IAgreementQuoteListItemModel>> = ({
  agreementId,
  ...params
}) => {
  return httpGet(`/api/billing-services/quote/agreement/${agreementId}`, { params });
};

export const createAgreementQuote: HandlerType<TAgreementQuoteCreateParams, number> = (data) => {
  return httpPost("/api/billing-services/quote/create", { data });
};

export const getAgreementInvoices: HandlerType<
  IGetAgreementInvoicesModel,
  PaginationListModel<IAgreementInvoicesModel>
> = ({ agreementId, params }) => {
  return httpGet(`/api/billing-services/billing-invoice/get-by-agreement-id/${agreementId}`, {
    params,
  });
};
