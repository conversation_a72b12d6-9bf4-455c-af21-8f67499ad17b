import { PaginationListModel, TIdCodeNameModel } from "#businessLogic/models/common";
import {
  ICustomerDeviceLookupItemModel,
  ICustomerDevicesListItemModel,
  TCreateCustomerDeviceParams,
  TCustomerDevicesListParams,
  TCustomerDevicesLookupParams,
} from "#businessLogic/models/customerDevices";
import { httpGet, httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getCustomerDevicesList: HandlerType<
  TCustomerDevicesListParams,
  PaginationListModel<ICustomerDevicesListItemModel>
> = (params) => {
  return httpGet("/api/partner/v1/customer-devices", { params });
};

export const createCustomerDevice: HandlerType<TCreateCustomerDeviceParams, number> = (data) => {
  return httpPost("/api/partner/v1/customer-devices", { data });
};

export const getCustomerDevicesLookup: HandlerType<
  TCustomerDevicesLookupParams,
  Array<ICustomerDeviceLookupItemModel>
> = (params) => {
  return httpGet("/api/partner/v1/customer-devices/lookup", { params });
};

export const getDeviceModels: HandlerType<void, Array<TIdCodeNameModel>> = () => {
  return httpGet("/api/company/models");
};
