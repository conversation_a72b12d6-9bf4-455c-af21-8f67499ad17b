import {
  ICaseNoteByCaseListItemModel,
  ICaseNoteEmployeeItemModel,
  TCaseNoteByCaseListParams,
  TCaseNoteCreateParams,
  TCaseNoteUploadFileParams,
} from "#businessLogic/models/caseNote";
import { PaginationListModel } from "#businessLogic/models/common";
import { httpGet, httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getNotesByCase: HandlerType<
  TCaseNoteByCaseListParams,
  PaginationListModel<ICaseNoteByCaseListItemModel>
> = ({ caseId, ...params }) => {
  return httpGet(`/api/case-note/get-by-case/${caseId}`, { params });
};

export const createCaseNote: HandlerType<TCaseNoteCreateParams, void> = (data) => {
  return httpPost("/api/case-note/create", { data });
};

export const uploadCaseNoteFile: HandlerType<TCaseNoteUploadFileParams, ICaseNoteByCaseListItemModel> = ({
  caseId,
  file,
}) => {
  return httpPost(`/api/case-note/case-id/${caseId}/upload`, { data: file });
};

export const getCaseNoteEmployees: HandlerType<any, Array<ICaseNoteEmployeeItemModel>> = (params) => {
  return httpGet("/api/case-note/employees/lookup", { params });
};
