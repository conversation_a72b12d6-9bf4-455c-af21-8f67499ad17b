import { IDeviceItemModel } from "#businessLogic/models/common";
import {
  TCreateCustomerFiscalModuleParams,
  TCustomerBalance,
  TCustomerFiscalModuleLookupParams,
  TFiscalModuleLookupParams,
  TFiscalizationParams,
} from "#businessLogic/models/customerFiscalModules";
import { UZKASSA_TIN } from "#constants/index";
import { httpGet, httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const createCustomerFiscalModule: HandlerType<TCreateCustomerFiscalModuleParams, number> = (data) => {
  return httpPost("/api/partner/v1/customer-fiscal-modules", { data });
};

export const getCustomerFiscalModulesLookup: HandlerType<TCustomerFiscalModuleLookupParams, Array<IDeviceItemModel>> = (
  params,
) => {
  return httpGet("/api/partner/v1/customer-branch-fiscal-modules/lookup", { params });
};

export const fiscalization: HandlerType<TFiscalizationParams, number> = (data) => {
  return httpPost("/api/partner/v1/fiscalizations", { data });
};

export const getFiscalModulesLookup: HandlerType<TFiscalModuleLookupParams, Array<IDeviceItemModel>> = (params) => {
  return httpGet("/api/partner/v1/fiscal-modules/lookup", { params });
};

export const getCustomerBalance: HandlerType<string, TCustomerBalance> = (tin) => {
  return httpGet(`/api/partner/v1/check-balance/${UZKASSA_TIN}/${tin}`);
};
