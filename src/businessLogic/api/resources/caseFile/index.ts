import { ICaseFileByCaseListItemModel, TCaseUploadParams } from "#businessLogic/models/caseFile";
import { PaginationListModel } from "#businessLogic/models/common";
import { httpGet, httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getCaseFile: HandlerType<number, PaginationListModel<ICaseFileByCaseListItemModel>> = (id) => {
  return httpGet(`/api/case-file/get-by-case/${id}`);
};

export const uploadCaseDocument: HandlerType<TCaseUploadParams, void> = ({ caseId, documentType, file }) => {
  return httpPost(`/api/case-file/upload?caseId=${caseId}&documentType=${documentType}`, {
    data: file,
  });
};
