import { ICheckCompanyByTinModel, TConfirmCustomerFiscalParams } from "#businessLogic/models/ofd";
import { httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const checkCompanyByTin: HandlerType<string, ICheckCompanyByTinModel> = (tinOrPinFl) => {
  return httpPost(`/api/ofd/customer/check/${tinOrPinFl}`);
};

export const confirmCustomerFiscal: HandlerType<TConfirmCustomerFiscalParams, void> = (data) => {
  return httpPost("/api/ofd/customer/fiscal", { data });
};
