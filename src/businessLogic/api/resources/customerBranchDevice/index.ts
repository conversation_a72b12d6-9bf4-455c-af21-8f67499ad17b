import { TDownloadDeviceApplicationFileParams } from "#businessLogic/models/customerBranchDevice";
import { httpGet, httpPut } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const downloadDeviceApplicationFile: HandlerType<TDownloadDeviceApplicationFileParams, Blob> = (params) => {
  return httpGet("/api/partner/v1/customer-branch-devices/application-file", { params, responseType: "blob" });
};

export const customerBranchDevicesSyncSmartpos: HandlerType<number, void> = (customerBranchDeviceId) => {
  return httpPut(`/api/partner/v1/customer-branch-devices/smartpos/${customerBranchDeviceId}/sync`);
};
