import { TBranchDevicesLookupParams, TCustomerBranchDevicesParams } from "#businessLogic/models/branchDevices";
import { IDeviceItemModel } from "#businessLogic/models/common";
import { httpGet } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getBranchDevicesLookup: HandlerType<TBranchDevicesLookupParams, Array<IDeviceItemModel>> = (params) => {
  return httpGet("/api/partner/v1/branch-devices/lookup", { params });
};

export const getCustomerBranchDevices: HandlerType<TCustomerBranchDevicesParams, Array<IDeviceItemModel>> = (
  params,
) => {
  return httpGet("/api/partner/v1/customer-branch-devices/items", { params });
};
