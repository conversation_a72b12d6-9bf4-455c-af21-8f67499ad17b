import { TCodeNameModel, TIdNameModel } from "#businessLogic/models/common";
import { IActivityTypesLookupModel, TActivityTypesLookupParams } from "#businessLogic/models/public/activityTypes";
import { INeighborhoodLookupItemModel, TNeighborhoodQueryParams } from "#businessLogic/models/public/neighborhood";
import {
  IReferenceTypeItemModel,
  TReferenceTypes,
  TReferenceTypesLookupParams,
} from "#businessLogic/models/public/referencesTypes";
import { VatItemModel } from "#businessLogic/models/public/vat";
import { httpGet } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getActivityTypesLookup: HandlerType<TActivityTypesLookupParams, IActivityTypesLookupModel[]> = (
  params,
) => {
  return httpGet("/api/public/v1/activity-types/lookup", {
    params,
  });
};

export const getVatItems: HandlerType<void, VatItemModel[]> = () => {
  return httpGet("/api/public/v1/vats/items");
};

export const getRegionItems: HandlerType<void, TIdNameModel[]> = () => {
  return httpGet("/api/public/v1/regions/items");
};

export const getDistrictItems: HandlerType<number, Array<TIdNameModel>> = (regionId) => {
  return httpGet(`/api/public/v1/districts/items/${regionId}`);
};

export const getNeighborhoodsLookup: HandlerType<TNeighborhoodQueryParams, INeighborhoodLookupItemModel[]> = (
  params,
) => {
  return httpGet("/api/public/v1/neighborhoods/lookup", { params });
};

export const getReferenceTypesLookup: HandlerType<TReferenceTypesLookupParams, Array<IReferenceTypeItemModel>> = (
  params,
) => {
  return httpGet("/api/public/v1/references/lookup", { params });
};

export const getReferenceItemsByType: HandlerType<
  {
    type: TReferenceTypes;
  },
  Array<IReferenceTypeItemModel>
> = ({ type }) => {
  return httpGet(`/api/public/v1/references/items/${type}`);
};

export const getCustomerBranchTypes: HandlerType<void, Array<TCodeNameModel>> = () => {
  return httpGet("/api/public/v1/commons/customer-branch/types");
};

export const getBankTypes: HandlerType<void, TCodeNameModel[]> = () => {
  return httpGet("/api/public/v1/commons/bank-types");
};

export const getBankCardTypes: HandlerType<void, TCodeNameModel[]> = () => {
  return httpGet("/api/public/v1/commons/bank-card-types");
};
