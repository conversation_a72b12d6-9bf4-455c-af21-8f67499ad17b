import {
  IBillingGroupedServiceChildrenItemModel,
  IBillingGroupedServiceItemModel,
  IBillingServicesAgreementsListItemModel,
  IBillingSingleServiceItemModel,
  IPublicOfferModel,
  TAgreementActivateParams,
  TAgreementCreateParams,
  TBillingServicesAgreementsListParamsType,
} from "#businessLogic/models/billingServices";
import { PaginationListModel } from "#businessLogic/models/common";
import { httpGet, httpPost } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getAgreementList: HandlerType<
  TBillingServicesAgreementsListParamsType,
  PaginationListModel<IBillingServicesAgreementsListItemModel>
> = (params) => {
  return httpGet("/api/partner/v1/billing-services/agreements", { params });
};

export const activateAgreement: HandlerType<TAgreementActivateParams, void> = (data) => {
  return httpPost("/api/partner/v1/billing-services/agreements/activate", { data });
};

export const getPublicOffer: HandlerType<string, IPublicOfferModel> = (tin) => {
  return httpGet(`/api/partner/v1/billing-services/customer-public-offers/${tin}`, {
    ignoreNotFoundNotification: true,
  });
};

export const getGroupedXizmats: HandlerType<{ companyInn: string }, Array<IBillingGroupedServiceItemModel>> = (
  params,
) => {
  return httpGet("/api/partner/v1/billing-services/grouped-xizmats", { params });
};

export const getGroupedXizmatChildren: HandlerType<number, Array<IBillingGroupedServiceChildrenItemModel>> = (id) => {
  return httpGet(`/api/partner/v1/billing-services/xizmat/${id}/items`);
};

export const getCustomerBillingBalance: HandlerType<string, any> = (tin) => {
  return httpGet(`/api/partner/v1/billing-services/balances/${tin}`, { ignoreNotFoundNotification: true });
};

export const getSingleXizmats: HandlerType<{ companyInn: string }, Array<IBillingSingleServiceItemModel>> = (
  params,
) => {
  return httpGet("/api/partner/v1/billing-services/single-xizmats", { params });
};

export const createAgreement: HandlerType<TAgreementCreateParams, any> = (data) => {
  return httpPost("/api/partner/v1/billing-services/agreements", { data });
};

export const serviceCalculation: HandlerType<any, any> = ({ xizmatId, tin, ...params }) => {
  return httpGet(`/api/billing-services/agreements/show-calculation/xizmat/${xizmatId}/customer/${tin}`, {
    params,
  });
};
