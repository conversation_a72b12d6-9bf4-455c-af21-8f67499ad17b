import { httpGet, httpPost, httpPut } from "#core/httpClients/client";
import { HandlerType } from "#core/stateManager/types/handler";

export const getCustomerBankInfo: HandlerType<number, any> = (customerId) => {
  return httpGet(`/api/partner/v1/customer-banks/customer/${customerId}`);
};

export const addCustomerBank: HandlerType<any, any> = (data) => {
  return httpPost("/api/partner/v1/customer-banks", { data });
};

export const updateCustomerBank: HandlerType<any, any> = (data) => {
  return httpPut(`/api/partner/v1/customer-banks/${data.id}`, { data });
};
