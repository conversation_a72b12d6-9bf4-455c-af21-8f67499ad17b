import { TCodeNameModel, TIdNameModel, TListQueryParams } from "#businessLogic/models/common";

export type TCustomerBranchesListParams = {
  search?: string;
  from?: string;
  to?: string;
  customerId?: number;
  recurring?: boolean;
} & TListQueryParams;

export type TCustomerBranchesListAdditionalParams = {};

export type ICustomerBranchesListItemModel = {
  id: number;
  name: string;
  brand: string;
  address: {
    region: TIdNameModel;
    district: TIdNameModel | null;
    neighborhood: TIdNameModel | null;
  };
  activityType: TIdNameModel;
  apayCategory: TIdNameModel;
  phone: string;
  totalDeviceCount: number;
  totalFiscalCount: number;
  status: TCodeNameModel;
  ofdStatus: TCodeNameModel | null;
  types: Array<TCodeNameModel> | null;
  files: Array<{
    id: number;
    name: string;
    fileName: string;
    fileDownloadUri: string;
    contentType: string;
    documentType: TCodeNameModel;
    size: number;
    fileSize: string;
    createdBy: string;
    createdDate: string;
  }>;
};

export type TCreateCustomerBranchParams = {
  name: string;
  customerId: number;
  address: {
    regionId: number;
    districtId: number;
    neighborhoodId: number;
    street: string;
    house: string;
    apartment: string;
    latitude: string;
    longitude: string;
  };
  activityTypeId: number;
  cadastreNumber: string | undefined;
  rent: {
    rentNumber: string;
    startDate: string;
    endDate: string;
  } | null;
  phones: Array<{
    name: string;
    typeId: number;
    primary: boolean;
  }>;
  types: Array<string>;
};

export type TConfirmCustomerBranchParams = {
  customerId: number;
  customerBranchId: number;
};

export type TUploadCustomerBranchFileParams = {
  customerBranchId: number;
  documentType: string;
  file: FormData;
};

export type TCustomerBranchesLookupParams = {
  search?: string;
  customerId?: number;
  type?: string;
};

export type TCheckCustomerBranchAddressParams = {
  tin: string;
} & (
  | { rentCadastre: false; rentNumber: string; beginDate: string; endDate: string }
  | { rentCadastre: true; cadastreNumber: string }
);

export interface ICheckedCustomerBranchAddressDetailsModel {
  region: TIdNameModel | null;
  district: TIdNameModel | null;
  neighborhood: TIdNameModel | null;
  landmark: string | null;
  street: string | null;
  house: string | null;
  apartment: string | null;
  longitude: number | null;
  latitude: number | null;
}
