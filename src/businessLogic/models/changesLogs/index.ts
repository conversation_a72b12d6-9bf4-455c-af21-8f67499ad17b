import { TCodeNameModel, TIdNameModel, TListQueryParams } from "#businessLogic/models/common";

export type TChangesLogsListParams = {
  entityId: number;
  entityType: string;
} & TListQueryParams;

export interface IChangesLogsListItemModel {
  id: string;
  entityId: number;
  entityType: TCodeNameModel;
  entityName: string;
  field: string;
  oldValue: string;
  newValue: string;
  updater: TIdNameModel;
  updatedDate: string;
}
