import { TCodeNameModel, TIdNameModel, TListQueryParams } from "#businessLogic/models/common";

export type TCasesListParams = {
  search?: string;
  from?: string;
  to?: string;
  customerId?: number;
} & TListQueryParams;

export type TCasesListAdditionalParams = {};

export type TCasesCreateParams = {
  customerId: number;
  type: string;
  data: THumoConnectionChangeCaseDataParams;
  checkAccount?: boolean;
};

export type TCasesUpdateParams = {
  id: number;
} & TCasesCreateParams;

type THumoConnectionChangeCaseDataParams = {
  accountNumber: string;
  bankId: number;
  bankName: string;
  bankType: string;
  branchName: string;
  customerBranchDeviceId: number;
  customerBranchId: number;
  customerId: number;
  deviceId: number;
  deviceSerialNumber: string;
  deviceType: string;
  mfo: string;
  oked: string;
  paymentType: string;
  phone: string;
  merchantId: string;
  terminalId: string;
};

export type ICasesListItemModel = {
  id: number;
  caseType: TCodeNameModel;
  createdDate: string;
  creator: TIdNameModel;
  customer: {
    id: number;
    name: string;
    pinfl: string | null;
    tin: string;
  };
  status: TCodeNameModel;
  data: TCaseData;
};

export type ICaseDetailsModel = {
  id: number;
  caseType: TCodeNameModel;
  createdDate: string;
  creator: TIdNameModel;
  customer: {
    id: number;
    name: string;
    pinfl: string | null;
    tin: string;
  };
  status: TCodeNameModel;
  data: TCaseData;
};

export type TCaseData = Partial<{
  agreementId: number;
  agreementNumber: string;
  caseId: number;
  currentDate: string;
  customerBranchDeviceId: number;
  customerId: number;
  deviceId: number;
  deviceSerialNumber: string;
  fmSerialNumber: string;
  lossFiscalModule: boolean;
  reason: TCodeNameModel;
  step: number;
  templateType: null;
  terminal: boolean;
  accountNumber: string;
  branchName: string;
  customerBranchId: number;
  bankId: number;
  bankName: string;
  mfo: string;
  bankType: TCodeNameModel;
  oked: string;
  phone: string;
  paymentType: TCodeNameModel;
  secretKey: string;
  deviceType: TCodeNameModel;
  changeCtsType: TCodeNameModel;
  deviceSerialNumbers: string[];
  address: any;
  rent: any;
  merchantId: string;
  terminalId: string;
}>;

export interface ICaseStatusHistoriesListItemModel {
  id: number;
  status: TCodeNameModel;
  duration: number | null;
  createdDate: string;
  modifiedDate: string;
  creator: TIdNameModel;
  modifier: TIdNameModel;
}
