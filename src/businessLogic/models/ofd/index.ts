export interface ICheckCompanyByTinModel {
  id: null;
  tin: null | string;
  pinfl: null | string;
  regionCode: number;
  regionName: string;
  districtCode: number;
  districtName: string;
  name: string;
  nameFull: null;
  address: string;
  subjectType: null;
  regionId: number;
  districtId: number;
  balance: number;
  businessType: null;
}

export type TConfirmCustomerFiscalParams = {
  customerId: number;
  customerBranchId: number;
  deviceSerial: string;
  fiscalModuleSerial?: string;
};
