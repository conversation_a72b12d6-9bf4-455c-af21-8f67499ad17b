import { TListQueryParams } from "#businessLogic/models/common";

export type TCaseNoteByCaseListParams = {
  caseId: number;
  search?: string;
  from?: string;
  to?: string;
} & TListQueryParams;

export interface ICaseNoteByCaseListItemModel {
  id: number;
  creatorId: number;
  creatorFullName: string;
  lastModifierId: number;
  lastModifierFullName: string;
  createdDate: string;
  message: string;
  messageType: string;
}

export type TCaseNoteCreateParams = {
  message: string;
  caseId: number;
  taggedEmployeeIds: Array<number>;
};

export type TCaseNoteUploadFileParams = {
  file: FormData;
  caseId: number;
};

export interface ICaseNoteEmployeeItemModel {
  id: number;
  name: string;
  login: string;
}
