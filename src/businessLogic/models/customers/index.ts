import { TCodeNameModel, TIdNameModel, TListQueryParams } from "#businessLogic/models/common";

export type TCustomersListParams = {
  search?: string;
  from?: string;
  to?: string;
} & TListQueryParams;

export type TCustomersListAdditionalParams = {};

export type ICustomersListItemModel = {
  id: number;
  name: string;
  tin: string;
  pinfl: string;
  companyName: string;
  activityType: TIdNameModel;
  businessType: TCodeNameModel;
  branch: TIdNameModel;
  phone: string;
  creator: TIdNameModel;
  createdDate: string;
  address: {
    region: TIdNameModel;
    district: TIdNameModel;
    neighborhood: TIdNameModel | null;
  } | null;
  source: TCodeNameModel;
  status: TCodeNameModel;
};

export type ICustomerDetailsModel = {
  id: number;
  name: string;
  tin: string;
  pinfl: string;
  companyName: string;
  activityType: TIdNameModel | null;
  businessType: TCodeNameModel;
  phone: string;
  creator: TIdNameModel;
  createdDate: string;
  oked: string | null;
  address: {
    region: TIdNameModel;
    district: TIdNameModel;
    neighborhood: TIdNameModel | null;
  } | null;
  status: TCodeNameModel;
};

export type TCreateCustomerParams = {
  tin: string;
  companyName: string;
  director: {
    firstName: string;
    lastName: string;
    patronymic: string;
  };
  oked: string;
  activityTypeId: number;
  businessType: string;
  address: {
    regionId: number;
    districtId: number;
    neighborhoodId: number;
    street: string;
    house: string;
    apartment?: string;
  };
  vatId: number;
  branches: Array<number>;
  sourceId: number;
};

export type TCustomersPublicOfferSendParams = {
  customerId: number;
};

export type TCustomersPublicOfferVerifyParams = {
  customerId: number;
  code: string;
};

export type TCustomersVerifyParams = {
  id: number;
  activationCode: string;
};
