import { TListQueryParams } from "#businessLogic/models/common";

export interface IPublicOfferModel {
  id: number;
  publicOfferContent: string;
}

export interface IBillingGroupedServiceItemModel {
  id: number;
  individual: boolean;
  displayInCabinet: boolean;
  title: string;
  xfileTitle: string;
  description: string;
  pricingModel: {
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
    code: string;
    selectable: boolean;
  };
  serviceType: string;
  category: {
    code: string;
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
    selectable: boolean;
  };
  fee: number;
  calculatedFee: number;
  bonusAmount: null;
  trialInterval: null;
  trialCount: null;
  tiers: Array<{
    id: number;
    firstUnitCount: null;
    lastUnitCount: number;
    perUnitPrice: null;
    flatFee: number;
    bcvInstance: null;
  }>;
  recurring: boolean;
  recurringModel: {
    id: number;
    interval: {
      code: string;
      nameRu: string;
      nameUzCyrillic: string;
      nameUzLatin: string;
      selectable: boolean;
    };
    intervalCount: number;
    recurring: boolean;
    repeatCount: number;
  } | null;
  calculatePer: null;
  sortOrder: null;
  disabled: boolean;
  appType: string;
  code: string;
  catalogCode: string;
  catalogName: string;
  packageName: string;
  packageCode: string;
  lgotaId: number;
  lgotaType: number;
  lgotaName: string;
  productGroup: {
    id: string;
    name: string;
  };
  organizationType: null;
  enableForSingle: boolean;
  origin: {
    code: string;
    id: 3;
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
  };
  bonusType: {
    code: string;
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
  };
  bcv: boolean;
  bcvInstance: number;
  freeUser: null;
  tariffType: null;
  vatId: string;
}

export interface IBillingGroupedServiceChildrenItemModel {
  id: number;
  title: string;
}

export interface IBillingSingleServiceItemModel {
  id: number;
  individual: boolean;
  displayInCabinet: boolean;
  title: string;
  xfileTitle: string;
  description: string;
  pricingModel: {
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
    code: string;
    selectable: boolean;
  };
  serviceType: string;
  category: {
    code: string;
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
    selectable: boolean;
  };
  fee: number;
  calculatedFee: number;
  bonusAmount: null;
  trialInterval: null;
  trialCount: null;
  tiers: Array<{
    id: number;
    firstUnitCount: null;
    lastUnitCount: number;
    perUnitPrice: null;
    flatFee: number;
    bcvInstance: null;
  }>;
  recurring: boolean;
  recurringModel: null;
  calculatePer: null;
  sortOrder: null;
  disabled: boolean;
  appType: string;
  code: string;
  catalogCode: string;
  catalogName: string;
  packageName: string;
  packageCode: string;
  lgotaId: number;
  lgotaType: number;
  lgotaName: string;
  productGroup: {
    id: string;
    name: string;
  };
  organizationType: null;
  enableForSingle: boolean;
  origin: {
    code: string;
    id: number;
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
  };
  bonusType: {
    code: string;
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
  };
  bcv: boolean;
  bcvInstance: number;
  freeUser: null;
  tariffType: null;
  vatId: null;
}

export type TAgreementCreateParams = {
  inn: string;
  serviceType: string;
  instances: number | null;
  xizmatId: number;
  firstName: string;
  lastName: string;
  smartposCreatorId: number;
};

export interface ICompanyBalanceByTinItemModel {
  companyType: {
    shortName: string;
    companyName: string;
  };
  balance: number;
  tin: string;
  customerName: string;
  customerId: number;
}

export type TBillingServicesTransactionsParams = {
  companyType: string;
  tin: string;
};

export interface IBillingServicesTransactionsItemModel {
  id: string;
  description: string;
  amount: number;
  debitOrCredit: {
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
    code: string;
  } | null;
  ledgerId: string;
  createdDate: string;
  noteFromExternalSystem: null;
  appType: {
    code: string;
    nameRu: string;
    companyType: {
      shortName: string;
      companyName: string;
    };
  };
}

export type TBillingServicesAgreementsListParamsType = TListQueryParams & {
  branchId?: number;
  cityId?: number;
  companyId?: number;
  ctoId?: number;
  customerId?: number;
  from?: string;
  search?: string;
  terminalId?: number;
  to?: string;
  userId?: number;
  serviceType?: string;
  status?: string;
  xizmatId?: number;
  recurring?: boolean;
  inn?: string;
};

export type TBillingServicesAgreementsListAdditionalParamsType = {
  companyName?: string;
};

export interface IBillingServicesAgreementsListItemModel {
  agreementNumber: string;
  agreements: IBillingServicesAgreementsListAgreementsModel[];
  attachments: IBillingServicesAgreementsListAttachmentsModel[];
  branches: IBillingServicesAgreementsListBranchesModel[];
  cancelOnPeriodEnd: boolean;
  customer: IBillingServicesAgreementsListCustomerModel;
  description: string;
  discount: IBillingServicesAgreementsListDiscountModel;
  firstActivatedDate: string;
  firstName: string;
  id: number;
  instances: number;
  lastName: string;
  lastPeriodEnd: string;
  lastPeriodStart: string;
  leftRecurrence: number;
  login: string;
  nextPeriodStart: string;
  patronymic: string;
  quoteId: string;
  serviceType: IBillingServicesAgreementsListServiceTypeModel;
  smartposCreatorId: string;
  status: IBillingServicesAgreementsListStatusModel;
  total: number;
  trial: boolean;
  xizmat: IBillingServicesAgreementsListXizmatModel;
}

export interface IBillingServicesAgreementsListAgreementsStatusModel {
  code: string;
  nameRu: string;
  nameUzCyrillic: string;
  nameUzLatin: string;
  selectable: boolean;
}

export interface IBillingServicesAgreementsListAgreementsModel {
  agreementNumber: string;
  availability: number;
  operatorId: string;
  operatorName: string;
  status: IBillingServicesAgreementsListAgreementsStatusModel;
}

export interface IBillingServicesAgreementsListAttachmentsModel {
  contentType: string;
  fileName: string;
  fileSize: number;
  id: number;
  name: string;
  path: string;
}

export interface IBillingServicesAgreementsListBranchesModel {
  branchId: number;
  branchName: string;
}

export interface IBillingServicesAgreementsListCustomerModel {
  ctoId: number;
  id: number;
  inn: string;
  pinfl: string;
  name: string;
  terminalCount: number;
}

export interface IBillingServicesAgreementsListDiscountModel {
  active: boolean;
  amount: number;
  id: number;
  name: string;
  percentage: number;
}

export interface IBillingServicesAgreementsListServiceTypeModel {
  code: string;
  nameRu: string;
  nameUzCyrillic: string;
  nameUzLatin: string;
  selectable: boolean;
}

export interface IBillingServicesAgreementsListStatusModel {
  code: string;
  nameRu: string;
  nameUzCyrillic: string;
  nameUzLatin: string;
  selectable: boolean;
}

export interface IBillingServicesAgreementsListXizmatModel {
  id: number;
  title: string;
  recurring: boolean;
}

export type TAgreementActivateParams = {
  agreementId: number;
};
