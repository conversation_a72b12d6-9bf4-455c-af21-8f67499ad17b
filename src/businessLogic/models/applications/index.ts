import { TCodeNameModel, TListQueryParams } from "#businessLogic/models/common";

export type TApplicationsListParams = {
  search?: string;
  from?: string;
  to?: string;
} & TListQueryParams;

export type TApplicationsListAdditionalParams = {

};

export type IApplicationsListItemModel = {
  id: number;
  tin: string,
  device: {
    id: number;
    serialNumber: string;
  };
  status: TCodeNameModel;
};
