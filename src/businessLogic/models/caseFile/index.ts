import { TCodeNameModel } from "#businessLogic/models/common";

export interface ICaseFileByCaseListItemModel {
  id: number;
  creatorId: number;
  creatorFullName: string;
  lastModifierId: number;
  lastModifierFullName: string;
  createdDate: string;
  lastModifiedDate: string;
  fileUrl: string;
  documentType: TCodeNameModel;
  description: string;
}

export type TCaseUploadParams = {
  caseId: number;
  documentType: string;
  file: FormData;
};
