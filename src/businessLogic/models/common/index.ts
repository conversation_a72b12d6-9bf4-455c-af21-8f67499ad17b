import { SortOrderFromAntMap } from "#types/common";

export interface PaginationListModel<T> {
  content: Array<T>;
  empty: boolean;
  first: boolean;
  last: boolean;
  number: number;
  numberOfElements: number;
  size: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  totalElements: number;
  totalPages: number;
}

export type TCustomerModel = {
  id: number;
  name: string;
  tin: string | null;
  pinfl: string | null;
};

export type TCodeNameModel<T = string> = {
  code: T;
  name: string;
};

export type TIdNameModel = {
  id: number;
  name: string;
};

export type TIdCodeNameModel<T = string> = {
  id: number;
  code: T;
  name: string;
};

export interface IDeviceItemModel {
  id: number;
  serialNumber: string;
}

export type TListQueryParams = {
  page?: number;
  size?: number;
  orderBy?: string;
  sortOrder?: SortOrderFromAntMap;
};

export type TRecordStore<T> = { [key: string | number]: T };

export type TDefaultLookupQueryParams = {
  search?: string;
};

export type TStatusChangeParams = {
  id: number;
  status: string;
};

export type TNamesAndCode<T = string> = {
  code: T;
  nameRu: string;
  nameUzCyrillic: string;
  nameUzLatin: string;
  selectable: boolean;
};
