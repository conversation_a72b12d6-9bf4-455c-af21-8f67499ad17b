import { TIdNameModel } from "#businessLogic/models/common";
import { SortOrderFromAntMap } from "#types/common";

export type TEmployeeLookupParams = {
  role?: "PROMO_MANAGER" | string; // need to fix
  roles?: string[]; // need to fix
  id?: number;
  customerId?: number;
  customerBranchId?: number;
  branchId?: number;
  userId?: number;
  employeeId?: number;
  managerId?: number;
  creatorId?: number;
  positionId?: number;
  departmentId?: number;
  deviceId?: number;
  modelId?: number;
  fiscalModuleId?: number;
  companyId?: number;
  categoryId?: number;
  status?: string; // need to fix
  regionId?: number;
  districtId?: number;
  neighborhoodId?: number;
  parentId?: number;
  from?: string;
  to?: string;
  tin?: string;
  page?: number;
  size?: number;
  search?: string;
  orderBy?: string;
  sortOrder?: SortOrderFromAntMap;
};

export type IEmployeeLookupItemModel = TIdNameModel;
