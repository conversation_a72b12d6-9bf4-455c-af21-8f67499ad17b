export type TReferenceTypesLookupParams = {
  search?: string;
  type: TReferenceTypes;
};

export interface IReferenceTypeItemModel {
  id: number;
  name: string;
  code: string;
}

export enum EnumReferenceTypes {
  PHONE_TYPE = "PHONE_TYPE",
}

export type TReferenceTypes =
  | EnumReferenceTypes.PHONE_TYPE
  | "RELATIONSHIP_TYPE"
  | "CHANGE_MANAGER_REASON_TYPE"
  | "DEACTIVATE_DEVICE_REASON_TYPE"
  | "CUSTOMER_SOURCE"
  | "REPAIR_TICKET_TYPE"
  | "REPAIR_TICKET_EQUIPMENTS"
  | "LETTER_REASON"
  | "LETTER_TYPE";
