import { TListQueryParams, TNamesAndCode } from "#businessLogic/models/common";

export enum ENUM_AGREEMENT_STATUSES {
  CREATED = "CREATED",
  TRIAL_ACTIVE = "TRIAL_ACTIVE",
  QUOTE_ACCEPTED = "QUOTE_ACCEPTED",
  PENDING_PAYMENT = "PENDING_PAYMENT",
  FORCE_STOPPED = "FORCE_STOPPED",
  ACCEPTED = "ACCEPTED",
  ACTIVE = "ACTIVE",
  PAUSE = "PAUSE",
  INACTIVE = "INACTIVE",
  BALANCE_NOT_ENOUGH = "BALANCE_NOT_ENOUGH",
}

export enum ENUM_QUOTE_STATUSES {
  ACCEPTED = "ACCEPTED",
  WAITING_FOR_DOCUMENT_ATTACHMENT = "WAITING_FOR_DOCUMENT_ATTACHMENT",
}

type TAgreementListCustomer = {
  id: number;
  name: string;
  inn: string;
  ctoId: number;
  customerOKED: string;
  accountNumber: string;
  region: string;
  district: string;
  street: string;
  house: string;
  apartment: string;
  phone: string;
  pinfl?: unknown;
  terminalCount?: unknown;
};

type TAgreementListXizmat = {
  id: number;
  title: string;
  xfileTitle?: unknown;
  description?: unknown;
  pricingModel: TNamesAndCode;
  serviceType: string;
  fee: number;
  bonusAmount?: unknown;
  bonusPercentage?: unknown;
  trialInterval?: unknown;
  trialCount?: unknown;
  discounts: unknown[];
  tiers: unknown[];
  recurring: true;
  recurringModel: {
    id: number;
    intervalCount: number;
    repeatCount: number;
    interval: TNamesAndCode;
  };
  calculatePer: TNamesAndCode;
  sortOrder: number;
  disabled: false;
  activeCount?: unknown;
  appType: string;
  code: string;
  catalogCode: string;
  catalogName: string;
  packageName: string;
  packageCode: string;
  lgotaId: number;
  lgotaType: number;
  lgotaName: string;
  productGroup?: unknown;
  enableForSingle: true;
};

export interface IAgreementModel {
  id: number;
  agreementNumber: string;
  serviceType: TNamesAndCode;
  firstActivatedDate?: unknown;
  lastPeriodStart?: unknown;
  lastPeriodEnd?: unknown;
  cancelOnPeriodEnd?: unknown;
  status: TNamesAndCode<ENUM_AGREEMENT_STATUSES>;
  instances: number;
  trial: false;
  customer: TAgreementListCustomer;
  description?: unknown;
  noteFromExternalSystem?: unknown;
  discount?: unknown;
  xizmat: TAgreementListXizmat;
  agreementItems?: unknown;
  agreements: IAgreementModel[];
  branches?: unknown;
  attachments: unknown[];
  leftRecurrence?: unknown;
  total: number;
  firstName?: unknown;
  lastName?: unknown;
  patronymic?: unknown;
  login?: unknown;
  smartposCreatorId?: unknown;
  nextPeriodStart?: unknown;
}

export interface IQuoteDetailsModel {
  id: string;
  createdDate: string;
  fileUri?: string;
  fileName?: string;
  contentType?: string;
  totalProductCount: number;
  totalPriceWithVat: number;
  totalPrice: number;
  totalVatPrice: number;
  documentNumber: string;
  status: Omit<TNamesAndCode<ENUM_QUOTE_STATUSES>, "selectable">;
  customerName: string;
  customerId: string;
  tin: string;
  customerOKED: string;
  customerAddress: string;
  publicOfferNumber: string;
  publicOfferId: string;
  publicOfferCreateDate: string;
}

export interface IUploadQuoteFileModel {
  id: string;
  file: FormData;
}

export interface IGetAgreementInvoicesModel {
  agreementId: number;
  params: TListQueryParams;
}

export interface IAgreementInvoicesModel {
  id: string;
  documentNumber: string;
  customerPublicOfferNumber: string;
  totalPriceWithVat: number;
  invoiceDate: string;
  status: {
    code: string;
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
  } | null;
}

export interface IAgreementQuoteListItemModel {
  id: string;
  createdDate: string;
  fileUri: null;
  fileName: null;
  contentType: null;
  totalProductCount: number;
  totalPriceWithVat: number;
  totalPrice: number;
  totalVatPrice: number;
  documentNumber: string;
  status: {
    code: string;
    nameRu: string;
    nameUzCyrillic: string;
    nameUzLatin: string;
  };
  customerName: string;
  customerId: string;
  tin: string;
  customerOKED: string;
  customerAddress: string;
  publicOfferNumber: string;
  publicOfferId: string;
  publicOfferCreateDate: string;
}

export type TAgreementQuoteCreateParams = {
  agreementId: number;
};

export type TAgreementActivateParams = {
  agreementId: number;
};
