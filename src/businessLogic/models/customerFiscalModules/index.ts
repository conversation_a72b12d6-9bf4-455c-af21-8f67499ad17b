export type TCreateCustomerFiscalModuleParams = {
  customerId: number;
  customerBranchId: number;
  serialNumber: string;
  fromRegistry: boolean;
};

export type TFiscalModuleLookupParams = {
  search?: string;
  customerId?: number;
  fromRegistry?: boolean;
};

export type TCustomerFiscalModuleLookupParams = {
  search?: string;
  customerId?: number;
  customerBranchId?: number;
};

export type TFiscalizationParams = {
  customerId: number;
  customerBranchId: number;
  deviceModelId: number;
  deviceSerial: string;
  fiscalModuleSerial: string;
};

export interface TCustomerBalance {
  data: {
    overpaymentSum: number;
    terminalCost: number;
    unpaidCount: number;
  };
}
