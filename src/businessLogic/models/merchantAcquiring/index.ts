import { TCodeNameModel, T<PERSON>ustomerModel, TListQueryParams } from "#businessLogic/models/common";

export type TMerchantAcquiringListParams = {
  search?: string;
  operatorStatus?: string;
  status?: string;
  from?: string;
  to?: string;
} & TListQueryParams;

export type TMerchantAcquiringListAdditionalParams = {};

export type IMerchantAcquiringListItemModel = {
  id: number;
  deviceSerialNumber: string;
  accountNumber: string;
  mfo: string;
  customer: TCustomerModel;
  status: TCodeNameModel;
  operatorStatus: TCodeNameModel;
};

export type TMerchantAcquiringTurnoverParams = {
  tin: string;
  fromDate?: string;
  toDate?: string;
  deviceSerial: string;
};
