import { TCodeNameModel, TId<PERSON>ame<PERSON>odel, TListQueryParams } from "#businessLogic/models/common";

export type TCustomerDevicesListParams = {
  search?: string;
  from?: string;
  to?: string;
  customerId?: number;
} & TListQueryParams;

export type TCustomerDevicesListAdditionalParams = {};

export interface ICustomerDevicesListItemModel {
  id: number;
  fiscalizationId: number;
  customerBranchDeviceId: number;
  device: {
    id: number;
    serialNumber: string;
  };
  fiscalModule: {
    id: number;
    serialNumber: string;
  };
  customerBranch: TIdNameModel;
  deviceStatus: TCodeNameModel;
  paymentType: TCodeNameModel;
  deviceType: TCodeNameModel;
  createdDate: string;
  sync: boolean;
  applicationFile: {
    id: number;
    name: string;
    url: string;
  };
  ofdFile: {
    id: number;
    name: string | null;
    url: string;
  };
  ofdApplication: {
    id: number;
    type: {
      code: string;
      name: string;
      id: number;
    };
    status: {
      code: string;
      name: string;
      id: number;
    };
  };
  paymentStatuses: TCodeNameModel[];
}

export type TCreateCustomerDeviceParams = {
  customerId: number;
  appType: string;
  devicePaymentType: string;
  deviceType: string;
  customerBranchId: number;
  devices: Array<number>;
};

export interface ICustomerDeviceLookupItemModel {
  customerBranchDeviceId: number;
  fiscalModuleSerial: string | null;
  customerBranch: TIdNameModel;
  device: {
    id: number;
    serialNumber: string;
  };
  deviceType: TCodeNameModel;
}

export type TCustomerDevicesLookupParams = {
  search?: string;
  type?: "PHYSICAL";
  customerId?: number;
};
