import React from "react";

export const AuthLoginInputIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9 3C7.34315 3 6 4.34315 6 6C6 7.65685 7.34315 9 9 9C10.6569 9 12 7.65685 12 6C12 4.34315 10.6569 3 9 3ZM4.5 6C4.5 3.51472 6.51472 1.5 9 1.5C11.4853 1.5 13.5 3.51472 13.5 6C13.5 8.48528 11.4853 10.5 9 10.5C6.51472 10.5 4.5 8.48528 4.5 6ZM6 13.5C4.75736 13.5 3.75 14.5074 3.75 15.75C3.75 16.1642 3.41421 16.5 3 16.5C2.58579 16.5 2.25 16.1642 2.25 15.75C2.25 13.6789 3.92893 12 6 12H12C14.0711 12 15.75 13.6789 15.75 15.75C15.75 16.1642 15.4142 16.5 15 16.5C14.5858 16.5 14.25 16.1642 14.25 15.75C14.25 14.5074 13.2426 13.5 12 13.5H6Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const AuthPasswordInputIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9 3C10.2358 3 11.25 4.01421 11.25 5.25V7.5H6.75V5.25C6.75 4.01421 7.76421 3 9 3ZM12.75 7.5V5.25C12.75 3.18579 11.0642 1.5 9 1.5C6.93579 1.5 5.25 3.18579 5.25 5.25V7.5H4.5C3.67157 7.5 3 8.17157 3 9V15C3 15.8284 3.67157 16.5 4.5 16.5H13.5C14.3284 16.5 15 15.8284 15 15V9C15 8.17157 14.3284 7.5 13.5 7.5H12.75ZM4.5 9H13.5V15H4.5V9Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const WarningIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9 9.83997V7.03497"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.99925 12.0938C8.89575 12.0938 8.81175 12.1778 8.8125 12.2812C8.8125 12.3847 8.8965 12.4688 9 12.4688C9.1035 12.4688 9.1875 12.3847 9.1875 12.2812C9.1875 12.1778 9.1035 12.0938 8.99925 12.0938"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.5218 3.10427L16.266 13.1573C16.9335 14.3258 16.0898 15.78 14.7443 15.78H3.25576C1.90951 15.78 1.06576 14.3258 1.73401 13.1573L7.47826 3.10427C8.15101 1.92602 9.84901 1.92602 10.5218 3.10427Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ArrowRightIcon = () => {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g>
        <path
          d="M11.0834 7.00004H2.91675"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.16675 9.91667L11.0834 7"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.16675 4.08337L11.0834 7.00004"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export const ArrowDownIcon = () => {
  return (
    <svg width="10" height="11" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2.5 4.7L5.16667 7.36667L7.83333 4.7"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ArrowLeftIcon = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 12L6 8L10 4" stroke="currentColor" strokeWidth="1.33" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export const EyeIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.33856 9.35025C2.22081 9.132 2.22081 8.86725 2.33856 8.649C3.75756 6.02475 6.37881 3.75 9.00006 3.75C11.6213 3.75 14.2426 6.02475 15.6616 8.64975C15.7793 8.868 15.7793 9.13275 15.6616 9.351C14.2426 11.9753 11.6213 14.25 9.00006 14.25C6.37881 14.25 3.75756 11.9753 2.33856 9.35025Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.591 7.40901C11.4697 8.28769 11.4697 9.71231 10.591 10.591C9.71231 11.4697 8.28769 11.4697 7.40901 10.591C6.53033 9.71231 6.53033 8.28769 7.40901 7.40901C8.28769 6.53033 9.71231 6.53033 10.591 7.40901"
        stroke="currentColor"
        strokeWidth="1.4286"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ClosedEyeIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8.99995 14.2501C8.36845 14.2501 7.7362 14.1166 7.12195 13.8788"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.6615 9.35107C14.2425 11.9753 11.6213 14.2501 9 14.2501"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.3092 6.69092C14.8275 7.29767 15.288 7.95917 15.6615 8.64992C15.7792 8.86817 15.7792 9.13292 15.6615 9.35117"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.75 14.25L14.25 3.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.32969 10.6703C6.40719 9.74783 6.40719 8.25158 7.32969 7.32908C8.25219 6.40658 9.74844 6.40658 10.6709 7.32908"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.7831 5.217C11.6228 4.31925 10.3111 3.75 9.00006 3.75C6.37881 3.75 3.75756 6.02475 2.33856 8.64975C2.22081 8.868 2.22081 9.13275 2.33856 9.351C3.04806 10.6628 4.05756 11.8868 5.21706 12.7838"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SearchIcon = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10.4759 4.05879C12.1098 5.69275 12.1098 8.34191 10.4759 9.97586C8.84191 11.6098 6.19275 11.6098 4.55879 9.97586C2.92484 8.34191 2.92484 5.69275 4.55879 4.05879C6.19275 2.42484 8.84191 2.42484 10.4759 4.05879"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.6667 12.1667L10.4733 9.97333"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CloseIcon = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M11.332 3.90137C11.3415 3.90152 11.3512 3.90152 11.3604 3.9043L11.3594 3.90625C11.3775 3.91178 11.3899 3.91899 11.3936 3.92188C11.3971 3.92471 11.3995 3.92783 11.4004 3.92871L11.4023 3.92969L12.0703 4.59766H12.0684C12.0723 4.60151 12.0779 4.60538 12.082 4.61133L12.0947 4.6377L12.0967 4.64453V4.66992H12.0996C12.0996 4.67622 12.0977 4.68194 12.0967 4.6875C12.0962 4.68996 12.0973 4.69288 12.0967 4.69531L12.0947 4.69434C12.091 4.709 12.0869 4.72141 12.0811 4.72949C12.0774 4.73447 12.0736 4.73782 12.0723 4.73926C12.0715 4.74008 12.0707 4.74087 12.0703 4.74121V4.74219L8.81152 8L12.0703 11.2578L12.084 11.2754L12.0947 11.2988L12.0957 11.3008L12.0996 11.3301C12.0996 11.3402 12.0987 11.3505 12.0957 11.3604L12.0928 11.3594C12.0872 11.3774 12.081 11.39 12.0781 11.3936C12.0753 11.3971 12.0722 11.3995 12.0713 11.4004L12.0703 11.4023L11.4023 12.0703L11.4014 12.0693C11.3986 12.0721 11.3962 12.0763 11.3926 12.0791C11.3839 12.0858 11.3753 12.0897 11.375 12.0898L11.3682 12.0928L11.3604 12.0957C11.341 12.1016 11.3201 12.1016 11.3008 12.0957V12.0947C11.2883 12.0909 11.2777 12.0863 11.2705 12.0811C11.2655 12.0774 11.2622 12.0736 11.2607 12.0723C11.2599 12.0715 11.2591 12.0707 11.2588 12.0703H11.2578L8 8.81152L4.74219 12.0703C4.73942 12.0731 4.7332 12.0785 4.72461 12.084C4.71471 12.0903 4.70514 12.0934 4.70117 12.0947L4.69922 12.0957C4.68936 12.0987 4.67914 12.0987 4.66895 12.0986V12.0996L4.66699 12.0986C4.65785 12.0984 4.64849 12.0984 4.63965 12.0957V12.0928C4.62217 12.0872 4.60999 12.081 4.60645 12.0781C4.60291 12.0753 4.60053 12.0722 4.59961 12.0713L4.59766 12.0703L3.92969 11.4023L3.93066 11.4014C3.92785 11.3986 3.92372 11.3962 3.9209 11.3926C3.91422 11.3839 3.9103 11.3753 3.91016 11.375L3.90723 11.3682L3.9043 11.3604C3.89841 11.3411 3.89842 11.3201 3.9043 11.3008H3.90527C3.90911 11.2883 3.9137 11.2777 3.91895 11.2705C3.92257 11.2655 3.92639 11.2622 3.92773 11.2607C3.92852 11.2599 3.92934 11.2591 3.92969 11.2588V11.2578L7.1875 8L3.92969 4.74219C3.92691 4.73941 3.92153 4.73319 3.91602 4.72461C3.90973 4.71473 3.90658 4.7051 3.90527 4.70117L3.9043 4.69922C3.89842 4.67994 3.89842 4.65892 3.9043 4.63965H3.90723C3.91278 4.62216 3.91905 4.60999 3.92188 4.60645C3.9247 4.60291 3.92783 4.60052 3.92871 4.59961L3.92969 4.59766L4.59766 3.92969V3.93066C4.60057 3.9277 4.60358 3.92389 4.60742 3.9209C4.6154 3.91473 4.62276 3.91078 4.62402 3.91016L4.63965 3.9043L4.66992 3.90039C4.67978 3.90042 4.68968 3.90139 4.69922 3.9043L4.69824 3.90527C4.71118 3.90911 4.7221 3.91358 4.72949 3.91895C4.73447 3.92257 4.73783 3.92639 4.73926 3.92773C4.74008 3.92851 4.74087 3.92935 4.74121 3.92969H4.74219L8 7.1875L11.2578 3.92969L11.2754 3.91602C11.2853 3.90972 11.2949 3.90658 11.2988 3.90527L11.3008 3.9043L11.3301 3.90039L11.3311 3.90137V3.90039L11.332 3.90137Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.2"
      />
    </svg>
  );
};

export const MenuIcon = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.998 11.6682C7.81459 11.6682 7.66452 11.8182 7.66652 12.0017C7.66652 12.1851 7.81659 12.3351 8 12.3351C8.18341 12.3351 8.33347 12.1851 8.33347 12.0017C8.33347 11.8182 8.18341 11.6682 7.998 11.6682"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.998 7.66653C7.81459 7.66653 7.66452 7.8166 7.66652 8.00001C7.66652 8.18342 7.81659 8.33348 8 8.33348C8.18341 8.33348 8.33347 8.18342 8.33347 8.00001C8.33347 7.8166 8.18341 7.66653 7.998 7.66653"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.998 3.66486C7.81459 3.66486 7.66452 3.81492 7.66652 3.99833C7.66652 4.18174 7.81659 4.3318 8 4.3318C8.18341 4.3318 8.33347 4.18174 8.33347 3.99833C8.33347 3.81492 8.18341 3.66486 7.998 3.66486"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DoneIcon = () => (
  <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.377808" width="20" height="20" rx="9.06667" fill="currentColor" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.247 6.53619L8.2803 12.2945L6.69696 10.6029C6.4053 10.3279 5.94696 10.3112 5.61363 10.5445C5.28863 10.7862 5.19696 11.2112 5.39696 11.5529L7.27196 14.6029C7.4553 14.8862 7.77196 15.0612 8.1303 15.0612C8.47196 15.0612 8.79696 14.8862 8.9803 14.6029C9.2803 14.2112 15.0053 7.38619 15.0053 7.38619C15.7553 6.61953 14.847 5.94453 14.247 6.52786V6.53619Z"
      fill="white"
    />
  </svg>
);

export const AttachmentIcon = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.3033 5.03C14.4541 3.87916 16.3191 3.87916 17.47 5.03V5.03C18.6208 6.18083 18.6208 8.04583 17.47 9.19666L12.5708 14.0958C11.42 15.2467 9.55497 15.2467 8.40414 14.0958V14.0958C7.25331 12.945 7.25331 11.08 8.40414 9.92916L9.13664 9.19666"
        stroke="#BFBFBF"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.69678 15.8033C5.54595 16.9542 3.68095 16.9542 2.53012 15.8033V15.8033C1.37928 14.6525 1.37928 12.7875 2.53012 11.6367L7.42928 6.73748C8.58012 5.58665 10.4451 5.58665 11.596 6.73748V6.73748C12.7468 7.88832 12.7468 9.75332 11.596 10.9042L10.8335 11.6667"
        stroke="#BFBFBF"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PrinterIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2.78348 9.2087H1.71205C1.42789 9.2087 1.15537 9.08328 0.954439 8.86002C0.753507 8.63676 0.640625 8.33396 0.640625 8.01823V5.04204C0.640625 4.7263 0.753507 4.4235 0.954439 4.20025C1.15537 3.97699 1.42789 3.85156 1.71205 3.85156H10.2835C10.5676 3.85156 10.8402 3.97699 11.0411 4.20025C11.242 4.4235 11.3549 4.7263 11.3549 5.04204V8.01823C11.3549 8.33396 11.242 8.63676 11.0411 8.86002C10.8402 9.08328 10.5676 9.2087 10.2835 9.2087H9.21205"
        stroke="currentColor"
        strokeWidth="0.7"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.20982 7.07031H3.78125C3.22897 7.07031 2.78125 7.51803 2.78125 8.07031V10.356C2.78125 10.9083 3.22896 11.356 3.78125 11.356H8.20982C8.76211 11.356 9.20982 10.9083 9.20982 10.356V8.07031C9.20982 7.51803 8.76211 7.07031 8.20982 7.07031Z"
        stroke="currentColor"
        strokeWidth="0.7"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.78125 3.85491V1.64062C2.78125 1.08834 3.22897 0.640625 3.78125 0.640625H8.20982C8.76211 0.640625 9.20982 1.08834 9.20982 1.64063V3.85491"
        stroke="currentColor"
        strokeWidth="0.7"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DownloadIcon = () => {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10.0238 6.37903C9.98924 6.30238 9.93321 6.23737 9.86251 6.19182C9.79181 6.14627 9.70945 6.12213 9.62535 6.1223H7.87544V0.437869C7.87544 0.321875 7.82942 0.210621 7.74748 0.128527C7.66553 0.0464332 7.55436 0.000208622 7.43837 0L5.68846 0C5.57233 0 5.46096 0.0461325 5.37884 0.128249C5.29672 0.210365 5.2506 0.321739 5.2506 0.437869V6.12545H3.50069C3.41662 6.12533 3.3343 6.14946 3.2636 6.19494C3.1929 6.24042 3.13682 6.30533 3.10207 6.38189C3.06733 6.45844 3.05539 6.54339 3.0677 6.62656C3.08002 6.70972 3.11606 6.78757 3.1715 6.85077L6.23422 10.3506C6.27517 10.3977 6.32576 10.4354 6.38254 10.4613C6.43933 10.4871 6.50101 10.5005 6.56341 10.5005C6.62581 10.5005 6.6875 10.4871 6.74429 10.4613C6.80107 10.4354 6.85164 10.3977 6.8926 10.3506L9.95453 6.85155C10.0108 6.78805 10.0473 6.70954 10.0596 6.62562C10.0719 6.54169 10.0595 6.45601 10.0238 6.37903Z"
        fill="currentColor"
      />
      <path
        d="M11.3759 9.62518V12.25H1.7499V9.62518H0V13.125C0 13.357 0.0921827 13.5796 0.256268 13.7437C0.420353 13.9077 0.6429 13.9999 0.874951 13.9999H12.2509C12.3658 14 12.4796 13.9775 12.5858 13.9336C12.692 13.8896 12.7885 13.8252 12.8698 13.7439C12.9511 13.6626 13.0155 13.5661 13.0595 13.4599C13.1034 13.3537 13.1259 13.2399 13.1258 13.125V9.62518H11.3759Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const UploadIcon = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5.99986 4.53594H7.3195V10.577C7.3195 10.6556 7.38379 10.7199 7.46236 10.7199H8.53379C8.61236 10.7199 8.67665 10.6556 8.67665 10.577V4.53594H9.99986C10.1195 4.53594 10.1856 4.39844 10.1124 4.30558L8.11236 1.77344C8.099 1.75637 8.08192 1.74256 8.06243 1.73306C8.04294 1.72356 8.02154 1.71863 7.99986 1.71863C7.97818 1.71863 7.95678 1.72356 7.93729 1.73306C7.9178 1.74256 7.90072 1.75637 7.88736 1.77344L5.88736 4.3038C5.81415 4.39844 5.88022 4.53594 5.99986 4.53594ZM14.5356 10.0413H13.4641C13.3856 10.0413 13.3213 10.1056 13.3213 10.1842V12.9342H2.67843V10.1842C2.67843 10.1056 2.61415 10.0413 2.53557 10.0413H1.46415C1.38557 10.0413 1.32129 10.1056 1.32129 10.1842V13.7199C1.32129 14.0359 1.57665 14.2913 1.89272 14.2913H14.107C14.4231 14.2913 14.6784 14.0359 14.6784 13.7199V10.1842C14.6784 10.1056 14.6141 10.0413 14.5356 10.0413Z"
        fill="currentColor"
      />
    </svg>
  );
};
