export type ErrorResponse = {
  detail?: string;
  title: string;
  status: number;
  message?: string;
};

export interface ResponseType<P = any, R = any> {
  result: {
    data: R;
    headers?: any;
    status: number;
    statusText: string;
  };
  params: P;
}

export type FetchDataModel<D> = {
  loading: boolean;
  data: D;
  error: ErrorResponse | null;
};

export type SuccessDataModel = {
  loading: boolean;
  success: boolean;
  error: ErrorResponse | null;
};
