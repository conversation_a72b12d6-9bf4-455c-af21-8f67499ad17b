@import "src/styles/variables.scss";

body {
  font-family: "Inter", sans-serif, Arial;
  font-weight: 400;
  font-size: 14px;
  background-color: #1b43bc;
}

#root {
  height: 100%;
}

.w-s-n {
  white-space: nowrap;
}

.t-a-r {
  text-align: right;
}

.t-a-l {
  text-align: left;
}

.t-a-c {
  text-align: center;
}

.m-b-10 {
  margin-bottom: 10px;
}

.danger-color {
  color: $danger;
}

.full-width {
  width: 100% !important;
  min-width: 0 !important;
}

.abs-loader {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
}

.main-loader {
  @extend .abs-loader;
  background: none;

  .ant-spin-dot-item {
    background: #fff;
  }
}

.site-main-wrapper {
  height: 100%;

  .site-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("../assets/images/auth-bg.jpg");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: -1;

    &:after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: repeat url("../assets/images/auth-pattern.png") 0 0;
      opacity: 0.5;
    }
  }
}


.u-fancy-scrollbar::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.u-fancy-scrollbar::-webkit-scrollbar-button:end:increment,
.u-fancy-scrollbar::-webkit-scrollbar-button:start:decrement {
  background: transparent;
  display: none;
}

.u-fancy-scrollbar::-webkit-scrollbar-track-piece {
  background: rgba(9, 30, 66, 0.08);
}

.u-fancy-scrollbar::-webkit-scrollbar-track-piece:vertical:start {
  border-radius: 4px 4px 0 0;
}

.u-fancy-scrollbar::-webkit-scrollbar-track-piece:vertical:end {
  border-radius: 0 0 4px 4px;
}

.u-fancy-scrollbar::-webkit-scrollbar-track-piece:horizontal:start {
  border-radius: 4px 0 0 4px;
}

.u-fancy-scrollbar::-webkit-scrollbar-track-piece:horizontal:end {
  border-radius: 0 4px 4px 0;
}

.u-fancy-scrollbar::-webkit-scrollbar-thumb:horizontal,
.u-fancy-scrollbar::-webkit-scrollbar-thumb:vertical {
  background: rgba($gray03, 0.1);
  border-radius: 4px;
  display: block;
  height: 48px;
}

.ant-select-dropdown {
  padding: 4px;
  border-radius: 6px;
  border: 1px solid $gray01;
  box-shadow: none;

  .ant-select-item {
    border-radius: 4px;
    min-height: 30px;
    padding: 4px 10px;

    &.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
      background-color: $gray02;
    }
  }
}

.ant-form-item-has-error {
  .ant-input-number.custom-input,
  .custom-calendar-range-picker .ant-picker {
    border-color: $danger;
  }

  .ant-input {
    border-color: $danger;

    &:focus {
      box-shadow: 0 0 0 2px rgba($danger, 0.2);
    }
  }
}

.ant-form-vertical .ant-form-item-label {
  line-height: 13px;
}

.ant-form-item-explain, .ant-form-item-extra {
  font-size: 11px;
}

/* Modal */

.ant-modal-wrap,
.ant-modal-mask {
  z-index: 1050;
}

.ant-notification,
.ant-message {
  z-index: 1051;
}

.ant-notification {
  margin-right: 30px;
}

/* Popover */
.ant-popover {
  z-index: 1050;
}

/* Checkbox start */

.ant-checkbox-wrapper {
  align-items: flex-start;
}

.ant-checkbox {
  top: 0;
}

.ant-checkbox .ant-checkbox-inner {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.02);
}

.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: $gray01;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover .ant-checkbox-inner {
  border-color: $primary;
}

.ant-checkbox-checked .ant-checkbox-inner {
  border-color: $gray01;
}

.ant-checkbox-checked::after {
  border-color: transparent;
}



/* Checkbox end */

iframe {
  border: none;
}

.table-balance {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  min-height: 30px;
  min-width: 130px;

  &.isRequested {
    .hiddenImg {
      display: none;
    }

    .button {
      display: block;
    }
  }

  .table-balance__button {
    display: none;
    max-width: 130px;
  }

  &:hover {
    .hiddenImg {
      display: none;
    }

    .table-balance__button {
      display: block;
    }
  }
}