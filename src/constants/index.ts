import { i18n } from "#localization/i18n";

export const UZKASSA_TIN = "306351564";

export const SMS_CODE_SIZE = 6;

export const DOCUMENT_TYPES = {
  CADASTRE: "CADASTRE",
  LEASE_CONTRACT: "LEASE_CONTRACT",
  APPLICATION: "APPLICATION",
  ATTACHMENT: "ATTACHMENT",
};

export const VALIDATE_MESSAGES = {
  required: i18n.t("requiredField"),
};

export const CURRENCIES = {
  UZS: i18n.t("UZS"),
};

export const DATE_FORMAT = "DD.MM.YY HH:mm";
export const RANGE_DATE_FORMAT = "YYYY-MM-DD";
export const RANGE_DATE_TIME_FORMAT = "YYYY-MM-DDTHH:mm";
export const DASH_DATE_FORMAT_WITH_TIME = "YYYY-MM-DD, HH:MM";

export const requiredRules = [{ required: true }];

export const ENTITY_TYPES = {
  CUSTOMER: "CUSTOMER",
  FISCALIZATION: "FISCALIZATION",
  CASES: "CASES",
};
