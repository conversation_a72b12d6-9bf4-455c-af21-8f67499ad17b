include:
  - project: 'frontend/frontend-ci-templates' 
    ref:     'main'
    file:    
      - '/templates/.ci-templates.yml'
      - '/templates/.web-cdti-partners.yml'

stages:
  - build
  - deploy
  
dev-docker-build:
  stage: build
  extends:
    - .default
    - .dev
    - .docker-login
    - .dev-docker-build

dev-k8s-deploy:
  stage: deploy
  extends:
    - .default
    - .dev
    - .helm-login
    - .dev-k8s-deploy
  needs:
    - job: dev-docker-build
      artifacts: false

prod-docker-build:
  stage: build
  extends:
    - .default
    - .prod
    - .docker-login
    - .prod-docker-build

prod-k8s-deploy:
  stage: deploy
  extends:
    - .default
    - .prod
    - .helm-login
    - .prod-k8s-deploy
    - .after_script_template
  needs:
    - job: prod-docker-build
      artifacts: false