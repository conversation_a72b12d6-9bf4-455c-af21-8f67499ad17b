{"printWidth": 120, "tabWidth": 2, "trailingComma": "all", "singleQuote": false, "jsxBracketSameLine": false, "semi": true, "importOrder": ["^(react)$", "<THIRD_PARTY_MODULES>", "^@businessLogic/(.*)$", "^@stores/(.*)$", "^@types/(.*)$", "^@core/(.*)$", "^@constructors/(.*)$", "^@styles/(.*)$", "^@pickers/(.*)$", "^@ui/(.*)$", "^@components/(.*)$", "^@utils/(.*)$", "^@hooks/(.*)$", "^@constants/(.*)$", "^@svgIcons/(.*)$", "^@assets/(.*)$", "^../(.*)", "^./(.*)", "(.scss)$"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "endOfLine": "auto"}