FROM node:22.14.0-alpine AS builder

RUN echo "Node.js version:" && node -v

WORKDIR /app

COPY .npmrc .
COPY package*.json ./
RUN npm ci

ARG BUILD_ENV="buildDev"

COPY . .
RUN npm run $BUILD_ENV

FROM nginx:alpine

RUN apk add --no-cache gettext

COPY ./ops/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

COPY ./ops/default.conf.template /etc/nginx/templates/default.conf.template

COPY --from=builder /app/build /usr/share/nginx/html

ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
