replicaCount: 1

nameOverride: ""
fullnameOverride: ""

namespace: partners

image:
  repository: artifactory.cdti.internal:5001/frontend/web-cdti-partners
  tag: 
  pullPolicy: Always

imagePullSecrets:
  - name: artifactory-registry-secret

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rewrite-target: "/"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
    nginx.ingress.kubernetes.io/proxy-real-ip-cidr: "0.0.0.0/0"
    nginx.ingress.kubernetes.io/real-ip-header: "X-Forwarded-For"
  hosts:
    - host: dev-partner.cdti.ai
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - dev-partner.cdti.ai
      secretName: cdtiai-tls

env:
  API_URL: ""

env:
  VAULT_TOKEN: ""

resources:
  requests:
    cpu: "10m"
    memory: "64Mi"
  limits:
    cpu: "500m"
    memory: "512Mi"

livenessProbe:
  httpGet:
    path: /health
    port: http
  initialDelaySeconds: 15
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5

hpa:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilization: 80
  targetMemoryUtilization: 70