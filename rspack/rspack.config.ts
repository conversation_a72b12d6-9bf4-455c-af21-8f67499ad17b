import {
  CopyRspackPlugin,
  CssExtractRspackPlugin,
  HtmlRspackPlugin,
  RspackPluginInstance,
  RuleSetRule,
  rspack,
} from "@rspack/core";
import { Configuration } from "@rspack/core/dist/config/types";
import path from "node:path";

import { modifyVars } from "../src/styles/antModifyVars";

function rsPackPlugins(isProdApp: boolean): Array<RspackPluginInstance> {
  const plugins: RspackPluginInstance[] = [
    new HtmlRspackPlugin({
      template: path.resolve(`${__dirname}/../`, "public", "index.html"),
      favicon: path.resolve(`${__dirname}/../`, "public", "favicon.ico"),
      templateParameters: {
        title: "CDTI Partners",
        buildTime: new Date().toISOString(),
      },
    }),
    new rspack.DefinePlugin({
      "process.env.isProdApp": JSON.stringify(isProdApp),
    }),
    new CopyRspackPlugin({
      patterns: [
        {
          from: path.resolve(`${__dirname}/../`, "public"),
          to: path.resolve(`${__dirname}/../`, "build"),
          globOptions: {
            ignore: ["**/*.html"],
          },
        },
      ],
    }),
  ];

  return plugins;
}

function rsPackLoaders(options: any): Array<RuleSetRule> {
  const { isDev } = options;

  const fontsRegex = /\.(woff|woff2)$/i;
  const imagesRegex = /\.(png|jpe?g|gif|webp)$/i;

  const imagesLoader = {
    test: imagesRegex,
    type: "asset/resource",
    generator: {
      filename: "assets/images/[name][ext]",
    },
  };

  const fontsLoader = {
    test: fontsRegex,
    type: "asset/resource",
    generator: {
      filename: "assets/fonts/[name][ext]",
    },
  };

  const getSwcLoader = (jsx = false) => ({
    exclude: /node_modules/,
    loader: "builtin:swc-loader",
    options: {
      jsc: {
        parser: {
          syntax: "typescript",
          tsx: jsx,
        },
        externalHelpers: true,
        transform: {
          react: {
            runtime: "automatic",
            development: isDev,
            refresh: isDev,
          },
        },
      },
      env: {
        targets: [
          ">0.5%",
          "last 2 versions",
          "not dead",
          "not IE 11",
          "not op_mini all"
        ],
      },
    },
  });

  const svgrLoader = [
    {
      test: /\.svg$/i,
      type: "asset",
    },
    {
      test: /\.svg$/i,
      issuer: /\.[jt]sx?$/,
      resourceQuery: /component/,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            dimensions: false,
          },
        },
      ],
    },
  ];

  const swcJsTsLoader = {
    test: /\.(j|t)s$/,
    ...getSwcLoader(),
  };

  const swcJsxTsxLoader = {
    test: /\.(j|t)sx$/,
    ...getSwcLoader(true),
  };

  const scssLoader = {
    exclude: /node_modules/,
    test: /\.s[ac]ss$/i,
    use: [
      isDev ? "style-loader" : CssExtractRspackPlugin.loader,
      {
        loader: "css-loader",
        options: {
          import: true,
          modules: {
            auto: (resPath: string) => Boolean(resPath.includes(".module.")),
            localIdentName: "[local]___[hash:base64:5]",
            namedExport: false,
          },
        },
      },
      "sass-loader",
    ],
  };

  const lessLoader = {
    test: /\.less$/,
    use: [
      isDev ? "style-loader" : CssExtractRspackPlugin.loader,
      {
        loader: "css-loader",
        options: {
          import: true,
          modules: {
            auto: (resPath: string) => Boolean(resPath.includes(".module.")),
            localIdentName: isDev
              ? "[name]___[hash:base64:5]"
              : "[hash:base64:5]",
            namedExport: false,
          },
        },
      },
      {
        loader: "less-loader",
        options: {
          lessOptions: {
            modifyVars: modifyVars,
            javascriptEnabled: true,
          },
        },
      }
    ]
  }

  return [
    swcJsTsLoader,
    swcJsxTsxLoader,
    ...svgrLoader,
    imagesLoader,
    fontsLoader,
    lessLoader,
    scssLoader,
  ];
}

export default ({ plugins, ...options }: any, isProdApp: any): Configuration => {
  const mode = options.mode || "development";

  const additionalParams = {
    isDev: mode === "development",
    isProdApp,
  };

  return {
    entry: path.resolve(`${__dirname}/../`, "src/index.tsx"),
    output: {
      path: path.resolve(`${__dirname}/../`, "build"),
      filename: "[name].[contenthash:8].js",
      chunkFilename: "chunk.[contenthash:8].js",
      clean: true,
      publicPath: "/",
    },
    mode,
    target: mode === "production" ? "browserslist" : "web",
    plugins: [
      //new BundleAnalyzerPlugin(),
      ...rsPackPlugins(isProdApp), ...plugins
    ],
    ...options,
    module: {
      rules: rsPackLoaders(additionalParams),
    },
    resolve: {
      extensions: [".ts", ".tsx", ".js", ".jsx", ".scss"],
      preferAbsolute: true,
      modules: [path.resolve(`${__dirname}/../`, "src"), "node_modules"],
      mainFiles: ["index"],
      alias: {
        "#businessLogic": path.resolve(__dirname, "../src/businessLogic/"),
        "#core": path.resolve(__dirname, "../src/core/"),
        "#assets": path.resolve(__dirname, "../src/assets/"),
        "#constructors": path.resolve(__dirname, "../src/constructors/"),
        "#constants": path.resolve(__dirname, "../src/constants/"),
        "#hooks": path.resolve(__dirname, "../src/hooks/"),
        "#styles": path.resolve(__dirname, "../src/styles/"),
        "#utils": path.resolve(__dirname, "../src/app/utils/"),
        "#stores": path.resolve(__dirname, "../src/app/stores/"),
        "#ui": path.resolve(__dirname, "../src/app/ui/"),
        "#pickers": path.resolve(__dirname, "../src/app/pickers/"),
        "#components": path.resolve(__dirname, "../src/app/components/"),
        "#types": path.resolve(__dirname, "../src/types/"),
        "#localization": path.resolve(__dirname, "../src/localization/"),
      },
    },
    optimization: {
      minimizer: [
        new rspack.SwcJsMinimizerRspackPlugin({
          minimizerOptions: {
            format: {
              comments: false,
            },
          },
        }),
        new rspack.LightningCssMinimizerRspackPlugin({
          minimizerOptions: {
            errorRecovery: false,
          },
        }),
      ],
      splitChunks: {
        chunks: "all", // разбивает и асинхронные, и синхронные импорты
        cacheGroups: {
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            name: "vendors",
            chunks: "all",
            priority: -10,
          },
          common: {
            minChunks: 2,
            name: "common",
            chunks: "all",
            priority: -20,
          },
        },
      },
    },
  };
};