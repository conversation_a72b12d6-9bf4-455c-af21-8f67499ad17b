import path from "node:path";
import baseConfig from "./rspack.config";
import ReactRefreshRspackPlugin from "@rspack/plugin-react-refresh";

export default (env: any) => {
  return baseConfig({
    mode: "development",
    devServer: {
      historyApiFallback: true,
      port: 3020,
      open: true,
      hot: true,
      compress: true,
      liveReload: false,
      client: {
        progress: true,
      },
      static: {
        directory: path.resolve(`${__dirname}/../`, "public"),
      },
      proxy: [
        {
          context: "/api",
          target: "https://dev-partner.cdti.ai",
          secure: false,
          changeOrigin: true,
        },
      ],
    },
    plugins: [
      new ReactRefreshRspackPlugin()
    ],
    devtool: "cheap-module-source-map",
  }, env.isProdApp);
};