declare module "*.webp";
declare module "*.png";
declare module "*.svg";
declare module "*.jpeg";
declare module "*.jpg";
declare module "*.scss";

declare module "*.svg?component" {
  import * as React from "react";
  const SVG: React.FC<React.SVGProps<SVGSVGElement>>;
  export default SVG;
}

declare module "*.svg?jsx" {
  import { type FC, type SVGProps } from "react";
  const SVG: FC<SVGProps<SVGElement> & { title?: string }>;
  export default SVG;
}